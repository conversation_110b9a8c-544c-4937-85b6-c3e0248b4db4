using System;
using System.Collections.Generic;
using UnityEngine;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Main inventory manager for the player. Handles multiple containers and inventory operations.
    /// </summary>
    public class PlayerInventory : MonoBehaviour
    {
        [Header("Inventory Settings")]
        [SerializeField] private int backpackSlots = 20;
        [SerializeField] private int hotbarSlots = 8;
        [SerializeField] private float maxCarryWeight = 50f;

        [Header("Pickup Behavior")]
        [SerializeField] private bool prioritizeBackpack = true; // If true, items go to backpack first
        
        [Header("Audio")]
        [SerializeField] private AudioSource audioSource;
        [SerializeField] private AudioClip itemPickupSound;
        [SerializeField] private AudioClip itemDropSound;
        [SerializeField] private AudioClip inventoryOpenSound;
        [SerializeField] private AudioClip inventoryCloseSound;
        
        // Containers
        private InventoryContainer backpack;
        private InventoryContainer hotbar;
        private InventoryContainer equipment;
        
        // State
        private bool isInventoryOpen = false;
        private int selectedHotbarSlot = 0;
        
        // Events
        public event Action<PlayerInventory> OnInventoryChanged;
        public event Action<bool> OnInventoryToggled;
        public event Action<int> OnHotbarSelectionChanged;
        public event Action<InventoryItem> OnItemPickedUp;
        public event Action<InventoryItem> OnItemDropped;
        public event Action<InventoryItem> OnItemUsed;
        
        // Properties
        public InventoryContainer Backpack => backpack;
        public InventoryContainer Hotbar => hotbar;
        public InventoryContainer Equipment => equipment;
        public bool IsInventoryOpen => isInventoryOpen;
        public int SelectedHotbarSlot => selectedHotbarSlot;
        public InventoryItem SelectedHotbarItem => hotbar.GetSlot(selectedHotbarSlot)?.Item;
        
        private void Awake()
        {
            InitializeContainers();
            SetupAudioSource();
        }
        
        private void InitializeContainers()
        {
            // Create main backpack
            backpack = new InventoryContainer("Backpack", backpackSlots, ContainerType.General, maxCarryWeight);
            backpack.OnContainerChanged += OnContainerChanged;
            
            // Create hotbar (quick access)
            hotbar = new InventoryContainer("Hotbar", hotbarSlots, ContainerType.Hotbar);
            hotbar.OnContainerChanged += OnContainerChanged;
            
            // Create equipment slots (helmet, armor, etc.)
            equipment = new InventoryContainer("Equipment", 6, ContainerType.Equipment);
            equipment.OnContainerChanged += OnContainerChanged;
        }
        
        private void SetupAudioSource()
        {
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
                audioSource.spatialBlend = 0f; // 2D sound
            }
        }
        
        private void OnContainerChanged(InventoryContainer container)
        {
            OnInventoryChanged?.Invoke(this);
        }
        
        /// <summary>
        /// Attempts to pick up an item
        /// </summary>
        public bool PickupItem(InventoryItem item)
        {
            if (item == null) 
            {
                Debug.LogWarning("PickupItem: item is null");
                return false;
            }
            
            if (item.ItemData == null)
            {
                Debug.LogWarning("PickupItem: item.ItemData is null");
                return false;
            }
            
            Debug.Log($"PickupItem: Attempting to pickup {item.ItemData.itemName} x{item.Quantity}");

            InventoryItem remainingItem;

            if (prioritizeBackpack)
            {
                // Try backpack first (better for survival games)
                remainingItem = backpack.AddItem(item);
                Debug.Log($"PickupItem: After backpack, remaining: {remainingItem?.Quantity ?? 0}");

                // If there's still remaining, try hotbar
                if (remainingItem != null)
                {
                    remainingItem = hotbar.AddItem(remainingItem);
                    Debug.Log($"PickupItem: After hotbar, remaining: {remainingItem?.Quantity ?? 0}");
                }
            }
            else
            {
                // Try hotbar first (for quick access items)
                remainingItem = hotbar.AddItem(item);
                Debug.Log($"PickupItem: After hotbar, remaining: {remainingItem?.Quantity ?? 0}");

                // If there's still remaining, try backpack
                if (remainingItem != null)
                {
                    remainingItem = backpack.AddItem(remainingItem);
                    Debug.Log($"PickupItem: After backpack, remaining: {remainingItem?.Quantity ?? 0}");
                }
            }
            
            // If we successfully added some or all of the item
            if (remainingItem == null || remainingItem.Quantity < item.Quantity)
            {
                PlaySound(itemPickupSound);
                OnItemPickedUp?.Invoke(item);
                return remainingItem == null; // Return true if all was picked up
            }
            
            return false; // Couldn't pick up anything
        }
        
        /// <summary>
        /// Drops an item from inventory into the world
        /// </summary>
        public bool DropItem(int containerIndex, int slotIndex, int quantity = -1)
        {
            InventoryContainer container = GetContainer(containerIndex);
            if (container == null) return false;
            
            var droppedItem = container.RemoveItem(slotIndex, quantity);
            if (droppedItem != null)
            {
                // TODO: Spawn item in world
                SpawnItemInWorld(droppedItem);
                
                PlaySound(itemDropSound);
                OnItemDropped?.Invoke(droppedItem);
                return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// Uses an item from the hotbar
        /// </summary>
        public bool UseSelectedItem()
        {
            var item = SelectedHotbarItem;
            if (item == null) return false;
            
            return UseItem(item, 1, selectedHotbarSlot);
        }
        
        /// <summary>
        /// Uses a specific item
        /// </summary>
        public bool UseItem(InventoryItem item, int quantity = 1, int hotbarSlotIndex = -1)
        {
            if (item == null || item.ItemData == null) return false;
            
            // Handle different item types
            switch (item.ItemData.itemType)
            {
                case ItemType.Consumable:
                    return UseConsumableItem(item, quantity, hotbarSlotIndex);
                case ItemType.Weapon:
                    return EquipWeapon(item);
                case ItemType.Equipment:
                    return EquipItem(item);
                default:
                    Debug.Log($"Item type {item.ItemData.itemType} cannot be used directly.");
                    return false;
            }
        }
        
        private bool UseConsumableItem(InventoryItem item, int quantity, int slotIndex)
        {
            if (!(item.ItemData is ConsumableItemData consumableData)) return false;
            
            // TODO: Apply consumable effects to player
            Debug.Log($"Used {item.ItemData.itemName}: +{consumableData.healthRestore} health, +{consumableData.hungerRestore} hunger");
            
            // Remove the consumed item
            if (slotIndex >= 0)
            {
                hotbar.RemoveItem(slotIndex, quantity);
            }
            
            OnItemUsed?.Invoke(item);
            return true;
        }
        
        private bool EquipWeapon(InventoryItem item)
        {
            // TODO: Implement weapon equipping
            Debug.Log($"Equipped weapon: {item.ItemData.itemName}");
            return true;
        }
        
        private bool EquipItem(InventoryItem item)
        {
            var equipmentManager = GetComponent<EquipmentManager>();
            if (equipmentManager != null)
            {
                return equipmentManager.EquipItem(item);
            }
            else
            {
                Debug.LogWarning("No EquipmentManager found - cannot equip items");
                return false;
            }
        }
        
        /// <summary>
        /// Toggles the inventory UI
        /// </summary>
        public void ToggleInventory()
        {
            isInventoryOpen = !isInventoryOpen;
            PlaySound(isInventoryOpen ? inventoryOpenSound : inventoryCloseSound);
            OnInventoryToggled?.Invoke(isInventoryOpen);
        }
        
        /// <summary>
        /// Selects a hotbar slot
        /// </summary>
        public void SelectHotbarSlot(int slotIndex)
        {
            if (slotIndex >= 0 && slotIndex < hotbarSlots)
            {
                selectedHotbarSlot = slotIndex;
                OnHotbarSelectionChanged?.Invoke(selectedHotbarSlot);
            }
        }
        
        /// <summary>
        /// Gets a container by index (0=backpack, 1=hotbar, 2=equipment)
        /// </summary>
        public InventoryContainer GetContainer(int index)
        {
            switch (index)
            {
                case 0: return backpack;
                case 1: return hotbar;
                case 2: return equipment;
                default: return null;
            }
        }
        
        /// <summary>
        /// Checks if player has a specific item
        /// </summary>
        public bool HasItem(string itemID, int quantity = 1)
        {
            int totalCount = backpack.GetItemCount(itemID) + hotbar.GetItemCount(itemID);
            return totalCount >= quantity;
        }
        
        /// <summary>
        /// Gets total count of an item across all containers
        /// </summary>
        public int GetItemCount(string itemID)
        {
            return backpack.GetItemCount(itemID) + hotbar.GetItemCount(itemID) + equipment.GetItemCount(itemID);
        }
        
        /// <summary>
        /// Gets current total weight
        /// </summary>
        public float GetCurrentWeight()
        {
            return backpack.GetCurrentWeight() + hotbar.GetCurrentWeight() + equipment.GetCurrentWeight();
        }
        
        private void SpawnItemInWorld(InventoryItem item)
        {
            // TODO: Implement world item spawning
            Debug.Log($"Dropped {item.ItemData.itemName} x{item.Quantity} in world");
        }
        
        private void PlaySound(AudioClip clip)
        {
            if (audioSource != null && clip != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        
        /// <summary>
        /// Saves inventory data to a serializable format
        /// </summary>
        public InventorySaveData GetSaveData()
        {
            // TODO: Implement save system
            return new InventorySaveData();
        }
        
        /// <summary>
        /// Loads inventory data from save
        /// </summary>
        public void LoadSaveData(InventorySaveData saveData)
        {
            // TODO: Implement load system
        }
    }
    
    [Serializable]
    public class InventorySaveData
    {
        // TODO: Implement save data structure
    }
}
