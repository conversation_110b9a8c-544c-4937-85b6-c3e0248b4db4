# Zombie Survival Inventory System

A complete, multiplayer-ready inventory system designed for zombie survival games.

## Features

- **Slot-based inventory** with backpack, hotbar, and equipment containers
- **Item stacking** and weight management
- **Multiple item types**: Consumables, Weapons, Materials, Equipment
- **World item pickups** with interaction system
- **Input integration** with keyboard and gamepad support
- **Multiplayer preparation** with network-ready data structures
- **Modular design** for easy customization and extension

## Quick Setup

### 1. Add to Player
Add the `PlayerInventoryIntegration` component to your player GameObject. It will automatically set up all required components.

### 2. Create Item Database
1. Right-click in Project window → Create → Inventory → Item Database
2. Place it in a `Resources` folder (create one if needed)
3. Name it "ItemDatabase"

### 3. Create Items
Create item data assets:
- Right-click → Create → Inventory → Item Data (basic items)
- Right-click → Create → Inventory → Consumable Item (food, medicine)
- Right-click → Create → Inventory → Weapon Item (weapons)
- Right-click → Create → Inventory → Material Item (crafting materials)

### 4. Add Items to Database
1. Select your ItemDatabase asset
2. Add your created items to the "All Items" list
3. Use "Validate Database" to check for issues

## Controls

- **Tab**: Toggle inventory
- **1-8**: Select hotbar slots
- **Mouse Wheel**: Scroll through hotbar
- **Q**: Drop selected item
- **Left Click**: Use selected item
- **E**: Interact/pickup items

## Architecture

### Core Classes

- **ItemData**: ScriptableObject base class for all item definitions
- **InventoryItem**: Runtime instance of an item with quantity, durability, etc.
- **InventorySlot**: Individual slot that can hold items
- **InventoryContainer**: Collection of slots (backpack, hotbar, equipment)
- **PlayerInventory**: Main inventory manager for the player

### Item Types

- **ConsumableItemData**: Food, medicine, drinks with stat restoration
- **WeaponItemData**: Melee and ranged weapons with damage stats
- **MaterialItemData**: Crafting materials and resources
- **ItemData**: Base class for other item types

### Systems

- **InteractionSystem**: Handles world item pickup and object interaction
- **InventoryInputController**: Manages all inventory-related input
- **WorldItem**: Represents items in the world that can be picked up

## Multiplayer Support

The system includes network-ready components:

- **NetworkInventoryData**: Serializable data structures for network sync
- **NetworkInventoryManager**: Handles client-server inventory synchronization
- **INetworkInventory**: Interface for network integration

### Network Integration

1. Add `NetworkInventoryManager` to player
2. Implement your network layer to handle `InventoryNetworkEvent`
3. Use `GetNetworkInventoryState()` and `ApplyNetworkInventoryState()` for sync

## Customization

### Adding New Item Types

1. Create a new class inheriting from `ItemData`
2. Add item-specific properties
3. Update `ItemType` enum if needed
4. Handle the new type in `PlayerInventory.UseItem()`

### Custom Containers

Create specialized containers by:
1. Using `SlotType.Restricted` with `allowedItemTypes`
2. Creating custom container types in `ContainerType` enum
3. Adding validation logic in `InventorySlot.CanAcceptItem()`

### UI Integration

The system provides events for UI integration:
- `OnInventoryChanged`: When any container changes
- `OnInventoryToggled`: When inventory opens/closes
- `OnHotbarSelectionChanged`: When hotbar selection changes
- `OnItemPickedUp/Dropped/Used`: For feedback and notifications

## Example Usage

```csharp
// Get player inventory
PlayerInventory inventory = GetComponent<PlayerInventory>();

// Check if player has an item
if (inventory.HasItem("bandage", 1))
{
    // Remove item
    inventory.GetContainer(0).RemoveItemByID("bandage", 1);
}

// Add item to inventory
var newItem = ItemDatabase.CreateItem("water_bottle", 2);
inventory.PickupItem(newItem);

// Create world item
Vector3 dropPosition = transform.position + Vector3.forward * 2f;
WorldItem.SpawnWorldItem(newItem, dropPosition);
```

## Performance Notes

- Items are referenced by ID strings for network efficiency
- ItemData assets are loaded once and cached
- Container operations are O(n) where n is slot count
- Network events are queued and processed per frame

## Equipment System

The inventory system includes a complete equipment system:

### Equipment Types
- **Head**: Helmets, hats, masks
- **Chest**: Armor, vests, jackets
- **Legs**: Pants, leg armor
- **Feet**: Boots, shoes
- **Hands**: Gloves
- **Back**: Backpacks, capes
- **MainHand**: Primary weapons
- **OffHand**: Secondary weapons, shields
- **Ring**: Small accessories
- **Necklace**: Jewelry, dog tags

### Stat System
Equipment can modify player stats:
- Health, Stamina, Speed, Strength
- Defense, Stealth, Carry Weight
- Accuracy, Critical Chance, Attack Speed
- Various resistances (Fire, Cold, Radiation, etc.)

### Equipment Features
- **Stat Modifiers**: Additive, multiplicative, or override
- **Armor Values**: Physical protection
- **Damage Reduction**: Percentage-based protection
- **Special Properties**: Night vision, stealth, etc.
- **Durability System**: Equipment degrades with use
- **Visual Representation**: 3D models when equipped

## UI System

Complete inventory UI with modern features:

### UI Components
- **InventoryUI**: Main UI controller
- **InventorySlotUI**: Individual inventory slots with drag-drop
- **EquipmentSlotUI**: Equipment slots with type restrictions
- **HotbarSlotUI**: Hotbar with selection and cooldowns

### UI Features
- **Drag & Drop**: Move items between containers
- **Visual Feedback**: Hover effects, selection highlighting
- **Item Icons**: Display item sprites and quantities
- **Stat Bars**: Health and stamina visualization
- **Weight Display**: Current/max carry weight
- **Tooltips**: Detailed item information (framework ready)

### UI Setup
Use `InventoryUISetup` script to automatically create UI:
1. Add script to any GameObject
2. Configure settings in inspector
3. Run "Setup Inventory UI" from context menu

## Future Enhancements

- Item tooltips with stat comparisons
- Crafting system integration
- Save/load system
- Item durability and repair
- Container locking and security
- Vendor/trading system
- Equipment visual representation
- Animation system integration

## Troubleshooting

### "ItemDatabase not found"
- Ensure ItemDatabase.asset is in a Resources folder
- Check the asset is named exactly "ItemDatabase"

### Items not stacking
- Verify items have same ID, condition, and durability
- Check `isStackable` is true on ItemData
- Ensure `maxStackSize` > 1

### Input not working
- Check InventoryInputController is enabled
- Verify input actions are not conflicting
- Ensure player has focus (cursor locked)

### Network sync issues
- Verify NetworkInventoryManager is on both client and server
- Check network events are being sent/received
- Ensure proper authority handling (server authoritative)
