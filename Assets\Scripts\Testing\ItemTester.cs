using UnityEngine;
using ZombieGame.Inventory;

namespace ZombieGame.Testing
{
    /// <summary>
    /// Simple script to test item pickup with placeholder objects
    /// </summary>
    public class ItemTester : MonoBehaviour
    {
        [Header("Test Settings")]
        [SerializeField] private bool spawnOnStart = true;
        [SerializeField] private Vector3 spawnOffset = new Vector3(3f, 1f, 0f);
        
        [Header("Test Items")]
        [SerializeField] private string[] testItemNames = { "Banana", "Apple", "Medkit", "Ammo" };
        
        private void Start()
        {
            if (spawnOnStart)
            {
                SpawnTestCube();
            }
        }
        
        [ContextMenu("Spawn Test Cube")]
        public void SpawnTestCube()
        {
            Vector3 spawnPosition = transform.position + spawnOffset;
            CreateTestCube(spawnPosition, "Banana");
        }
        
        [ContextMenu("Spawn Multiple Test Items")]
        public void SpawnMultipleTestItems()
        {
            for (int i = 0; i < testItemNames.Length; i++)
            {
                Vector3 spawnPosition = transform.position + new Vector3(i * 2f, 1f, 0f);
                CreateTestCube(spawnPosition, testItemNames[i]);
            }
        }
        
        private void CreateTestCube(Vector3 position, string itemName)
        {
            // Create a cube GameObject
            GameObject cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cube.name = $"TestItem_{itemName}";
            cube.transform.position = position;
            cube.transform.localScale = Vector3.one * 0.5f; // Smaller cube

            // Make sure it's on the default layer (layer 0) so raycast can hit it
            cube.layer = 0;
            
            // Add a bright color to make it visible
            Renderer renderer = cube.GetComponent<Renderer>();
            renderer.material.color = GetRandomColor();

            // Add a simple text label above the cube for debugging
            GameObject label = new GameObject("ItemLabel");
            label.transform.SetParent(cube.transform);
            label.transform.localPosition = Vector3.up * 1.5f;

            // Create 3D text (TextMesh) for the label
            TextMesh textMesh = label.AddComponent<TextMesh>();
            textMesh.text = itemName;
            textMesh.fontSize = 20;
            textMesh.color = Color.white;
            textMesh.anchor = TextAnchor.MiddleCenter;
            
            // Create test item data
            ConsumableItemData itemData = ScriptableObject.CreateInstance<ConsumableItemData>();
            itemData.itemName = itemName;
            itemData.itemID = itemName.ToLower();
            itemData.description = $"A test {itemName.ToLower()} for pickup testing.";
            itemData.healthRestore = Random.Range(10f, 50f);
            itemData.maxStackSize = Random.Range(1, 10);
            itemData.weight = 0.5f;
            
            // Create inventory item
            InventoryItem inventoryItem = new InventoryItem(itemData, Random.Range(1, 4));
            
            // Add WorldItem component
            WorldItem worldItem = cube.AddComponent<WorldItem>();
            worldItem.Initialize(inventoryItem);

            // Debug: Check if WorldItem was set up correctly
            Debug.Log($"WorldItem setup - CanBePickedUp: {worldItem.CanBePickedUp}, Item: {worldItem.Item?.ItemData?.itemName}");
            
            // Fix the floating issue by adjusting physics
            Rigidbody rb = cube.GetComponent<Rigidbody>();
            if (rb != null)
            {
                rb.mass = 2f; // Heavier so it doesn't float
                rb.linearDamping = 5f; // Air resistance
                rb.angularDamping = 5f; // Rotation resistance
            }
            
            Debug.Log($"Created test cube: {itemName} at {position}");
        }
        
        private Color GetRandomColor()
        {
            Color[] colors = {
                Color.red,
                Color.green,
                Color.blue,
                Color.yellow,
                Color.magenta,
                Color.cyan,
                new Color(1f, 0.5f, 0f), // Orange
                new Color(0.5f, 0f, 1f)  // Purple
            };
            
            return colors[Random.Range(0, colors.Length)];
        }
        
        [ContextMenu("Clear All Test Items")]
        public void ClearAllTestItems()
        {
            // Find all WorldItems in the scene
            WorldItem[] worldItems = FindObjectsByType<WorldItem>(FindObjectsSortMode.None);
            
            foreach (WorldItem item in worldItems)
            {
                if (item.name.StartsWith("TestItem_"))
                {
                    DestroyImmediate(item.gameObject);
                }
            }
            
            Debug.Log($"Cleared {worldItems.Length} test items");
        }
    }
}
