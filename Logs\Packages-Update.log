
=== Mon Jun 30 17:00:47 2025

Packages were changed.
Update Mode: mergeDefaultDependencies

The following packages were added:
  com.unity.modules.ai@1.0.0
  com.unity.modules.androidjni@1.0.0
  com.unity.modules.animation@1.0.0
  com.unity.modules.assetbundle@1.0.0
  com.unity.modules.audio@1.0.0
  com.unity.modules.cloth@1.0.0
  com.unity.modules.director@1.0.0
  com.unity.modules.imageconversion@1.0.0
  com.unity.modules.imgui@1.0.0
  com.unity.modules.jsonserialize@1.0.0
  com.unity.modules.particlesystem@1.0.0
  com.unity.modules.physics@1.0.0
  com.unity.modules.physics2d@1.0.0
  com.unity.modules.screencapture@1.0.0
  com.unity.modules.terrain@1.0.0
  com.unity.modules.terrainphysics@1.0.0
  com.unity.modules.tilemap@1.0.0
  com.unity.modules.ui@1.0.0
  com.unity.modules.uielements@1.0.0
  com.unity.modules.umbra@1.0.0
  com.unity.modules.unityanalytics@1.0.0
  com.unity.modules.unitywebrequest@1.0.0
  com.unity.modules.unitywebrequestassetbundle@1.0.0
  com.unity.modules.unitywebrequestaudio@1.0.0
  com.unity.modules.unitywebrequesttexture@1.0.0
  com.unity.modules.unitywebrequestwww@1.0.0
  com.unity.modules.vehicles@1.0.0
  com.unity.modules.video@1.0.0
  com.unity.modules.vr@1.0.0
  com.unity.modules.wind@1.0.0
  com.unity.modules.xr@1.0.0
  com.unity.modules.accessibility@1.0.0
  com.unity.multiplayer.center@1.0.0
The following packages were updated:
  com.unity.ai.navigation from version 2.0.0 to 2.0.7
  com.unity.collab-proxy from version 2.2.0 to 2.7.1
  com.unity.ide.rider from version 3.0.27 to 3.0.36
  com.unity.ide.visualstudio from version 2.0.22 to 2.0.23
  com.unity.inputsystem from version 1.12.0 to 1.14.0
  com.unity.render-pipelines.universal from version 17.0.1 to 17.1.0
  com.unity.test-framework from version 1.4.2 to 1.5.1
  com.unity.timeline from version 1.8.6 to 1.8.7
  com.unity.visualscripting from version 1.9.1 to 1.9.6

=== Tue Jul  1 01:33:46 2025

Packages were changed.
Update Mode: updateDependencies

The following packages were updated:
  com.unity.collab-proxy from version 1.2.16 to 2.7.1
  com.unity.ide.rider from version 1.1.4 to 3.0.36
  com.unity.test-framework from version 1.1.16 to 1.5.1
  com.unity.timeline from version 1.2.14 to 1.8.7
  com.unity.ugui from version 1.0.0 to 2.0.0
The following packages were removed:
  com.unity.textmeshpro@2.0.1

=== Tue Jul  1 01:33:46 2025

Packages were changed.
Update Mode: mergeDefaultDependencies

The following packages were added:
  com.unity.ide.vscode@1.2.1
