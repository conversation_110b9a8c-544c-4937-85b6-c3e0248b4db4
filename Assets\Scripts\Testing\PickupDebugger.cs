using UnityEngine;
using ZombieGame.Inventory;

namespace ZombieGame.Testing
{
    /// <summary>
    /// Simple debug script to test pickup detection
    /// </summary>
    public class PickupDebugger : MonoBehaviour
    {
        [Header("Debug Settings")]
        [SerializeField] private float debugRange = 5f;
        [SerializeField] private KeyCode debugKey = KeyCode.F;
        
        private void Update()
        {
            if (Input.GetKeyDown(debugKey))
            {
                DebugNearbyItems();
            }
        }
        
        private void DebugNearbyItems()
        {
            Debug.Log("=== PICKUP DEBUG ===");
            
            // Find all WorldItems in range
            WorldItem[] allWorldItems = FindObjectsByType<WorldItem>(FindObjectsSortMode.None);
            
            Debug.Log($"Total WorldItems in scene: {allWorldItems.Length}");
            
            foreach (WorldItem item in allWorldItems)
            {
                float distance = Vector3.Distance(transform.position, item.transform.position);
                Debug.Log($"WorldItem: {item.name} - Distance: {distance:F2}m - CanPickup: {item.CanBePickedUp}");
                
                if (distance <= debugRange)
                {
                    Debug.Log($"  -> WITHIN RANGE! Item: {item.Item?.ItemData?.itemName}");
                    
                    // Test direct raycast to this item
                    Vector3 direction = (item.transform.position - transform.position).normalized;
                    if (Physics.Raycast(transform.position, direction, out RaycastHit hit, debugRange))
                    {
                        Debug.Log($"  -> Raycast hit: {hit.collider.name}");
                        if (hit.collider.gameObject == item.gameObject)
                        {
                            Debug.Log($"  -> DIRECT HIT! This item should be pickupable!");
                        }
                    }
                }
            }
            
            // Test camera raycast
            Camera cam = Camera.main;
            if (cam != null)
            {
                Ray ray = cam.ScreenPointToRay(new Vector3(Screen.width / 2, Screen.height / 2, 0));
                if (Physics.Raycast(ray, out RaycastHit hit, debugRange))
                {
                    Debug.Log($"Camera raycast hit: {hit.collider.name} at {hit.distance:F2}m");
                    WorldItem worldItem = hit.collider.GetComponent<WorldItem>();
                    if (worldItem != null)
                    {
                        Debug.Log($"  -> Found WorldItem: {worldItem.Item?.ItemData?.itemName}");
                    }
                }
                else
                {
                    Debug.Log("Camera raycast hit nothing");
                }
            }
            else
            {
                Debug.LogError("No Camera.main found!");
            }
            
            Debug.Log("=== END DEBUG ===");
        }
    }
}
