using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;
using ZombieGame.Inventory;

namespace ZombieGame.UI
{
    /// <summary>
    /// UI component for hotbar slots
    /// </summary>
    public class HotbarSlotUI : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointer<PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerEnter<PERSON><PERSON>ler, IPointerExitHandler
    {
        [Header("UI Elements")]
        [SerializeField] private Image itemIcon;
        [SerializeField] private TextMeshProUGUI quantityText;
        [SerializeField] private TextMeshProUGUI keyText;
        [SerializeField] private Image backgroundImage;
        [SerializeField] private Image selectionBorder;
        [SerializeField] private Image cooldownOverlay;
        
        [Header("Visual Settings")]
        [SerializeField] private Color normalColor = Color.white;
        [SerializeField] private Color hoverColor = Color.yellow;
        [SerializeField] private Color selectedColor = Color.green;
        [SerializeField] private Color emptySlotColor = new Color(1f, 1f, 1f, 0.3f);
        [SerializeField] private Color cooldownColor = new Color(0f, 0f, 0f, 0.7f);
        
        [Header("Animation")]
        [SerializeField] private float selectionAnimationSpeed = 5f;
        [SerializeField] private float hoverScale = 1.1f;
        
        // References
        private InventoryContainer container;
        private int slotIndex;
        private int displayNumber;
        private InventorySlot inventorySlot;
        
        // State
        private bool isSelected = false;
        private bool isHovered = false;
        private float cooldownTimer = 0f;
        private float maxCooldown = 0f;
        
        // Animation
        private Vector3 originalScale;
        private Color originalBorderColor;
        
        public int SlotIndex => slotIndex;
        public int DisplayNumber => displayNumber;
        public bool IsSelected => isSelected;
        
        private void Awake()
        {
            SetupComponents();
            originalScale = transform.localScale;
        }
        
        private void Update()
        {
            UpdateCooldown();
            UpdateAnimations();
        }
        
        private void SetupComponents()
        {
            // Setup default UI elements if not assigned
            if (itemIcon == null)
            {
                itemIcon = transform.Find("ItemIcon")?.GetComponent<Image>();
            }
            
            if (quantityText == null)
            {
                quantityText = transform.Find("QuantityText")?.GetComponent<TextMeshProUGUI>();
            }
            
            if (keyText == null)
            {
                keyText = transform.Find("KeyText")?.GetComponent<TextMeshProUGUI>();
            }
            
            if (backgroundImage == null)
            {
                backgroundImage = GetComponent<Image>();
            }
            
            if (selectionBorder == null)
            {
                selectionBorder = transform.Find("SelectionBorder")?.GetComponent<Image>();
            }
            
            if (cooldownOverlay == null)
            {
                cooldownOverlay = transform.Find("CooldownOverlay")?.GetComponent<Image>();
            }
            
            // Store original border color
            if (selectionBorder != null)
            {
                originalBorderColor = selectionBorder.color;
            }
        }
        
        public void Initialize(InventoryContainer container, int slotIndex, int displayNumber)
        {
            this.container = container;
            this.slotIndex = slotIndex;
            this.displayNumber = displayNumber;
            this.inventorySlot = container.GetSlot(slotIndex);
            
            // Subscribe to slot changes
            if (inventorySlot != null)
            {
                inventorySlot.OnSlotChanged += OnSlotChanged;
            }
            
            // Set key text
            if (keyText != null)
            {
                keyText.text = displayNumber.ToString();
            }
            
            UpdateDisplay();
        }
        
        public void UpdateDisplay()
        {
            if (inventorySlot == null) return;
            
            var item = inventorySlot.Item;
            
            // Update item icon
            if (itemIcon != null)
            {
                if (item?.ItemData?.icon != null)
                {
                    itemIcon.sprite = item.ItemData.icon;
                    itemIcon.color = Color.white;
                    itemIcon.gameObject.SetActive(true);
                }
                else
                {
                    itemIcon.gameObject.SetActive(false);
                }
            }
            
            // Update quantity text
            if (quantityText != null)
            {
                if (item != null && item.Quantity > 1)
                {
                    quantityText.text = item.Quantity.ToString();
                    quantityText.gameObject.SetActive(true);
                }
                else
                {
                    quantityText.gameObject.SetActive(false);
                }
            }
            
            UpdateVisualState();
        }
        
        private void UpdateVisualState()
        {
            if (backgroundImage == null) return;
            
            Color targetColor = normalColor;
            
            if (isSelected)
            {
                targetColor = selectedColor;
            }
            else if (isHovered)
            {
                targetColor = hoverColor;
            }
            else if (inventorySlot.IsEmpty)
            {
                targetColor = emptySlotColor;
            }
            
            backgroundImage.color = targetColor;
            
            // Update selection border
            if (selectionBorder != null)
            {
                selectionBorder.gameObject.SetActive(isSelected);
                if (isSelected)
                {
                    selectionBorder.color = selectedColor;
                }
            }
        }
        
        private void UpdateCooldown()
        {
            if (cooldownTimer > 0f)
            {
                cooldownTimer -= Time.deltaTime;
                
                if (cooldownOverlay != null)
                {
                    float fillAmount = maxCooldown > 0 ? cooldownTimer / maxCooldown : 0f;
                    cooldownOverlay.fillAmount = fillAmount;
                    cooldownOverlay.gameObject.SetActive(fillAmount > 0f);
                }
                
                if (cooldownTimer <= 0f)
                {
                    cooldownTimer = 0f;
                    if (cooldownOverlay != null)
                    {
                        cooldownOverlay.gameObject.SetActive(false);
                    }
                }
            }
        }
        
        private void UpdateAnimations()
        {
            // Scale animation for hover effect
            Vector3 targetScale = isHovered ? originalScale * hoverScale : originalScale;
            transform.localScale = Vector3.Lerp(transform.localScale, targetScale, Time.deltaTime * selectionAnimationSpeed);
            
            // Selection border pulse animation
            if (selectionBorder != null && isSelected)
            {
                float pulse = Mathf.Sin(Time.time * selectionAnimationSpeed) * 0.3f + 0.7f;
                Color borderColor = originalBorderColor;
                borderColor.a = pulse;
                selectionBorder.color = borderColor;
            }
        }
        
        public void SetSelected(bool selected)
        {
            isSelected = selected;
            UpdateVisualState();
        }
        
        public void StartCooldown(float duration)
        {
            cooldownTimer = duration;
            maxCooldown = duration;
            
            if (cooldownOverlay != null)
            {
                cooldownOverlay.fillAmount = 1f;
                cooldownOverlay.color = cooldownColor;
                cooldownOverlay.gameObject.SetActive(true);
            }
        }
        
        public bool IsOnCooldown()
        {
            return cooldownTimer > 0f;
        }
        
        // Event handlers
        private void OnSlotChanged(InventorySlot slot)
        {
            UpdateDisplay();
        }
        
        // UI Event Handlers
        public void OnPointerClick(PointerEventData eventData)
        {
            if (eventData.button == PointerEventData.InputButton.Left)
            {
                HandleLeftClick();
            }
            else if (eventData.button == PointerEventData.InputButton.Right)
            {
                HandleRightClick();
            }
        }
        
        public void OnPointerEnter(PointerEventData eventData)
        {
            isHovered = true;
            UpdateVisualState();
            
            // Show tooltip
            if (inventorySlot?.Item != null)
            {
                ShowTooltip();
            }
        }
        
        public void OnPointerExit(PointerEventData eventData)
        {
            isHovered = false;
            UpdateVisualState();
            
            // Hide tooltip
            HideTooltip();
        }
        
        private void HandleLeftClick()
        {
            // Select this hotbar slot
            var playerInventory = FindFirstObjectByType<PlayerInventory>();
            if (playerInventory != null)
            {
                playerInventory.SelectHotbarSlot(slotIndex);
            }
        }
        
        private void HandleRightClick()
        {
            // Use item directly
            if (!inventorySlot.IsEmpty && !IsOnCooldown())
            {
                var playerInventory = FindFirstObjectByType<PlayerInventory>();
                if (playerInventory != null)
                {
                    // Select slot first, then use item
                    playerInventory.SelectHotbarSlot(slotIndex);
                    bool used = playerInventory.UseSelectedItem();
                    
                    if (used)
                    {
                        // Start cooldown if item was used
                        StartCooldown(1f); // Default 1 second cooldown
                    }
                }
            }
        }
        
        private void ShowTooltip()
        {
            // TODO: Implement tooltip system
            var item = inventorySlot.Item;
            if (item != null)
            {
                Debug.Log($"Hotbar tooltip: {item.ItemData.itemName} (Slot {displayNumber})");
            }
        }
        
        private void HideTooltip()
        {
            // TODO: Implement tooltip hiding
        }
        
        private void OnDestroy()
        {
            if (inventorySlot != null)
            {
                inventorySlot.OnSlotChanged -= OnSlotChanged;
            }
        }
    }
}
