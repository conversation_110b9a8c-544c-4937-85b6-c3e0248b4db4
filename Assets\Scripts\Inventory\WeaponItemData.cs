using UnityEngine;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Data for weapon items (melee and ranged)
    /// </summary>
    [CreateAssetMenu(fileName = "New Weapon", menuName = "Inventory/Weapon Item")]
    public class WeaponItemData : ItemData
    {
        [Header("Weapon Properties")]
        public WeaponType weaponType = WeaponType.Melee;
        
        [Range(1f, 200f)]
        public float damage = 25f;
        
        [Range(0.1f, 5f)]
        public float attackSpeed = 1f; // Attacks per second
        
        [Range(0.5f, 20f)]
        public float range = 2f; // Attack range in meters
        
        [Header("Durability")]
        public float maxDurability = 100f;
        public float durabilityLossPerUse = 1f;
        
        [Header("Ranged Weapon Settings")]
        [SerializeField] private string ammunitionType = ""; // Item ID for ammo
        [SerializeField] private int magazineSize = 30;
        [SerializeField] private float reloadTime = 2f;
        [SerializeField] private float accuracy = 0.9f; // 0-1 accuracy rating
        
        [Header("Melee Weapon Settings")]
        public bool canBlock = false;
        public float blockEfficiency = 0.5f; // Damage reduction when blocking
        
        [Header("Audio")]
        public AudioClip attackSound;
        public AudioClip reloadSound;
        public AudioClip emptySound;
        
        [Header("Weapon Model")]
        public GameObject weaponPrefab; // 3D model for when equipped
        
        // Properties for ranged weapons
        public string AmmunitionType => weaponType == WeaponType.Ranged ? ammunitionType : "";
        public int MagazineSize => weaponType == WeaponType.Ranged ? magazineSize : 0;
        public float ReloadTime => weaponType == WeaponType.Ranged ? reloadTime : 0f;
        public float Accuracy => weaponType == WeaponType.Ranged ? accuracy : 1f;
        
        private void Awake()
        {
            itemType = ItemType.Weapon;
            isStackable = false; // Weapons don't stack
            maxStackSize = 1;
        }
        
        private void OnValidate()
        {
            // Ensure weapon-specific settings are valid
            if (weaponType == WeaponType.Ranged)
            {
                if (string.IsNullOrEmpty(ammunitionType))
                {
                    Debug.LogWarning($"Ranged weapon '{name}' should have an ammunition type specified.");
                }
                if (magazineSize <= 0)
                {
                    magazineSize = 1;
                }
            }
            
            if (weaponType == WeaponType.Melee)
            {
                ammunitionType = "";
                magazineSize = 0;
                reloadTime = 0f;
                accuracy = 1f;
            }
        }
    }
    
    public enum WeaponType
    {
        Melee,
        Ranged
    }
}
