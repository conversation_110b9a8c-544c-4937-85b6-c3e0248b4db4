{"format": 1, "restore": {"F:\\Prototype Running Game\\Assembly-CSharp-Editor.csproj": {}}, "projects": {"F:\\Prototype Running Game\\Assembly-CSharp-Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Prototype Running Game\\Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "F:\\Prototype Running Game\\Assembly-CSharp-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Prototype Running Game\\Temp\\obj\\Assembly-CSharp-Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"F:\\Prototype Running Game\\Assembly-CSharp.csproj": {"projectPath": "F:\\Prototype Running Game\\Assembly-CSharp.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "F:\\Prototype Running Game\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Prototype Running Game\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "F:\\Prototype Running Game\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Prototype Running Game\\Temp\\obj\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}