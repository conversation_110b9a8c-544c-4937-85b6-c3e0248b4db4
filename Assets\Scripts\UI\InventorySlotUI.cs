using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;
using ZombieGame.Inventory;

namespace ZombieGame.UI
{
    /// <summary>
    /// UI component for individual inventory slots
    /// </summary>
    public class InventorySlotUI : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointer<PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerEnter<PERSON><PERSON><PERSON>, IPointerExitHandler, IBeginDragHandler, IDragHandler, IEndDragHandler, IDropHandler
    {
        [Header("UI Elements")]
        [SerializeField] private Image itemIcon;
        [SerializeField] private TextMeshProUGUI quantityText;
        [SerializeField] private Image backgroundImage;
        [SerializeField] private Image borderImage;
        [SerializeField] private GameObject lockIcon;
        
        [Header("Visual Settings")]
        [SerializeField] private Color normalColor = Color.white;
        [SerializeField] private Color hoverColor = Color.yellow;
        [SerializeField] private Color selectedColor = Color.green;
        [SerializeField] private Color lockedColor = Color.gray;
        [SerializeField] private Color emptySlotColor = new Color(1f, 1f, 1f, 0.3f);
        
        // References
        private InventoryContainer container;
        private int slotIndex;
        private InventorySlot inventorySlot;
        
        // Drag and drop
        private Canvas canvas;
        private GraphicRaycaster graphicRaycaster;
        private GameObject dragPreview;
        private bool isDragging = false;
        
        // State
        private bool isSelected = false;
        private bool isHovered = false;
        
        public InventorySlot InventorySlot => inventorySlot;
        public int SlotIndex => slotIndex;
        public InventoryContainer Container => container;
        
        private void Awake()
        {
            SetupComponents();
        }
        
        private void SetupComponents()
        {
            canvas = GetComponentInParent<Canvas>();
            graphicRaycaster = GetComponentInParent<GraphicRaycaster>();
            
            // Setup default UI elements if not assigned
            if (itemIcon == null)
            {
                itemIcon = transform.Find("ItemIcon")?.GetComponent<Image>();
            }
            
            if (quantityText == null)
            {
                quantityText = GetComponentInChildren<TextMeshProUGUI>();
            }
            
            if (backgroundImage == null)
            {
                backgroundImage = GetComponent<Image>();
            }
        }
        
        public void Initialize(InventoryContainer container, int slotIndex)
        {
            this.container = container;
            this.slotIndex = slotIndex;
            this.inventorySlot = container.GetSlot(slotIndex);
            
            // Subscribe to slot changes
            if (inventorySlot != null)
            {
                inventorySlot.OnSlotChanged += OnSlotChanged;
            }
            
            UpdateDisplay();
        }
        
        public void UpdateDisplay()
        {
            if (inventorySlot == null) return;
            
            var item = inventorySlot.Item;
            
            // Update item icon
            if (itemIcon != null)
            {
                if (item?.ItemData?.icon != null)
                {
                    itemIcon.sprite = item.ItemData.icon;
                    itemIcon.color = Color.white;
                    itemIcon.gameObject.SetActive(true);
                }
                else
                {
                    itemIcon.gameObject.SetActive(false);
                }
            }
            
            // Update quantity text
            if (quantityText != null)
            {
                if (item != null && item.Quantity > 1)
                {
                    quantityText.text = item.Quantity.ToString();
                    quantityText.gameObject.SetActive(true);
                }
                else
                {
                    quantityText.gameObject.SetActive(false);
                }
            }
            
            // Update background color
            UpdateVisualState();
            
            // Update lock icon
            if (lockIcon != null)
            {
                lockIcon.SetActive(inventorySlot.IsLocked);
            }
        }
        
        private void UpdateVisualState()
        {
            if (backgroundImage == null) return;
            
            Color targetColor = normalColor;
            
            if (inventorySlot.IsLocked)
            {
                targetColor = lockedColor;
            }
            else if (isSelected)
            {
                targetColor = selectedColor;
            }
            else if (isHovered)
            {
                targetColor = hoverColor;
            }
            else if (inventorySlot.IsEmpty)
            {
                targetColor = emptySlotColor;
            }
            
            backgroundImage.color = targetColor;
            
            // Update border if available
            if (borderImage != null)
            {
                borderImage.color = isSelected ? selectedColor : Color.clear;
            }
        }
        
        public void SetSelected(bool selected)
        {
            isSelected = selected;
            UpdateVisualState();
        }
        
        // Event handlers
        private void OnSlotChanged(InventorySlot slot)
        {
            UpdateDisplay();
        }
        
        // UI Event Handlers
        public void OnPointerClick(PointerEventData eventData)
        {
            if (inventorySlot.IsLocked) return;
            
            if (eventData.button == PointerEventData.InputButton.Left)
            {
                // Left click - select or use item
                HandleLeftClick();
            }
            else if (eventData.button == PointerEventData.InputButton.Right)
            {
                // Right click - context menu or quick action
                HandleRightClick();
            }
        }
        
        public void OnPointerEnter(PointerEventData eventData)
        {
            isHovered = true;
            UpdateVisualState();
            
            // Show tooltip
            if (inventorySlot?.Item != null)
            {
                ShowTooltip();
            }
        }
        
        public void OnPointerExit(PointerEventData eventData)
        {
            isHovered = false;
            UpdateVisualState();
            
            // Hide tooltip
            HideTooltip();
        }
        
        public void OnBeginDrag(PointerEventData eventData)
        {
            if (inventorySlot.IsLocked || inventorySlot.IsEmpty) return;
            
            isDragging = true;
            CreateDragPreview();
        }
        
        public void OnDrag(PointerEventData eventData)
        {
            if (!isDragging || dragPreview == null) return;
            
            // Move drag preview to mouse position
            Vector2 localPoint;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                canvas.transform as RectTransform,
                eventData.position,
                canvas.worldCamera,
                out localPoint
            );
            
            dragPreview.transform.localPosition = localPoint;
        }
        
        public void OnEndDrag(PointerEventData eventData)
        {
            if (!isDragging) return;
            
            isDragging = false;
            DestroyDragPreview();
        }
        
        public void OnDrop(PointerEventData eventData)
        {
            var draggedSlot = eventData.pointerDrag?.GetComponent<InventorySlotUI>();
            if (draggedSlot != null && draggedSlot != this)
            {
                // Handle item swap/move
                HandleItemDrop(draggedSlot);
            }
        }
        
        private void HandleLeftClick()
        {
            // TODO: Implement left click behavior (select, use, etc.)
            Debug.Log($"Left clicked slot {slotIndex}");
        }
        
        private void HandleRightClick()
        {
            // TODO: Implement right click behavior (context menu, quick use, etc.)
            Debug.Log($"Right clicked slot {slotIndex}");
        }
        
        private void HandleItemDrop(InventorySlotUI draggedSlot)
        {
            // Try to swap items between slots
            if (container == draggedSlot.Container)
            {
                // Same container - swap slots
                container.SwapSlots(draggedSlot.SlotIndex, slotIndex);
            }
            else
            {
                // Different containers - move item
                var draggedItem = draggedSlot.InventorySlot.RemoveItem();
                if (draggedItem != null)
                {
                    var remainingItem = inventorySlot.AddItem(draggedItem);
                    if (remainingItem != null)
                    {
                        // Couldn't add all items, put back what we can
                        draggedSlot.InventorySlot.AddItem(remainingItem);
                    }
                }
            }
        }
        
        private void CreateDragPreview()
        {
            if (inventorySlot?.Item?.ItemData?.icon == null) return;
            
            // Create a preview object
            GameObject previewObj = new GameObject("DragPreview");
            previewObj.transform.SetParent(canvas.transform, false);
            
            Image previewImage = previewObj.AddComponent<Image>();
            previewImage.sprite = inventorySlot.Item.ItemData.icon;
            previewImage.color = new Color(1f, 1f, 1f, 0.8f);
            previewImage.raycastTarget = false;
            
            // Set size
            RectTransform rectTransform = previewObj.GetComponent<RectTransform>();
            rectTransform.sizeDelta = (transform as RectTransform).sizeDelta * 0.8f;
            
            dragPreview = previewObj;
        }
        
        private void DestroyDragPreview()
        {
            if (dragPreview != null)
            {
                Destroy(dragPreview);
                dragPreview = null;
            }
        }
        
        private void ShowTooltip()
        {
            // TODO: Implement tooltip system
            Debug.Log($"Show tooltip for {inventorySlot.Item.ItemData.itemName}");
        }
        
        private void HideTooltip()
        {
            // TODO: Implement tooltip hiding
        }
        
        private void OnDestroy()
        {
            if (inventorySlot != null)
            {
                inventorySlot.OnSlotChanged -= OnSlotChanged;
            }
            
            DestroyDragPreview();
        }
    }
}
