{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 19704, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 19704, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 19704, "tid": 1894, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 19704, "tid": 1894, "ts": 1751309586157953, "dur": 22, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 19704, "tid": 1894, "ts": 1751309586158009, "dur": 21, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 19704, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 19704, "tid": 1, "ts": 1751309585904535, "dur": 2271, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19704, "tid": 1, "ts": 1751309585906811, "dur": 93143, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19704, "tid": 1, "ts": 1751309585999957, "dur": 118109, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 19704, "tid": 1894, "ts": 1751309586158035, "dur": 23, "ph": "X", "name": "", "args": {}}, {"pid": 19704, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585904501, "dur": 17364, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585921867, "dur": 234861, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585921883, "dur": 54, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585921943, "dur": 583, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585922537, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585922541, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585922624, "dur": 12, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585922638, "dur": 3349, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585925998, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926005, "dur": 169, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926181, "dur": 11, "ph": "X", "name": "ProcessMessages 2506", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926194, "dur": 84, "ph": "X", "name": "ReadAsync 2506", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926284, "dur": 4, "ph": "X", "name": "ProcessMessages 1228", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926292, "dur": 61, "ph": "X", "name": "ReadAsync 1228", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926357, "dur": 2, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926361, "dur": 86, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926451, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926454, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926527, "dur": 3, "ph": "X", "name": "ProcessMessages 882", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926531, "dur": 81, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926617, "dur": 7, "ph": "X", "name": "ProcessMessages 1333", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926626, "dur": 70, "ph": "X", "name": "ReadAsync 1333", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926699, "dur": 1, "ph": "X", "name": "ProcessMessages 1311", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926705, "dur": 68, "ph": "X", "name": "ReadAsync 1311", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926778, "dur": 2, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926781, "dur": 47, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926831, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926834, "dur": 57, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926894, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926897, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926951, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926956, "dur": 36, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926994, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585926999, "dur": 44, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927045, "dur": 9, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927056, "dur": 27, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927087, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927090, "dur": 56, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927151, "dur": 1, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927154, "dur": 42, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927198, "dur": 1, "ph": "X", "name": "ProcessMessages 1052", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927201, "dur": 34, "ph": "X", "name": "ReadAsync 1052", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927243, "dur": 49, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927294, "dur": 5, "ph": "X", "name": "ProcessMessages 1018", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927301, "dur": 35, "ph": "X", "name": "ReadAsync 1018", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927342, "dur": 5, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927348, "dur": 44, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927397, "dur": 1, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927403, "dur": 34, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927439, "dur": 1, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927450, "dur": 50, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927505, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927507, "dur": 57, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927568, "dur": 2, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927572, "dur": 61, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927637, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927642, "dur": 59, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927703, "dur": 2, "ph": "X", "name": "ProcessMessages 1488", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927709, "dur": 47, "ph": "X", "name": "ReadAsync 1488", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927759, "dur": 4, "ph": "X", "name": "ProcessMessages 887", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927765, "dur": 39, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927810, "dur": 4, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927818, "dur": 62, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927882, "dur": 1, "ph": "X", "name": "ProcessMessages 1385", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927885, "dur": 45, "ph": "X", "name": "ReadAsync 1385", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927934, "dur": 33, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585927971, "dur": 53, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928026, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928037, "dur": 42, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928085, "dur": 52, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928139, "dur": 3, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928155, "dur": 48, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928206, "dur": 1, "ph": "X", "name": "ProcessMessages 916", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928209, "dur": 49, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928259, "dur": 2, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928263, "dur": 51, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928316, "dur": 2, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928318, "dur": 40, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928366, "dur": 45, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928413, "dur": 1, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928415, "dur": 51, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928470, "dur": 59, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928531, "dur": 1, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928541, "dur": 39, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928583, "dur": 37, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928623, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928625, "dur": 36, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928663, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928666, "dur": 53, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928721, "dur": 1, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928725, "dur": 37, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928764, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928767, "dur": 42, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928813, "dur": 48, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928863, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928866, "dur": 35, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928905, "dur": 37, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928946, "dur": 31, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928982, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585928984, "dur": 32, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929019, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929057, "dur": 35, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929095, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929170, "dur": 3, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929176, "dur": 58, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929236, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929239, "dur": 47, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929287, "dur": 1, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929289, "dur": 37, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929329, "dur": 35, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929366, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929368, "dur": 64, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929434, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929436, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929478, "dur": 33, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929514, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929516, "dur": 37, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929555, "dur": 3, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929559, "dur": 35, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929596, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929598, "dur": 63, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929665, "dur": 5, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929671, "dur": 62, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929737, "dur": 4, "ph": "X", "name": "ProcessMessages 1141", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929742, "dur": 58, "ph": "X", "name": "ReadAsync 1141", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929802, "dur": 2, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929810, "dur": 41, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929853, "dur": 1, "ph": "X", "name": "ProcessMessages 1027", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929854, "dur": 52, "ph": "X", "name": "ReadAsync 1027", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929910, "dur": 45, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929959, "dur": 36, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585929997, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930002, "dur": 42, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930046, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930049, "dur": 39, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930090, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930092, "dur": 45, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930138, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930140, "dur": 55, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930199, "dur": 2, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930203, "dur": 42, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930248, "dur": 1, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930250, "dur": 55, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930308, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930311, "dur": 51, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930364, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930368, "dur": 43, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930412, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930414, "dur": 44, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930463, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930467, "dur": 54, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930523, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930526, "dur": 54, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930586, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930588, "dur": 44, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930647, "dur": 24, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930674, "dur": 66, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930747, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930751, "dur": 60, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930814, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930821, "dur": 58, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930881, "dur": 2, "ph": "X", "name": "ProcessMessages 1020", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930885, "dur": 53, "ph": "X", "name": "ReadAsync 1020", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930943, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930948, "dur": 37, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930988, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585930991, "dur": 31, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931025, "dur": 48, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931078, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931081, "dur": 48, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931131, "dur": 1, "ph": "X", "name": "ProcessMessages 1059", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931134, "dur": 38, "ph": "X", "name": "ReadAsync 1059", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931183, "dur": 3, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931187, "dur": 55, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931252, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931254, "dur": 67, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931324, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931327, "dur": 82, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931423, "dur": 3, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931428, "dur": 86, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931518, "dur": 10, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931561, "dur": 60, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931624, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931628, "dur": 86, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931723, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931732, "dur": 107, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931848, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931851, "dur": 60, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931917, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585931921, "dur": 75, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932003, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932009, "dur": 48, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932063, "dur": 56, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932123, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932130, "dur": 116, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932250, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932256, "dur": 59, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932319, "dur": 47, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932369, "dur": 2, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932375, "dur": 79, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932467, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932562, "dur": 6, "ph": "X", "name": "ProcessMessages 1122", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932571, "dur": 69, "ph": "X", "name": "ReadAsync 1122", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932644, "dur": 1, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932646, "dur": 53, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932703, "dur": 61, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932767, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932769, "dur": 54, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932825, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932827, "dur": 56, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932890, "dur": 4, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932897, "dur": 47, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932946, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585932948, "dur": 69, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933020, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933088, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933090, "dur": 43, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933136, "dur": 39, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933178, "dur": 63, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933246, "dur": 54, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933308, "dur": 4, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933314, "dur": 56, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933374, "dur": 2, "ph": "X", "name": "ProcessMessages 966", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933377, "dur": 29, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933408, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933412, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933460, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933463, "dur": 75, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933545, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933548, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933586, "dur": 4, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933593, "dur": 64, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933659, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933662, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933733, "dur": 2, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933736, "dur": 59, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933800, "dur": 4, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933806, "dur": 47, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933856, "dur": 4, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585933862, "dur": 212, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934080, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934086, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934165, "dur": 5, "ph": "X", "name": "ProcessMessages 1358", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934173, "dur": 54, "ph": "X", "name": "ReadAsync 1358", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934231, "dur": 2, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934238, "dur": 64, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934305, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934307, "dur": 54, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934366, "dur": 6, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934374, "dur": 62, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934440, "dur": 2, "ph": "X", "name": "ProcessMessages 1219", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934443, "dur": 49, "ph": "X", "name": "ReadAsync 1219", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934495, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934498, "dur": 54, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934556, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934558, "dur": 60, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934621, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934624, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934713, "dur": 1, "ph": "X", "name": "ProcessMessages 1230", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934715, "dur": 67, "ph": "X", "name": "ReadAsync 1230", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934787, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934790, "dur": 47, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934840, "dur": 1, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934843, "dur": 49, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934895, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934897, "dur": 74, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934975, "dur": 5, "ph": "X", "name": "ProcessMessages 1378", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585934982, "dur": 46, "ph": "X", "name": "ReadAsync 1378", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935033, "dur": 60, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935097, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935103, "dur": 94, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935200, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935203, "dur": 71, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935283, "dur": 6, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935291, "dur": 152, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935447, "dur": 2, "ph": "X", "name": "ProcessMessages 1281", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935455, "dur": 81, "ph": "X", "name": "ReadAsync 1281", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935541, "dur": 2, "ph": "X", "name": "ProcessMessages 1470", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935545, "dur": 60, "ph": "X", "name": "ReadAsync 1470", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935610, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935616, "dur": 45, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935663, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935666, "dur": 65, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935736, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935740, "dur": 57, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935799, "dur": 1, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935802, "dur": 46, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935849, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935851, "dur": 45, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935901, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935904, "dur": 50, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935956, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585935959, "dur": 46, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936007, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936010, "dur": 61, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936074, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936077, "dur": 59, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936143, "dur": 4, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936152, "dur": 50, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936209, "dur": 1, "ph": "X", "name": "ProcessMessages 1011", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936214, "dur": 69, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936285, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936287, "dur": 69, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936360, "dur": 2, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936363, "dur": 46, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936410, "dur": 1, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936412, "dur": 49, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936471, "dur": 1, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936475, "dur": 74, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936553, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936562, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936642, "dur": 2, "ph": "X", "name": "ProcessMessages 1182", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936647, "dur": 68, "ph": "X", "name": "ReadAsync 1182", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936728, "dur": 3, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936734, "dur": 61, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936799, "dur": 2, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936803, "dur": 76, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936884, "dur": 2, "ph": "X", "name": "ProcessMessages 1037", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936887, "dur": 59, "ph": "X", "name": "ReadAsync 1037", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936950, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585936953, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937041, "dur": 2, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937045, "dur": 102, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937152, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937155, "dur": 79, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937245, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937248, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937327, "dur": 3, "ph": "X", "name": "ProcessMessages 1299", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937332, "dur": 37, "ph": "X", "name": "ReadAsync 1299", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937370, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937373, "dur": 28, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937404, "dur": 60, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937470, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937473, "dur": 58, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937537, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937543, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937614, "dur": 2, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937618, "dur": 70, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937693, "dur": 2, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585937696, "dur": 347, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938050, "dur": 3, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938054, "dur": 97, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938155, "dur": 3, "ph": "X", "name": "ProcessMessages 2396", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938159, "dur": 66, "ph": "X", "name": "ReadAsync 2396", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938233, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938237, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938295, "dur": 2, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938300, "dur": 38, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938343, "dur": 36, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938381, "dur": 2, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938385, "dur": 43, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938432, "dur": 1, "ph": "X", "name": "ProcessMessages 93", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938434, "dur": 75, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938513, "dur": 3, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938517, "dur": 81, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938603, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938607, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938668, "dur": 2, "ph": "X", "name": "ProcessMessages 1106", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938675, "dur": 97, "ph": "X", "name": "ReadAsync 1106", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938776, "dur": 4, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938784, "dur": 60, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938849, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938854, "dur": 46, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938906, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938910, "dur": 52, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938966, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585938968, "dur": 60, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939036, "dur": 1, "ph": "X", "name": "ProcessMessages 107", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939039, "dur": 68, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939111, "dur": 2, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939115, "dur": 64, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939184, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939189, "dur": 65, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939257, "dur": 2, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939261, "dur": 56, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939327, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939426, "dur": 2, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939436, "dur": 83, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939525, "dur": 3, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939530, "dur": 51, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939586, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939588, "dur": 38, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939628, "dur": 2, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939631, "dur": 48, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939683, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939687, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939776, "dur": 2, "ph": "X", "name": "ProcessMessages 1046", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939779, "dur": 47, "ph": "X", "name": "ReadAsync 1046", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939833, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939836, "dur": 56, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939897, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585939900, "dur": 94, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940000, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940003, "dur": 63, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940068, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940071, "dur": 123, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940200, "dur": 3, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940205, "dur": 51, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940259, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940263, "dur": 90, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940360, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940425, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940428, "dur": 52, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940484, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940486, "dur": 53, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940541, "dur": 3, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940545, "dur": 49, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940601, "dur": 3, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940608, "dur": 65, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940678, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940682, "dur": 47, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940736, "dur": 60, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940799, "dur": 3, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940806, "dur": 62, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940873, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940879, "dur": 38, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940920, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585940922, "dur": 77, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941006, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941050, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941054, "dur": 63, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941124, "dur": 3, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941129, "dur": 42, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941173, "dur": 1, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941175, "dur": 53, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941232, "dur": 3, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941238, "dur": 51, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941292, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941296, "dur": 47, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941346, "dur": 3, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941354, "dur": 58, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941417, "dur": 5, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941423, "dur": 49, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941478, "dur": 7, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941486, "dur": 121, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941614, "dur": 3, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941618, "dur": 56, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941678, "dur": 1, "ph": "X", "name": "ProcessMessages 223", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941714, "dur": 57, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941775, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941779, "dur": 65, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941851, "dur": 3, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941856, "dur": 59, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941918, "dur": 2, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941923, "dur": 41, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941967, "dur": 1, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585941974, "dur": 53, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942032, "dur": 2, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942035, "dur": 62, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942102, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942105, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942157, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942159, "dur": 38, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942199, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942201, "dur": 25, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942229, "dur": 96, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942328, "dur": 1, "ph": "X", "name": "ProcessMessages 106", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942331, "dur": 49, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942385, "dur": 2, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942389, "dur": 33, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942424, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942426, "dur": 36, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942465, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942467, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942503, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942506, "dur": 37, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942549, "dur": 46, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942599, "dur": 3, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942604, "dur": 41, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942649, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942651, "dur": 71, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942727, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942736, "dur": 69, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942807, "dur": 2, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942811, "dur": 57, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942872, "dur": 7, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942880, "dur": 60, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942946, "dur": 5, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585942954, "dur": 56, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943015, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943021, "dur": 40, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943065, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943068, "dur": 53, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943127, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943133, "dur": 60, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943196, "dur": 2, "ph": "X", "name": "ProcessMessages 1033", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943200, "dur": 34, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943237, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943244, "dur": 88, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943335, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943338, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943394, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943398, "dur": 65, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943517, "dur": 2, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943521, "dur": 35, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943558, "dur": 1, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943561, "dur": 65, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943630, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943633, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943700, "dur": 6, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943708, "dur": 85, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943798, "dur": 2, "ph": "X", "name": "ProcessMessages 1101", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943802, "dur": 37, "ph": "X", "name": "ReadAsync 1101", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943842, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943844, "dur": 32, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943879, "dur": 1, "ph": "X", "name": "ProcessMessages 99", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943883, "dur": 50, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943935, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585943940, "dur": 268, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944217, "dur": 5, "ph": "X", "name": "ProcessMessages 1079", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944224, "dur": 91, "ph": "X", "name": "ReadAsync 1079", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944320, "dur": 3, "ph": "X", "name": "ProcessMessages 3554", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944325, "dur": 31, "ph": "X", "name": "ReadAsync 3554", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944367, "dur": 31, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944415, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944419, "dur": 59, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944488, "dur": 2, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944492, "dur": 40, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944534, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944536, "dur": 56, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944596, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944599, "dur": 58, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944661, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944664, "dur": 53, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944720, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944723, "dur": 48, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944773, "dur": 3, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944777, "dur": 52, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944839, "dur": 3, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944846, "dur": 53, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944902, "dur": 2, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585944906, "dur": 131, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945041, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945044, "dur": 44, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945090, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945093, "dur": 50, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945151, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945155, "dur": 89, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945248, "dur": 2, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945253, "dur": 54, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945310, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945313, "dur": 31, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945348, "dur": 37, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945391, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945398, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945470, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945473, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945522, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945526, "dur": 29, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945557, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945561, "dur": 44, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945608, "dur": 2, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945612, "dur": 48, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945671, "dur": 2, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945674, "dur": 68, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945751, "dur": 4, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945757, "dur": 54, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945814, "dur": 1, "ph": "X", "name": "ProcessMessages 98", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945819, "dur": 61, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945887, "dur": 2, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945892, "dur": 49, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945943, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945947, "dur": 40, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945993, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585945996, "dur": 41, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946053, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946056, "dur": 57, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946123, "dur": 6, "ph": "X", "name": "ProcessMessages 117", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946134, "dur": 43, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946181, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946185, "dur": 64, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946259, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946270, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946366, "dur": 3, "ph": "X", "name": "ProcessMessages 1486", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946371, "dur": 86, "ph": "X", "name": "ReadAsync 1486", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946463, "dur": 2, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946468, "dur": 94, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946571, "dur": 3, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946579, "dur": 65, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946649, "dur": 3, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946657, "dur": 119, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946788, "dur": 6, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946796, "dur": 70, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946873, "dur": 4, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585946879, "dur": 58, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947001, "dur": 4, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947008, "dur": 68, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947080, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947082, "dur": 62, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947157, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947161, "dur": 74, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947246, "dur": 3, "ph": "X", "name": "ProcessMessages 1118", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947253, "dur": 86, "ph": "X", "name": "ReadAsync 1118", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947342, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947347, "dur": 70, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947425, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947460, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947509, "dur": 2, "ph": "X", "name": "ProcessMessages 916", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947513, "dur": 41, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947557, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947562, "dur": 51, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947618, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947622, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947677, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947680, "dur": 52, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947737, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947740, "dur": 37, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947782, "dur": 16, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947800, "dur": 31, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947838, "dur": 105, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947953, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585947959, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948038, "dur": 2, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948043, "dur": 34, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948087, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948185, "dur": 5, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948199, "dur": 177, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948381, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948385, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948452, "dur": 2, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948458, "dur": 42, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948506, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948516, "dur": 159, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948688, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948703, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948809, "dur": 3, "ph": "X", "name": "ProcessMessages 1102", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948814, "dur": 84, "ph": "X", "name": "ReadAsync 1102", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948901, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948907, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948984, "dur": 2, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585948995, "dur": 68, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949068, "dur": 3, "ph": "X", "name": "ProcessMessages 119", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949075, "dur": 68, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949162, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949169, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949252, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949260, "dur": 59, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949334, "dur": 8, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949344, "dur": 48, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949399, "dur": 7, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949409, "dur": 21, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949436, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949539, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949545, "dur": 150, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949699, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949702, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949758, "dur": 6, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949767, "dur": 161, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949935, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585949939, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950039, "dur": 3, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950048, "dur": 91, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950142, "dur": 5, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950149, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950227, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950237, "dur": 76, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950331, "dur": 3, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950340, "dur": 57, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950402, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950404, "dur": 59, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950491, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950498, "dur": 44, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950549, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950557, "dur": 132, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950695, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950701, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950781, "dur": 4, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950787, "dur": 79, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950869, "dur": 2, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585950875, "dur": 130, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951033, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951070, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951154, "dur": 2, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951163, "dur": 103, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951275, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951297, "dur": 92, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951396, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951402, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951476, "dur": 2, "ph": "X", "name": "ProcessMessages 967", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951486, "dur": 112, "ph": "X", "name": "ReadAsync 967", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951605, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951632, "dur": 7, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951641, "dur": 176, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951822, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951831, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951924, "dur": 12, "ph": "X", "name": "ProcessMessages 1051", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585951938, "dur": 52, "ph": "X", "name": "ReadAsync 1051", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952003, "dur": 10, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952014, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952078, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952081, "dur": 97, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952184, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952194, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952270, "dur": 3, "ph": "X", "name": "ProcessMessages 1245", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952290, "dur": 132, "ph": "X", "name": "ReadAsync 1245", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952428, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952439, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952521, "dur": 9, "ph": "X", "name": "ProcessMessages 1194", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952531, "dur": 90, "ph": "X", "name": "ReadAsync 1194", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952625, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952635, "dur": 138, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952788, "dur": 8, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952802, "dur": 65, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952872, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952883, "dur": 60, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952946, "dur": 2, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585952952, "dur": 56, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585953075, "dur": 12, "ph": "X", "name": "ProcessMessages 95", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585953095, "dur": 97, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585953203, "dur": 12, "ph": "X", "name": "ProcessMessages 997", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585953221, "dur": 54, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585953280, "dur": 183, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585953475, "dur": 11, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585953489, "dur": 72, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585953578, "dur": 11, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585953593, "dur": 79, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585953679, "dur": 7, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585953690, "dur": 57, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585953750, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585953754, "dur": 210, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585953978, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585953982, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954071, "dur": 3, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954129, "dur": 92, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954224, "dur": 1, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954229, "dur": 22, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954255, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954272, "dur": 70, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954345, "dur": 4, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954354, "dur": 39, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954395, "dur": 49, "ph": "X", "name": "ProcessMessages 98", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954449, "dur": 62, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954516, "dur": 12, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954534, "dur": 47, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954594, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954598, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954665, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954671, "dur": 91, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954774, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954779, "dur": 65, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954851, "dur": 3, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585954856, "dur": 139, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955002, "dur": 6, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955011, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955080, "dur": 4, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955113, "dur": 70, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955211, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955278, "dur": 6, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955286, "dur": 51, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955345, "dur": 57, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955414, "dur": 2, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955419, "dur": 135, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955563, "dur": 9, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955576, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955639, "dur": 2, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955643, "dur": 58, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955707, "dur": 1, "ph": "X", "name": "ProcessMessages 119", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955717, "dur": 42, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955770, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955783, "dur": 107, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955897, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585955910, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956057, "dur": 31, "ph": "X", "name": "ProcessMessages 1478", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956092, "dur": 94, "ph": "X", "name": "ReadAsync 1478", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956193, "dur": 2, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956240, "dur": 72, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956319, "dur": 5, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956336, "dur": 74, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956414, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956418, "dur": 39, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956468, "dur": 3, "ph": "X", "name": "ProcessMessages 5", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956477, "dur": 38, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956519, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956524, "dur": 200, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956778, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956789, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956886, "dur": 4, "ph": "X", "name": "ProcessMessages 1089", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956922, "dur": 53, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956980, "dur": 6, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585956991, "dur": 52, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957049, "dur": 1, "ph": "X", "name": "ProcessMessages 1101", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957052, "dur": 66, "ph": "X", "name": "ReadAsync 1101", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957127, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957138, "dur": 93, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957238, "dur": 3, "ph": "X", "name": "ProcessMessages 1134", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957310, "dur": 92, "ph": "X", "name": "ReadAsync 1134", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957406, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957422, "dur": 140, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957571, "dur": 3, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957590, "dur": 138, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957732, "dur": 11, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957771, "dur": 99, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957877, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957880, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957950, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585957956, "dur": 135, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958095, "dur": 9, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958111, "dur": 198, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958317, "dur": 4, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958324, "dur": 85, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958416, "dur": 3, "ph": "X", "name": "ProcessMessages 1024", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958421, "dur": 82, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958511, "dur": 3, "ph": "X", "name": "ProcessMessages 1160", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958518, "dur": 57, "ph": "X", "name": "ReadAsync 1160", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958579, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958583, "dur": 52, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958639, "dur": 5, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958647, "dur": 36, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958685, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958687, "dur": 88, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958782, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958784, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958880, "dur": 3, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958891, "dur": 49, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958942, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585958947, "dur": 71, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959027, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959031, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959103, "dur": 2, "ph": "X", "name": "ProcessMessages 1020", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959108, "dur": 84, "ph": "X", "name": "ReadAsync 1020", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959198, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959205, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959294, "dur": 2, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959304, "dur": 82, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959391, "dur": 37, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959437, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959588, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959625, "dur": 88, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959721, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959724, "dur": 62, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959795, "dur": 2, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959799, "dur": 113, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959923, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959977, "dur": 5, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585959984, "dur": 146, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585960138, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585960147, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585960206, "dur": 2, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585960209, "dur": 110, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585960326, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585960331, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585960401, "dur": 2, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585960404, "dur": 307, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585960723, "dur": 3, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585960729, "dur": 91, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585960826, "dur": 11, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585960839, "dur": 42, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585960887, "dur": 7, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585960896, "dur": 80, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961086, "dur": 1, "ph": "X", "name": "ProcessMessages 117", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961091, "dur": 83, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961182, "dur": 3, "ph": "X", "name": "ProcessMessages 988", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961189, "dur": 56, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961256, "dur": 5, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961264, "dur": 50, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961318, "dur": 2, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961322, "dur": 48, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961375, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961384, "dur": 153, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961612, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961618, "dur": 127, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961751, "dur": 7, "ph": "X", "name": "ProcessMessages 1110", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961768, "dur": 69, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961840, "dur": 26, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585961884, "dur": 132, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962020, "dur": 3, "ph": "X", "name": "ProcessMessages 1133", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962025, "dur": 54, "ph": "X", "name": "ReadAsync 1133", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962083, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962087, "dur": 167, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962266, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962276, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962343, "dur": 2, "ph": "X", "name": "ProcessMessages 1237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962351, "dur": 122, "ph": "X", "name": "ReadAsync 1237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962511, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962513, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962580, "dur": 2, "ph": "X", "name": "ProcessMessages 1216", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962584, "dur": 53, "ph": "X", "name": "ReadAsync 1216", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962729, "dur": 3, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962735, "dur": 71, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962810, "dur": 34, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962858, "dur": 71, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962943, "dur": 3, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585962949, "dur": 105, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963062, "dur": 2, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963072, "dur": 105, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963181, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963196, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963244, "dur": 7, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963253, "dur": 125, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963383, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963386, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963444, "dur": 5, "ph": "X", "name": "ProcessMessages 1067", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963451, "dur": 40, "ph": "X", "name": "ReadAsync 1067", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963499, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963501, "dur": 199, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963712, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963719, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963823, "dur": 4, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963828, "dur": 55, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963891, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963898, "dur": 67, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963971, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585963983, "dur": 68, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585964058, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585964064, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585964158, "dur": 2, "ph": "X", "name": "ProcessMessages 1134", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585964161, "dur": 95, "ph": "X", "name": "ReadAsync 1134", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585964262, "dur": 183, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585964466, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585964561, "dur": 6, "ph": "X", "name": "ProcessMessages 1139", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585964572, "dur": 41, "ph": "X", "name": "ReadAsync 1139", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585964622, "dur": 38, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585964671, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585964676, "dur": 263, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585964945, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585964952, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965042, "dur": 3, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965046, "dur": 67, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965126, "dur": 5, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965137, "dur": 62, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965204, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965212, "dur": 139, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965355, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965369, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965462, "dur": 7, "ph": "X", "name": "ProcessMessages 1194", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965471, "dur": 64, "ph": "X", "name": "ReadAsync 1194", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965537, "dur": 3, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965542, "dur": 41, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965593, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965601, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965688, "dur": 34, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965734, "dur": 243, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965984, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585965989, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966044, "dur": 12, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966058, "dur": 228, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966292, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966294, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966350, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966352, "dur": 108, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966463, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966478, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966570, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966575, "dur": 71, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966649, "dur": 3, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966668, "dur": 174, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966847, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966851, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966958, "dur": 2, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585966964, "dur": 81, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967104, "dur": 4, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967116, "dur": 75, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967193, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967198, "dur": 94, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967298, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967315, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967395, "dur": 2, "ph": "X", "name": "ProcessMessages 1137", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967399, "dur": 208, "ph": "X", "name": "ReadAsync 1137", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967625, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967629, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967705, "dur": 3, "ph": "X", "name": "ProcessMessages 1062", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967714, "dur": 70, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967790, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967798, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967845, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967848, "dur": 66, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967918, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967920, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967990, "dur": 3, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585967998, "dur": 174, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585968179, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585968184, "dur": 115, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585968303, "dur": 3, "ph": "X", "name": "ProcessMessages 1682", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585968307, "dur": 90, "ph": "X", "name": "ReadAsync 1682", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585968409, "dur": 61, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585968475, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585968577, "dur": 3, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585968586, "dur": 83, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585968675, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585968680, "dur": 231, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585968922, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585968928, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585968973, "dur": 3, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585968981, "dur": 76, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969070, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969076, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969167, "dur": 2, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969170, "dur": 40, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969213, "dur": 13, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969258, "dur": 80, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969345, "dur": 4, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969353, "dur": 96, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969459, "dur": 6, "ph": "X", "name": "ProcessMessages 1410", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969471, "dur": 40, "ph": "X", "name": "ReadAsync 1410", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969588, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969590, "dur": 41, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969640, "dur": 4, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969664, "dur": 78, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969746, "dur": 2, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969752, "dur": 98, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969854, "dur": 4, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969871, "dur": 84, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969966, "dur": 2, "ph": "X", "name": "ProcessMessages 104", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585969970, "dur": 103, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970079, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970086, "dur": 93, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970183, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970207, "dur": 155, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970371, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970377, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970453, "dur": 6, "ph": "X", "name": "ProcessMessages 1121", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970462, "dur": 227, "ph": "X", "name": "ReadAsync 1121", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970702, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970709, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970784, "dur": 2, "ph": "X", "name": "ProcessMessages 1093", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970790, "dur": 48, "ph": "X", "name": "ReadAsync 1093", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970841, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970844, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970913, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585970923, "dur": 130, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971062, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971072, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971127, "dur": 5, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971134, "dur": 40, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971179, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971181, "dur": 169, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971357, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971362, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971412, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971415, "dur": 229, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971653, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971658, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971705, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971716, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971863, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585971996, "dur": 6, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972005, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972073, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972134, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972137, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972189, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972192, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972242, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972248, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972414, "dur": 8, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972426, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972478, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972485, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972535, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972538, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972588, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972595, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972661, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972665, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972704, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972709, "dur": 41, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972752, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972754, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972793, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972795, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972883, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972888, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972926, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972928, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972972, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585972975, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973034, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973037, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973086, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973088, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973142, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973146, "dur": 105, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973255, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973263, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973290, "dur": 8, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973301, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973358, "dur": 8, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973368, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973448, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973455, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973519, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973527, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973594, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973597, "dur": 158, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973759, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973763, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973819, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973825, "dur": 86, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973921, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973927, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973982, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585973984, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974041, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974051, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974097, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974102, "dur": 186, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974303, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974314, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974406, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974413, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974469, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974528, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974542, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974588, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974594, "dur": 66, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974674, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974678, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974737, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974745, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974790, "dur": 7, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974800, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974866, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974874, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974932, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974935, "dur": 53, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585974992, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975028, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975035, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975073, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975076, "dur": 41, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975122, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975125, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975178, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975194, "dur": 268, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975471, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975479, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975570, "dur": 4, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975575, "dur": 48, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975626, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975660, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975696, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975700, "dur": 95, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975797, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975802, "dur": 70, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975881, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975957, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585975968, "dur": 64, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976035, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976039, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976104, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976107, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976176, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976181, "dur": 90, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976276, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976282, "dur": 52, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976358, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976361, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976419, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976562, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976570, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976660, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976669, "dur": 59, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976734, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976742, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976792, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976800, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976844, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976850, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976905, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585976912, "dur": 303, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977231, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977234, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977280, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977341, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977354, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977413, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977416, "dur": 55, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977480, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977488, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977547, "dur": 11, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977570, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977608, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977620, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977666, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977674, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977728, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977733, "dur": 85, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977825, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977833, "dur": 105, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977945, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585977950, "dur": 126, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978119, "dur": 31, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978153, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978248, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978253, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978308, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978310, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978387, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978400, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978467, "dur": 30, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978502, "dur": 50, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978554, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978562, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978601, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978664, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978667, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978710, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978716, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978769, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978779, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978853, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978857, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978911, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978913, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978965, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585978971, "dur": 72, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979049, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979053, "dur": 53, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979110, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979116, "dur": 57, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979181, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979185, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979242, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979249, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979292, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979319, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979334, "dur": 162, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979500, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979509, "dur": 83, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979597, "dur": 3, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979604, "dur": 74, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979682, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979685, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979744, "dur": 3, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979750, "dur": 47, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979800, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979802, "dur": 106, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979918, "dur": 7, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585979930, "dur": 81, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980017, "dur": 6, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980027, "dur": 67, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980098, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980103, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980189, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980193, "dur": 72, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980274, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980281, "dur": 40, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980325, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980327, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980373, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980385, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980418, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980426, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980486, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980490, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980540, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980544, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980618, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980622, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980707, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980715, "dur": 63, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980783, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980785, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980823, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980925, "dur": 6, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980935, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980990, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585980994, "dur": 183, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981185, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981187, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981234, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981239, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981315, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981320, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981364, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981369, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981409, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981423, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981483, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981491, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981528, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981533, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981576, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981625, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981635, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981706, "dur": 5, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981719, "dur": 77, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981807, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981854, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981893, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981896, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981925, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981930, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585981997, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982001, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982068, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982071, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982117, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982121, "dur": 67, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982192, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982196, "dur": 76, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982285, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982301, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982370, "dur": 9, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982381, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982450, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982455, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982515, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982519, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982553, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982556, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982601, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982612, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982804, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982809, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982847, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585982860, "dur": 513, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585983382, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585983386, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585983453, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585983457, "dur": 134, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585983595, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585983600, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585983687, "dur": 6, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585983700, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585983801, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585983804, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585983843, "dur": 225, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585984075, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585984081, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585984132, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585984137, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585984399, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585984403, "dur": 136, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585984541, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585984546, "dur": 1247, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585985810, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585985816, "dur": 142, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585985969, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585985977, "dur": 709, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585986703, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585986707, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585986759, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585986762, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585986817, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585986949, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585986958, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585987010, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309585987013, "dur": 13160, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586000183, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586000189, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586000239, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586000242, "dur": 59, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586000310, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586000318, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586000412, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586000416, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586000473, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586000479, "dur": 1050, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586001538, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586001548, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586001631, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586001634, "dur": 134, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586001774, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586001820, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586001825, "dur": 215, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002060, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002196, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002200, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002272, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002278, "dur": 199, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002485, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002491, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002583, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002586, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002722, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002731, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002785, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002788, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002864, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002866, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002913, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002917, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002983, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586002985, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003038, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003040, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003082, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003182, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003187, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003214, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003221, "dur": 277, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003503, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003511, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003575, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003578, "dur": 191, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003774, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003782, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003827, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003858, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586003977, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586004040, "dur": 6, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586004048, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586004155, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586004161, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586004255, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586004263, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586004334, "dur": 9, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586004355, "dur": 315, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586004676, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586004684, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586004824, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586004828, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586004917, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586004926, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005062, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005112, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005116, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005153, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005187, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005190, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005288, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005338, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005343, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005378, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005385, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005420, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005539, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005541, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005587, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005590, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005623, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005627, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005681, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005714, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005746, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005789, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005793, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005842, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586005846, "dur": 172, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006021, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006023, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006092, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006096, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006140, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006168, "dur": 144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006316, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006318, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006382, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006387, "dur": 162, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006560, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006567, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006610, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006612, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006654, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006659, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006715, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006718, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006761, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006770, "dur": 186, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006961, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586006966, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007003, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007012, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007037, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007157, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007166, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007223, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007237, "dur": 88, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007330, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007335, "dur": 153, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007493, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007496, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007546, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007549, "dur": 185, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007738, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007741, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007783, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007787, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007842, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007864, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007959, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586007965, "dur": 273, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586008244, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586008249, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586008292, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586008294, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586008370, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586008372, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586008422, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586008427, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586008568, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586008623, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586008631, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586008684, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586008689, "dur": 205, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586008907, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586008909, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009019, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009026, "dur": 49, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009080, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009089, "dur": 49, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009149, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009155, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009242, "dur": 6, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009257, "dur": 179, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009442, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009446, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009482, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009484, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009546, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009552, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009636, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009640, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009692, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009697, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009745, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009753, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009804, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009810, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009873, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586009876, "dur": 539, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586010422, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586010427, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586010472, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586010475, "dur": 551, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011037, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011044, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011109, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011111, "dur": 147, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011264, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011317, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011325, "dur": 101, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011432, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011469, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011472, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011533, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011536, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011585, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011589, "dur": 247, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011840, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011843, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011902, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011906, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011952, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586011982, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012028, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012031, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012066, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012072, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012112, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012117, "dur": 144, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012264, "dur": 11, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012277, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012334, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012337, "dur": 170, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012532, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012534, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012573, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012575, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012609, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012618, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012646, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012671, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012682, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012719, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586012736, "dur": 262, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013005, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013013, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013093, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013099, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013158, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013164, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013227, "dur": 7, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013236, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013314, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013319, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013357, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013365, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013403, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013410, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013545, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013547, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013610, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013615, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013668, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013678, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013716, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013723, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013778, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013785, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013829, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013831, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013946, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013950, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586013988, "dur": 209, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586014293, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586014297, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586014342, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586014344, "dur": 47, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586014396, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586014401, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586014489, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586014491, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586014531, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586014536, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586014594, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586014598, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586014640, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586014644, "dur": 432, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015086, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015093, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015277, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015283, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015387, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015390, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015524, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015526, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015565, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015700, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015710, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015771, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015775, "dur": 123, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015906, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015948, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586015951, "dur": 252, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586016213, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586016217, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586016265, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586016268, "dur": 51, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586016328, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586016338, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586016473, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586016476, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586016531, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586016534, "dur": 716, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586017259, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586017270, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586017307, "dur": 11, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586017319, "dur": 136, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586017459, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586017471, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586017516, "dur": 8, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586017525, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586017716, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586017742, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586017810, "dur": 380, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586018196, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586018213, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586018288, "dur": 9, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586018299, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586018390, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586018397, "dur": 561, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586018965, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586018975, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586019040, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586019045, "dur": 406, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586019457, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586019463, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586019515, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586019529, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586019616, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586019621, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586019707, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586019710, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586019795, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586019802, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586019853, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586019858, "dur": 236, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020108, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020111, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020149, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020162, "dur": 222, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020389, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020393, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020436, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020443, "dur": 107, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020557, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020569, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020598, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020604, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020674, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020737, "dur": 8, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020747, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586020807, "dur": 547, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586021361, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586021372, "dur": 183, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586021563, "dur": 6, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586021576, "dur": 403, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586021987, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586021995, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586022092, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586022098, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586022139, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586022145, "dur": 244, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586022397, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586022400, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586022455, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586022502, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586022504, "dur": 593, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586023109, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586023116, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586023172, "dur": 6, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586023180, "dur": 664, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586023851, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586023857, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586023938, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586023940, "dur": 83572, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586107523, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586107529, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586107595, "dur": 56, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586107652, "dur": 15980, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586123727, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586123732, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586123787, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586123789, "dur": 324, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586124121, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586124125, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586124159, "dur": 1526, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586125693, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586125698, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586125766, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586125771, "dur": 185, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586125960, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586125963, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586126002, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586126157, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586126159, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586126194, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586126196, "dur": 102, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586126303, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586126334, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586126335, "dur": 324, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586126663, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586126665, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586126715, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586126718, "dur": 1383, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586128108, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586128112, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586128157, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586128159, "dur": 269, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586128434, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586128438, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586128478, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586128558, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586128560, "dur": 287, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586128853, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586128904, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586128906, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586128943, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586128945, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129021, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129024, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129050, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129099, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129151, "dur": 28, "ph": "X", "name": "ReadAsync 2", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129181, "dur": 1, "ph": "X", "name": "ProcessMessages 2", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129183, "dur": 69, "ph": "X", "name": "ReadAsync 2", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129255, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129257, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129296, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129298, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129458, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129479, "dur": 274, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129758, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129799, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129801, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129909, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586129932, "dur": 1142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586131079, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586131083, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586131112, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586131114, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586131154, "dur": 622, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586131785, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586131789, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586131833, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586131835, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586131921, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586131923, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586131973, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586131976, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586132031, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586132035, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586132077, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586132079, "dur": 610, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586132694, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586132699, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586132730, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586132973, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586133012, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586133016, "dur": 449, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586133469, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586133513, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586133515, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586133551, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586133590, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586133592, "dur": 610, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586134206, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586134252, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586134254, "dur": 159, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586134417, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586134461, "dur": 437, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586134904, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586134909, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586134971, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586134976, "dur": 1012, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136000, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136006, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136072, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136075, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136119, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136121, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136209, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136210, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136283, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136286, "dur": 314, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136606, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136609, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136661, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136758, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136759, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136803, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586136806, "dur": 272, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137083, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137085, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137141, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137144, "dur": 305, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137457, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137460, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137509, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137512, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137559, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137562, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137608, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137611, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137643, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137645, "dur": 63, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137714, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137720, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137782, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137784, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137820, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137824, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137875, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137877, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137920, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586137922, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586138068, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586138071, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586138123, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586138126, "dur": 823, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586138958, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586138965, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139058, "dur": 4, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139069, "dur": 31, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139104, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139106, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139130, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139132, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139167, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139169, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139198, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139200, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139237, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139239, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139266, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139300, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139302, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139327, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139329, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139424, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139427, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139481, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139484, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139516, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139521, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139580, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139584, "dur": 122, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139709, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139714, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139745, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139747, "dur": 87, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139837, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139839, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139884, "dur": 29, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139917, "dur": 40, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139959, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586139962, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140007, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140012, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140049, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140053, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140071, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140073, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140108, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140112, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140160, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140162, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140297, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140303, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140353, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140355, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140427, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140430, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140497, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140504, "dur": 102, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140611, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140615, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140656, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140658, "dur": 201, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140864, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140868, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140912, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140915, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140970, "dur": 6, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586140981, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586141141, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586141144, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586141184, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586141188, "dur": 67, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586141268, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586141272, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586141320, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586141324, "dur": 462, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586141797, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586141806, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586141877, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586141884, "dur": 938, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586142831, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586142842, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586142890, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 19704, "tid": 42949672960, "ts": 1751309586142896, "dur": 13818, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 19704, "tid": 1894, "ts": 1751309586158065, "dur": 9162, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 19704, "tid": 38654705664, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 19704, "tid": 38654705664, "ts": 1751309585904446, "dur": 213653, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 19704, "tid": 38654705664, "ts": 1751309586118101, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 19704, "tid": 38654705664, "ts": 1751309586118102, "dur": 97, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 19704, "tid": 1894, "ts": 1751309586167235, "dur": 11, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 19704, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 19704, "tid": 34359738368, "ts": 1751309585900384, "dur": 256401, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751309585900506, "dur": 3360, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751309586156792, "dur": 310, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751309586156819, "dur": 30, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751309586157106, "dur": 2, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 19704, "tid": 1894, "ts": 1751309586167248, "dur": 11, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751309585922292, "dur": 78, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309585922402, "dur": 2442, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309585924862, "dur": 835, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309585925819, "dur": 78, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751309585925897, "dur": 540, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309585926643, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_20A6B658F2F96E47.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585928424, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F0EAC96F0603CD08.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585929694, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585931290, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585931724, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751309585931944, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585932058, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585932260, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585932361, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751309585932718, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751309585933910, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585934970, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751309585935513, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585935687, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Runtime.ref.dll_35E0707C8501A09D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585936060, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585936188, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585937488, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585937565, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585938208, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751309585938525, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585938687, "dur": 141, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_E03858E727F23F6F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585938937, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751309585939319, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585939632, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585939909, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751309585940277, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585940652, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585941011, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585941143, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585941898, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751309585942080, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585942379, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751309585942448, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585942517, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585942766, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585942828, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751309585943359, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585944331, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585944831, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585945445, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585945650, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751309585945854, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585946067, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585946209, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585946425, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751309585946596, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309585947004, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751309585947060, "dur": 137, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751309585947260, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751309585947400, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751309585947492, "dur": 159, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751309585948238, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 0, "ts": 1751309585948588, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585948980, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585949524, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585949782, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751309585949939, "dur": 169, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585950280, "dur": 232, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751309585950553, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585950771, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751309585950980, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585951237, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751309585951293, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751309585951534, "dur": 144, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751309585951700, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585952018, "dur": 169, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585952476, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585953734, "dur": 136, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751309585953918, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585954764, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751309585954889, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585955199, "dur": 130, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585955377, "dur": 186, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585955656, "dur": 134, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585956098, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751309585956177, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585956527, "dur": 110, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751309585956660, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751309585956943, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8863518860715653438.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585957435, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751309585957595, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585958046, "dur": 395, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585959819, "dur": 141, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585960538, "dur": 141, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585960931, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585961716, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751309585962148, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585962549, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585963367, "dur": 145, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751309585964323, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585964973, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585965060, "dur": 122, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585965445, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751309585965559, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751309585965644, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585966101, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751309585966235, "dur": 280, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585967015, "dur": 117, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751309585967394, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751309585967530, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585968825, "dur": 179, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751309585969110, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585969774, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751309585969980, "dur": 157, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751309585970190, "dur": 158, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585970469, "dur": 172, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585971002, "dur": 216, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751309585971229, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585971336, "dur": 289, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309585926465, "dur": 45460, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309585971948, "dur": 170682, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309586142631, "dur": 236, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309586142868, "dur": 191, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309586143215, "dur": 90, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309586143354, "dur": 2093, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751309585927192, "dur": 45812, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309585973013, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_B566D0CB2E423B86.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585973140, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9843DD54C5E6159E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585973216, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_E5318FF293833B30.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585973291, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7E47E9336205E31D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585973363, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_526B70D8724FDBD0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585973447, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A619838E516A75AC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585973579, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_BFD5601BA05C7CD8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585973653, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BADDD17E000661FB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585973779, "dur": 574, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BADDD17E000661FB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585974356, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7B8E45163E4907D2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585974623, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_C01CA8838C1CE31C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585974702, "dur": 531, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_C01CA8838C1CE31C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585975237, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_922751811D286E63.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585975335, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AFE94C31B12D3213.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585975816, "dur": 938, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AFE94C31B12D3213.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585976783, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585976923, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309585977107, "dur": 10050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751309585987261, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309585987464, "dur": 13166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751309586000754, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309586000965, "dur": 1025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751309586002129, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309586002332, "dur": 4850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751309586007254, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309586007537, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309586007737, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751309586008440, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309586008693, "dur": 1241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751309586010005, "dur": 1762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751309586011814, "dur": 332, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309586012655, "dur": 95405, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751309586121590, "dur": 2513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751309586124104, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309586124161, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751309586124272, "dur": 2192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751309586126521, "dur": 2256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751309586128778, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309586128992, "dur": 2561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751309586131565, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309586131644, "dur": 2313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751309586133958, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309586134031, "dur": 2374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751309586136407, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309586136569, "dur": 2728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751309586139300, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309586139596, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751309586139765, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309586139988, "dur": 298, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751309586140354, "dur": 1043, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309586141411, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309586142343, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309585926984, "dur": 45553, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309585972550, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7B82E1658E753B21.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309585972740, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_8325F75A1495950D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309585972947, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_14A5559FFED55C50.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309585973432, "dur": 1300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309585974733, "dur": 936, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_14A5559FFED55C50.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309585975735, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309585976112, "dur": 344, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751309585976495, "dur": 329, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751309585976827, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751309585977216, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309585977490, "dur": 1576, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751309585979506, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309585979574, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309585979652, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309585979787, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309585980108, "dur": 696, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751309585980833, "dur": 1136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751309585981971, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751309585982246, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751309585982775, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309585983226, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309585985327, "dur": 657, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751309585985984, "dur": 1194, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751309585983701, "dur": 3571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309585987582, "dur": 1487, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\hostpolicy.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751309585989233, "dur": 2211, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Grpc.Core.Api.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751309585991444, "dur": 3590, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Grpc.AspNetCore.Server.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751309585995459, "dur": 1789, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-string-l1-1-0.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751309585987327, "dur": 9922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309585997432, "dur": 1012, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Reflection\\DocumentedOption.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751309585998569, "dur": 2367, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751309585997249, "dur": 3736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586000985, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586001230, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586001456, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586001667, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586001906, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586002281, "dur": 3318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309586005601, "dur": 1061, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586007346, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751309586007981, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586008093, "dur": 2147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751309586010241, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586010411, "dur": 1672, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586012096, "dur": 2162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586014259, "dur": 6201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586020464, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309586020647, "dur": 960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751309586021701, "dur": 99881, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586121584, "dur": 2490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751309586124126, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751309586124214, "dur": 2472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751309586126687, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586126872, "dur": 3394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751309586130267, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586130343, "dur": 6694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751309586137060, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586137167, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751309586137248, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586137338, "dur": 632, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586137995, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586138183, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751309586138324, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586138542, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586138697, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586138749, "dur": 887, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751309586139743, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751309586139876, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751309586140225, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586140873, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586141381, "dur": 986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309586142368, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585926968, "dur": 45159, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585972138, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_82F8B03BA9650A81.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309585972408, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_CC3B7EF1A2C4EA40.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309585972469, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585972565, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_A1485D01311C863E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309585975425, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_1FC72A0013DC4EEB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309585975506, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_12124AFB436F9D5D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309585975626, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_7AC1C063560F2BC6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309585975728, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_570D95476513426D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309585976067, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7F3203F2669A3DC6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309585976265, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585976355, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751309585976487, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751309585976699, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585976750, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751309585976957, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309585977413, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751309585977527, "dur": 1821, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751309585979351, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751309585979937, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751309585980089, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751309585980569, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751309585980935, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585981042, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751309585981704, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585982174, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751309585982304, "dur": 1615, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751309585983921, "dur": 1963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585986216, "dur": 996, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Xml.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751309585987667, "dur": 2338, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.EnvironmentVariables.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751309585990183, "dur": 1036, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Caching.Memory.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751309585985885, "dur": 5335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585991463, "dur": 1502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@89399b10acbb\\Editor\\2D\\Renderer2DMenus.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751309585991220, "dur": 1745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585992966, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585993183, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585993442, "dur": 703, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Data\\Nodes\\Input\\PBR\\DielectricSpecularNode.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751309585993380, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585994300, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585994546, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585994776, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585995176, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585995384, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585995596, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585995817, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585996054, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585996283, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585996486, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585996710, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585996950, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585997174, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585997402, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585997632, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585997856, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585998215, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309585998571, "dur": 3272, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Analysis\\GraphElementAnalysis.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751309585998414, "dur": 3528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309586001942, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309586002189, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309586002356, "dur": 9160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751309586011636, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309586011821, "dur": 1697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751309586013585, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309586013684, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309586013877, "dur": 907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751309586014786, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309586014892, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309586015084, "dur": 920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751309586016085, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309586016281, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751309586016755, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309586016838, "dur": 105502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309586122342, "dur": 2292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751309586124677, "dur": 2320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751309586126998, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309586127235, "dur": 2520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751309586129756, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309586130026, "dur": 2524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751309586132598, "dur": 2317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751309586134978, "dur": 6494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751309586141535, "dur": 970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585927110, "dur": 45770, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585972892, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_596B249A76101BE6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309585973066, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F27EBD72F4881481.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309585973276, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_C047F56604413718.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309585973410, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_0EEB0D491376C433.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309585973487, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0FEAE2B241ACD344.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309585973557, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0999ABDFA115F312.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309585973637, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_64D9AC7F4C4AC8A8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309585973708, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_64D9AC7F4C4AC8A8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309585973870, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_658DA73782B14E20.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309585973954, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_209987431F1A3EDC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309585974075, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_FE92BD4D4925733A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309585974152, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B1D6DCAB2D5A18DA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309585974323, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B6153E13308654D1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309585974537, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_F21194D2F617C9ED.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309585974791, "dur": 1685, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_F21194D2F617C9ED.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309585976506, "dur": 273, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751309585976782, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751309585976918, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585977475, "dur": 2039, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751309585979623, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751309585979675, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585980044, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585980104, "dur": 315, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751309585980577, "dur": 405, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751309585981078, "dur": 3078, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751309585985352, "dur": 693, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.ServicePoint.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751309585986162, "dur": 1029, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751309585984159, "dur": 3507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585987758, "dur": 907, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-processenvironment-l1-1-0.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751309585987666, "dur": 1683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585989349, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585989570, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585989763, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585990055, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585990253, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585990440, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585990656, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585991048, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585992466, "dur": 1017, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Generation\\Enumerations\\KeywordShaderStage.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751309585991874, "dur": 1695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585993570, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585993778, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585993994, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585994194, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585994420, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585994644, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585994854, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585995537, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585995775, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585996027, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585996272, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585997001, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585997227, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585997994, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\ShortInspector.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751309585997915, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585998719, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585999023, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585999368, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585999566, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309585999788, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586000006, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586000379, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586000621, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586000959, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586001181, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586001405, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586001776, "dur": 593, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Plugins\\UI\\InputSystemUIInputModule.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751309586001619, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586002411, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309586002605, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309586002761, "dur": 962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751309586003773, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_D58562F0DF82BCBA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309586004715, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309586005122, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586005615, "dur": 1211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751309586006827, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586006906, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309586007566, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751309586008450, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309586008711, "dur": 1540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751309586010315, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586010560, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586010761, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586011360, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586011812, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309586012045, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586012135, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751309586013000, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586013074, "dur": 857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586013933, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309586014110, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751309586014791, "dur": 6224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586021020, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309586021240, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751309586021806, "dur": 99834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586121644, "dur": 2464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751309586124159, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751309586124260, "dur": 2650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751309586126912, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586127232, "dur": 2682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751309586129922, "dur": 517, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586130455, "dur": 2607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751309586133063, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586133240, "dur": 3153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751309586136394, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586136514, "dur": 4582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751309586141097, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586141215, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309586141665, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585926913, "dur": 45179, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585972117, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585972210, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_5F13DCF03A10B823.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309585972466, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585972555, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_8A89D5AFAE49E63E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309585972657, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FECC8B8B813FB02A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309585972848, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_69FC355D1BD4BB29.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309585972922, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585973028, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_15A0A72BE0744047.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309585973207, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585973288, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_4E1AB9DAA17812DB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309585973423, "dur": 818, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585974249, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_77F91A8BF54EFB7C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309585974900, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_48483D327EA17808.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309585974995, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_CBABBF352E91654E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309585975215, "dur": 798, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FEA05FC664382286.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309585976103, "dur": 382, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751309585976522, "dur": 721, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751309585977289, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751309585977464, "dur": 2568, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751309585980052, "dur": 442, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751309585980546, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751309585980810, "dur": 684, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751309585981592, "dur": 3008, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751309585985465, "dur": 735, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751309585986366, "dur": 1090, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751309585987776, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751309585984603, "dur": 3796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585988399, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585988714, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585989162, "dur": 1143, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Editor\\Obsolete\\CinemachineTool.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751309585988982, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585990328, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585990580, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585990822, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585991097, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585991646, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585991977, "dur": 628, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Generation\\Descriptors\\BlockFieldDescriptor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751309585991875, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585992719, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585992940, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585993138, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585993335, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585993645, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585993888, "dur": 2293, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Data\\Graphs\\Matrix3ShaderProperty.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751309585996181, "dur": 2854, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Data\\Graphs\\Matrix3MaterialSlot.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751309585993888, "dur": 5489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585999377, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585999617, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309585999853, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309586000069, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309586000356, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309586000619, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309586002730, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309586003060, "dur": 2612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751309586005673, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309586005885, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309586005944, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309586006271, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309586007116, "dur": 4693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751309586011811, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309586011984, "dur": 417, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751309586012502, "dur": 1732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309586014235, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309586014614, "dur": 921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751309586015538, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309586015600, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751309586015733, "dur": 105855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309586121590, "dur": 2490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751309586124127, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751309586124231, "dur": 2443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751309586126721, "dur": 2553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751309586129276, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309586129675, "dur": 3763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751309586133441, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309586133535, "dur": 8119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751309586141656, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309586141743, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309586141823, "dur": 777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585927054, "dur": 48189, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585975267, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3EF742C8CA4C5E1E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309585975659, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_0D77228FB13B7FB6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309585975740, "dur": 463, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_0D77228FB13B7FB6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309585976209, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751309585976301, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751309585976523, "dur": 2451, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751309585979060, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751309585979297, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585979396, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751309585979605, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585979939, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751309585980040, "dur": 3083, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751309585983126, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751309585983217, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585984119, "dur": 1557, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Unity.ILPP.Runner.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751309585983507, "dur": 2412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585985920, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585987044, "dur": 773, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Results.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751309585986812, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585988336, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585988573, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585988835, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585989042, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585989292, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585989486, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585989869, "dur": 569, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Signals\\SignalUtility.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751309585989690, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585990439, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585991191, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585991467, "dur": 3420, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Util\\FileUtilities.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751309585991442, "dur": 4032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585995475, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585995760, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585996145, "dur": 1322, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Ports\\IUnitPort.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751309585996016, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585997574, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585997796, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309585998067, "dur": 1133, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Description\\MachineDescription.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751309585999200, "dur": 992, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Description\\IMacroDescription.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751309585998031, "dur": 2366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309586000398, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309586000904, "dur": 1615, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@56bff8827a7e\\Unity.Collections\\xxHash3.StreamingState.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751309586002593, "dur": 3734, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@56bff8827a7e\\Unity.Collections\\UnsafeStream.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751309586000801, "dur": 5855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309586006683, "dur": 2124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751309586008808, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309586008969, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309586009223, "dur": 2733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751309586011957, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309586012614, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309586012849, "dur": 1524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751309586014374, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309586014886, "dur": 103868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309586121345, "dur": 493, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1751309586121838, "dur": 1770, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1751309586123609, "dur": 198, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 6, "ts": 1751309586118756, "dur": 5052, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309586123811, "dur": 2343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751309586126155, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309586126275, "dur": 3264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751309586129603, "dur": 2781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751309586132392, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309586132529, "dur": 2759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751309586135290, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309586135424, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751309586135543, "dur": 6708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751309586142349, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585927038, "dur": 45601, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585972658, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_0E82E95F1D4B1B8B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585972772, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_467B09015CCE177C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585972852, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_E681FA607883332E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585972927, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0333895C18D609C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585973127, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585973432, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_308026626194FDB8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585973508, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_91BF37D1F055EC15.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585973596, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D7031BE9F197A0A6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585973733, "dur": 453, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_DB48149D0776AF57.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585974189, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_8FE2697BF98A2193.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585974328, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_93B77B82222D1B4A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585974550, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_9870EDF59A107BDF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585974661, "dur": 483, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_9870EDF59A107BDF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585975147, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_7E691CD5ECB673D9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585975229, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585975317, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_1FF1C2755BAA46D4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585975724, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_BFEF6BF3A445E475.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585975785, "dur": 564, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_BFEF6BF3A445E475.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585976402, "dur": 309, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309585977857, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585978006, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309585978103, "dur": 1114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309585979417, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309585979647, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585979944, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751309585980060, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309585980139, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309585980460, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309585980579, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309585980814, "dur": 707, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309585981857, "dur": 802, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309585982761, "dur": 1595, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309585984359, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585985760, "dur": 734, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.ObjectPool.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751309585986598, "dur": 1003, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.EventLog.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751309585987944, "dur": 2575, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Identity.Core.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751309585990520, "dur": 722, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Http.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751309585985672, "dur": 6212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585991976, "dur": 637, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Generation\\Collections\\KernelCollection.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751309585991884, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585992755, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585992949, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585993148, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585993348, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585993890, "dur": 2329, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Data\\Graphs\\SerializableTextureArray.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751309585993874, "dur": 2542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585996417, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585996626, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585996886, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585997138, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585997433, "dur": 1126, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Plugin\\NamingSchemePage.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751309585997394, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585998782, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585999125, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309585999620, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586000394, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586000665, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586002093, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586002700, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751309586003610, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309586004325, "dur": 1725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751309586006105, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309586006325, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751309586006918, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751309586007525, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586007584, "dur": 818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751309586008472, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309586008729, "dur": 895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751309586009679, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586009741, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Splines.Editor.ref.dll_FBEF939465B2E7A7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309586009804, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586010174, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586010873, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586011350, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586011500, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586011728, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309586011990, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751309586012514, "dur": 648, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586013170, "dur": 832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586014003, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751309586014867, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586014934, "dur": 107592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586122528, "dur": 3717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751309586126293, "dur": 3070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751309586129413, "dur": 4632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751309586134103, "dur": 2402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751309586136506, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309586136738, "dur": 4853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751309586141675, "dur": 771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585926860, "dur": 45195, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585972116, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585972384, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_256223B421F6FD72.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585972646, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_44462716F1E6187F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585972736, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F697CEAF687CB7A2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585972854, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_1182EE28E68C62C8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585972930, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_D32EF602AAA54BE6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585973045, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_1DC8148833156840.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585973200, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_8EA224AC6679B7C2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585973346, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C8A867A03915DFB5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585973426, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_5DFD03B93DA29B4D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585973603, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_DBB153F07C192DFF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585973687, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_DBB153F07C192DFF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585973826, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8B3842F7B08DFF89.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585974156, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_D8FCB30ADD921A73.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585974267, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585974336, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_D4344483B74B03A7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585974571, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_EAAAA0867963BCF7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585974659, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_0B2798972D9D68EB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585974728, "dur": 1630, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_0B2798972D9D68EB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309585976415, "dur": 297, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751309585976855, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585977206, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585977460, "dur": 431, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751309585978015, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751309585978100, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751309585978345, "dur": 1108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751309585979648, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585979790, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751309585979946, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751309585980110, "dur": 299, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751309585980561, "dur": 978, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751309585982267, "dur": 402, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751309585982790, "dur": 2152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751309585986262, "dur": 1081, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Net.Http.Headers.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751309585984944, "dur": 2610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585987554, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585988291, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585988625, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@4d374c7eb6db\\Rider\\Editor\\PluginSettings.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751309585988486, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585989245, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585989436, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585989629, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585989824, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585990096, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585990431, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585990657, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585990940, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585991197, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585991977, "dur": 1188, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Generation\\Processors\\GenerationUtils.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751309585991842, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585993260, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585993663, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585993972, "dur": 2445, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Data\\Graphs\\ColorMaterialSlot.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751309585993904, "dur": 2653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585996558, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585996778, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585997052, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585997324, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585997743, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585997965, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585998401, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585998643, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585998945, "dur": 836, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Runtime\\PostProcessing\\IPostProcessComponent.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751309585998918, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309585999968, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586000279, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586000501, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586000826, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586001067, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586001278, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586001474, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586001691, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586001993, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586002318, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586002674, "dur": 708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586003455, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586003746, "dur": 720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586004529, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586004710, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586004901, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586005107, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586005366, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586005832, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586005921, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586006185, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586006394, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586006611, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586007199, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586007276, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586007522, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586007719, "dur": 912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586008701, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586008881, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586009634, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586009798, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586010097, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586010223, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586010520, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586010759, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586011008, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586011501, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586011636, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586011858, "dur": 1723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586013583, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586013705, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586013928, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586014208, "dur": 721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586014931, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586015016, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586015162, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586015865, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586015972, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586016150, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586016761, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586016846, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309586017037, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586017782, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586017872, "dur": 103779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586121653, "dur": 2472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586124173, "dur": 2040, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586126218, "dur": 2353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586128645, "dur": 3326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586131972, "dur": 498, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586132485, "dur": 2212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586134762, "dur": 3240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586138003, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586138400, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751309586138583, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586138685, "dur": 660, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751309586139581, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751309586139651, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586139769, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586139976, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751309586140184, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751309586140264, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586140452, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751309586141333, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309586141553, "dur": 945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585927168, "dur": 45726, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585972907, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_857E61E190401437.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309585973043, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_0E07881F85CDD302.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309585973113, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_B83C6071BB394334.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309585973192, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_33AFB99E7761AA5C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309585973271, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_595F986B2A245834.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309585973360, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_67CB883191F4D66D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309585973492, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BA0CB78B239CCACF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309585973677, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_BEB6379C07D3D185.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309585973766, "dur": 520, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_BEB6379C07D3D185.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309585974288, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_7DC1C7578DCAB919.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309585974495, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585974726, "dur": 849, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_7DC1C7578DCAB919.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309585975889, "dur": 878, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309585977807, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309585977997, "dur": 8246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751309585986555, "dur": 916, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Razor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751309585987653, "dur": 1080, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.RazorPages.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751309585988857, "dur": 1644, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Formatters.Json.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751309585986351, "dur": 4486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585990838, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585991470, "dur": 3424, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Util\\UIUtilities.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751309585991293, "dur": 3634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585994927, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585995149, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585995346, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585995550, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585995773, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585996089, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Runtime\\GPUDriven\\AABB.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751309585996009, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585996772, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585997029, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585997433, "dur": 1114, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\ResourceProviders\\IResourceProvider.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751309585997232, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585998577, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585998789, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585999306, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585999510, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309585999906, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586000138, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586000476, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586001948, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586002169, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309586002338, "dur": 3547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751309586005963, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309586006296, "dur": 1559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751309586007906, "dur": 1151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751309586009058, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586009131, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586009706, "dur": 87, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586009842, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586010130, "dur": 825, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\VisualElementExtensions.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751309586010104, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586011503, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586011639, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309586011791, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586012017, "dur": 1223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751309586013241, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586013562, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586013754, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309586013929, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751309586014752, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586014842, "dur": 7862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586022710, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309586022937, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586023017, "dur": 98606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586121627, "dur": 2480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751309586124109, "dur": 2159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586126278, "dur": 2278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751309586128557, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586128717, "dur": 2864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751309586131583, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586131709, "dur": 5789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751309586137501, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586137633, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586138191, "dur": 286, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751309586138485, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586138868, "dur": 793, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751309586139767, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586139955, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586140441, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586140682, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586141271, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586141749, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309586141803, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751309586141865, "dur": 951, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585927231, "dur": 45888, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585973373, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DAD9D37CE1A4BA55.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309585973668, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E04F87D224606974.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309585973739, "dur": 474, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E04F87D224606974.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309585974216, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D7CA9FED890DDAB1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309585974326, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585974618, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E689A207C3E7A0C2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309585974716, "dur": 591, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E689A207C3E7A0C2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309585975310, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1948650C4D21EE20.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309585975428, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_1638DBF3D84589E5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309585975916, "dur": 833, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_1638DBF3D84589E5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309585976854, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_3D651E01942E03F8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309585977505, "dur": 1594, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_3D651E01942E03F8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309585979235, "dur": 384, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751309585979644, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585979946, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751309585980089, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751309585980320, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751309585980564, "dur": 1170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751309585981761, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585982280, "dur": 451, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751309585982780, "dur": 1891, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751309585985899, "dur": 1272, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Console.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751309585984674, "dur": 3124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585987799, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585987958, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585988117, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585988266, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585988467, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585988713, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585988971, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585989596, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585989792, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585990023, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585990224, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585990424, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585990644, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585991059, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585991591, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585991974, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Generation\\TargetResources\\StructFields.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751309585991785, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585992558, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585992781, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585992997, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585993188, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585993403, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585993620, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585993815, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585994022, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585994237, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585994448, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585994686, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585995081, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585995260, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585995438, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585995654, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585996147, "dur": 1315, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Runtime\\GPUDriven\\LODGroupRenderingUtils.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751309585995916, "dur": 1569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585997485, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585997705, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585997928, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585998681, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585998904, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585999113, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585999368, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309585999590, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309586000335, "dur": 1874, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Ensure\\Extensions\\XString.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751309586000285, "dur": 2111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309586002397, "dur": 833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309586003270, "dur": 1426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751309586004698, "dur": 985, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309586005737, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309586006242, "dur": 2010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751309586008329, "dur": 1623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751309586009953, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309586010196, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309586010677, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309586010902, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309586011595, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309586011881, "dur": 2069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309586013951, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_D75DF4EA1AFE54F4.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309586014277, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309586014509, "dur": 1495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751309586016088, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309586016246, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751309586016794, "dur": 107354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309586124149, "dur": 4848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751309586128999, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309586129085, "dur": 2484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751309586131637, "dur": 2409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751309586134047, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309586134136, "dur": 2237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751309586136375, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309586136671, "dur": 4447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751309586141204, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309586141623, "dur": 874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585927293, "dur": 46148, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585973588, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_A164BAE5670FC544.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309585973811, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585973998, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_472976495F1230C7.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309585974240, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_1A2A3B79BD3B4CFD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309585974312, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585974496, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DA7D4247AC2DB3F7.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309585974658, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DA7D4247AC2DB3F7.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309585974850, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_D1635D1E3A6F959D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309585974950, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_584BDBB0AACB340C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309585975204, "dur": 412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585975636, "dur": 398, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_80843626DE95EA13.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309585976096, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751309585976271, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751309585976404, "dur": 296, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751309585977461, "dur": 484, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751309585978104, "dur": 1883, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751309585979992, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585980051, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751309585980165, "dur": 322, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751309585980553, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751309585980806, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751309585980986, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585981276, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751309585981671, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585982041, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15875538839575725175.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751309585982236, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10719215101466552486.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751309585982303, "dur": 1475, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10719215101466552486.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751309585983995, "dur": 910, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751309585984996, "dur": 506, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Cryptography.Cng.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751309585985729, "dur": 661, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751309585983780, "dur": 3046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585987008, "dur": 576, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Cryptography.KeyDerivation.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751309585987660, "dur": 2756, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Cors.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751309585986827, "dur": 4291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585991441, "dur": 628, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@89399b10acbb\\Editor\\Camera\\UniversalRenderPipelineCameraUI.Skin.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751309585992070, "dur": 1626, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@89399b10acbb\\Editor\\Camera\\UniversalRenderPipelineCameraUI.Rendering.Skin.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751309585991118, "dur": 2806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585993924, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585994204, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585994497, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585994726, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585994990, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585995671, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585995922, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585996181, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585996402, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585996812, "dur": 1733, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Input\\OnMouseUp.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751309585996597, "dur": 1948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585998545, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309585998936, "dur": 1989, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Runtime\\RenderGraph\\RenderGraphResources.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751309585998836, "dur": 2394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586001231, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586001447, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586001665, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586001907, "dur": 853, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Editor\\UITKAssetEditor\\Views\\Selectors.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751309586001907, "dur": 1919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586003829, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309586004085, "dur": 9016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751309586013102, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586013259, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309586013904, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586013974, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309586014152, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586014346, "dur": 1419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309586015804, "dur": 1888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751309586017696, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586017793, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586018040, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309586018291, "dur": 970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751309586019262, "dur": 730, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586020016, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586020187, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309586020355, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751309586021028, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586021153, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309586021300, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751309586021814, "dur": 102328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586124144, "dur": 2560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751309586126705, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586126764, "dur": 2590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751309586129355, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309586129406, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751309586129515, "dur": 2754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751309586132331, "dur": 9920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751309586142361, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309585927345, "dur": 47454, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309585974809, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_C1BEC5DDD68DA9AF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309585975462, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_486DF76931BD0557.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309585975632, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_E106F825F731F4BC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309585975723, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_715C92D4D50EF494.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309585976059, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_07FA64190ECA4CC1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309585976143, "dur": 539, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_07FA64190ECA4CC1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309585976688, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751309585976740, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309585976888, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309585977206, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309585977451, "dur": 385, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751309585977857, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309585978049, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751309585978325, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751309585978477, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751309585978729, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309585979046, "dur": 948, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751309585980084, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751309585980296, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751309585980471, "dur": 828, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751309585981890, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751309585982287, "dur": 564, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751309585983169, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309585983735, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751309585984344, "dur": 1311, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751309585985809, "dur": 1291, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Windows.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751309585987536, "dur": 1104, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Web.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751309585989014, "dur": 1928, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Transactions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751309585983581, "dur": 7599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309585991207, "dur": 2697, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@89399b10acbb\\Editor\\AssetPostProcessors\\UniversalRenderPipelineGlobalSettingsPostprocessor.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751309585991181, "dur": 2984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309585994165, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309585994415, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309585994866, "dur": 1334, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@89399b10acbb\\Runtime\\FrameData\\UniversalCameraData.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751309585994650, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309585996241, "dur": 2942, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector4\\Vector4Normalize.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751309585999183, "dur": 973, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector4\\Vector4Multiply.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751309586000215, "dur": 4246, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector4\\Vector4Lerp.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751309586004518, "dur": 1127, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector4\\Vector4Average.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751309585996201, "dur": 9654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309586005858, "dur": 986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309586006877, "dur": 4005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751309586010883, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309586011038, "dur": 1074, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\PlasticNotification.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751309586011001, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309586012296, "dur": 1946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309586014242, "dur": 1986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309586016233, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309586016466, "dur": 2206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751309586018674, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309586018754, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309586018905, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309586019540, "dur": 1340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751309586020881, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309586021007, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309586021271, "dur": 1187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751309586022460, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309586022515, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751309586022695, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309586022955, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751309586023542, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309586023637, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751309586024265, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309586024360, "dur": 97261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309586121629, "dur": 2448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751309586124125, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751309586124201, "dur": 5224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751309586129426, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309586129489, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751309586129831, "dur": 2468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751309586132349, "dur": 8564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751309586140915, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309586141036, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309586141637, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309586153372, "dur": 3061, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 19704, "tid": 1894, "ts": 1751309586167335, "dur": 980, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 19704, "tid": 1894, "ts": 1751309586168367, "dur": 7053, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 19704, "tid": 1894, "ts": 1751309586157978, "dur": 17529, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}