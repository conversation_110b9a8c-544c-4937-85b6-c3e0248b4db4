{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 19704, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 19704, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 19704, "tid": 2397, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 19704, "tid": 2397, "ts": 1751311001868092, "dur": 1075, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 19704, "tid": 2397, "ts": 1751311001873988, "dur": 909, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 19704, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 19704, "tid": 1, "ts": 1751311001124032, "dur": 8121, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19704, "tid": 1, "ts": 1751311001132158, "dur": 66930, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19704, "tid": 1, "ts": 1751311001199103, "dur": 44751, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 19704, "tid": 2397, "ts": 1751311001874905, "dur": 13, "ph": "X", "name": "", "args": {}}, {"pid": 19704, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001121610, "dur": 5879, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001127495, "dur": 728757, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001128661, "dur": 3339, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001132012, "dur": 2326, "ph": "X", "name": "ProcessMessages 13803", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001134345, "dur": 364, "ph": "X", "name": "ReadAsync 13803", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001134713, "dur": 13, "ph": "X", "name": "ProcessMessages 20504", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001134727, "dur": 68, "ph": "X", "name": "ReadAsync 20504", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001134797, "dur": 1, "ph": "X", "name": "ProcessMessages 1225", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001134799, "dur": 44, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001134845, "dur": 1, "ph": "X", "name": "ProcessMessages 1053", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001134846, "dur": 59, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001134907, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001134909, "dur": 79, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001134990, "dur": 1, "ph": "X", "name": "ProcessMessages 1234", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001134992, "dur": 43, "ph": "X", "name": "ReadAsync 1234", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135037, "dur": 1, "ph": "X", "name": "ProcessMessages 910", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135038, "dur": 30, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135071, "dur": 71, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135150, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135153, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135189, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135191, "dur": 39, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135232, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135234, "dur": 30, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135267, "dur": 28, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135298, "dur": 20, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135320, "dur": 25, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135347, "dur": 69, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135419, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135421, "dur": 30, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135453, "dur": 115, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135572, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135645, "dur": 3, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135650, "dur": 45, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135696, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135698, "dur": 45, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135747, "dur": 2, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135750, "dur": 24, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135776, "dur": 39, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135818, "dur": 25, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135845, "dur": 32, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135880, "dur": 25, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135908, "dur": 29, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135940, "dur": 50, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135993, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001135995, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136075, "dur": 2, "ph": "X", "name": "ProcessMessages 1294", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136077, "dur": 43, "ph": "X", "name": "ReadAsync 1294", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136122, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136124, "dur": 78, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136206, "dur": 34, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136245, "dur": 50, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136299, "dur": 30, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136332, "dur": 27, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136361, "dur": 35, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136399, "dur": 31, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136433, "dur": 36, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136470, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136471, "dur": 30, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136506, "dur": 42, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136550, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136551, "dur": 44, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136598, "dur": 25, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136625, "dur": 31, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136659, "dur": 26, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136688, "dur": 33, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136723, "dur": 62, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136787, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136788, "dur": 44, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136833, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136835, "dur": 23, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136861, "dur": 72, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001136938, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137003, "dur": 2, "ph": "X", "name": "ProcessMessages 1122", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137006, "dur": 66, "ph": "X", "name": "ReadAsync 1122", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137075, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137077, "dur": 37, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137117, "dur": 29, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137149, "dur": 51, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137202, "dur": 44, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137249, "dur": 26, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137278, "dur": 50, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137331, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137369, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137370, "dur": 45, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137418, "dur": 42, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137463, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137465, "dur": 29, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137496, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137498, "dur": 45, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137546, "dur": 41, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137590, "dur": 28, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137620, "dur": 36, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137660, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137662, "dur": 19, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137684, "dur": 35, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137721, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137756, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137758, "dur": 41, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137802, "dur": 62, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137867, "dur": 24, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137894, "dur": 24, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137920, "dur": 23, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137945, "dur": 36, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137982, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001137985, "dur": 39, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138027, "dur": 43, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138071, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138073, "dur": 25, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138102, "dur": 31, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138134, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138137, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138172, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138174, "dur": 34, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138212, "dur": 67, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138286, "dur": 4, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138292, "dur": 60, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138353, "dur": 2, "ph": "X", "name": "ProcessMessages 1044", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138356, "dur": 43, "ph": "X", "name": "ReadAsync 1044", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138401, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138405, "dur": 29, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138436, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138438, "dur": 26, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138466, "dur": 2, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138470, "dur": 25, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138498, "dur": 59, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138561, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138602, "dur": 29, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138632, "dur": 29, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138665, "dur": 76, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138744, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138746, "dur": 31, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138779, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138781, "dur": 36, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138820, "dur": 31, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138856, "dur": 24, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138882, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138884, "dur": 32, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138919, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138945, "dur": 51, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001138998, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139061, "dur": 3, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139066, "dur": 103, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139173, "dur": 2, "ph": "X", "name": "ProcessMessages 1125", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139177, "dur": 41, "ph": "X", "name": "ReadAsync 1125", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139223, "dur": 115, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139342, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139376, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139378, "dur": 15, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139394, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139396, "dur": 27, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139427, "dur": 52, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139483, "dur": 3, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139487, "dur": 38, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139527, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139530, "dur": 26, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139561, "dur": 37, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139601, "dur": 31, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139636, "dur": 19, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139657, "dur": 22, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139683, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139685, "dur": 32, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139721, "dur": 24, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139747, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139839, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139841, "dur": 39, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139883, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139884, "dur": 27, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139914, "dur": 23, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139941, "dur": 39, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001139982, "dur": 29, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140015, "dur": 42, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140060, "dur": 34, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140097, "dur": 29, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140129, "dur": 14, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140145, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140182, "dur": 31, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140214, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140216, "dur": 36, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140254, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140256, "dur": 25, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140282, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140284, "dur": 25, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140312, "dur": 53, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140367, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140369, "dur": 50, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140422, "dur": 26, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140451, "dur": 41, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140494, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140496, "dur": 24, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140523, "dur": 30, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140558, "dur": 26, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140587, "dur": 33, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140625, "dur": 37, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140666, "dur": 24, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140693, "dur": 24, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140719, "dur": 29, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140751, "dur": 27, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140781, "dur": 29, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140813, "dur": 55, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140870, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140871, "dur": 47, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140926, "dur": 3, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140930, "dur": 60, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140991, "dur": 1, "ph": "X", "name": "ProcessMessages 1016", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001140993, "dur": 32, "ph": "X", "name": "ReadAsync 1016", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141026, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141028, "dur": 33, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141064, "dur": 41, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141107, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141108, "dur": 38, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141150, "dur": 36, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141187, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141189, "dur": 40, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141234, "dur": 35, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141272, "dur": 37, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141312, "dur": 31, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141345, "dur": 31, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141379, "dur": 34, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141416, "dur": 39, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141458, "dur": 37, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141498, "dur": 23, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141523, "dur": 56, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141582, "dur": 38, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141621, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141623, "dur": 35, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141661, "dur": 89, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141754, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141756, "dur": 41, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141802, "dur": 2, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141805, "dur": 43, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141850, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141851, "dur": 26, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141880, "dur": 23, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141905, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141906, "dur": 39, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141948, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141968, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001141971, "dur": 26, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142000, "dur": 32, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142034, "dur": 29, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142066, "dur": 22, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142090, "dur": 18, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142110, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142113, "dur": 27, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142143, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142177, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142179, "dur": 25, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142206, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142235, "dur": 40, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142278, "dur": 32, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142313, "dur": 21, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142337, "dur": 21, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142360, "dur": 80, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142442, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142445, "dur": 31, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142478, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142480, "dur": 28, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142509, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142511, "dur": 25, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142539, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142541, "dur": 24, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142568, "dur": 88, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142661, "dur": 3, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142665, "dur": 37, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142705, "dur": 36, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142743, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142746, "dur": 61, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142810, "dur": 2, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142813, "dur": 29, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142846, "dur": 32, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142881, "dur": 43, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142927, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142929, "dur": 27, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142960, "dur": 26, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001142989, "dur": 25, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143018, "dur": 25, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143047, "dur": 26, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143076, "dur": 32, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143112, "dur": 40, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143155, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143158, "dur": 31, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143190, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143192, "dur": 29, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143224, "dur": 32, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143259, "dur": 27, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143289, "dur": 26, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143317, "dur": 23, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143343, "dur": 22, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143367, "dur": 47, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143417, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143437, "dur": 35, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143475, "dur": 26, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143503, "dur": 25, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143531, "dur": 26, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143560, "dur": 21, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143584, "dur": 74, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143661, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143662, "dur": 57, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143727, "dur": 3, "ph": "X", "name": "ProcessMessages 872", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143732, "dur": 49, "ph": "X", "name": "ReadAsync 872", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143782, "dur": 1, "ph": "X", "name": "ProcessMessages 1041", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143784, "dur": 36, "ph": "X", "name": "ReadAsync 1041", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143824, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143827, "dur": 37, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143867, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143921, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143922, "dur": 55, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001143980, "dur": 58, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144041, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144044, "dur": 57, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144108, "dur": 3, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144112, "dur": 79, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144195, "dur": 2, "ph": "X", "name": "ProcessMessages 1383", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144198, "dur": 31, "ph": "X", "name": "ReadAsync 1383", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144231, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144233, "dur": 31, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144268, "dur": 1, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144293, "dur": 41, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144338, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144340, "dur": 26, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144369, "dur": 42, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144412, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144414, "dur": 33, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144450, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144453, "dur": 77, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144532, "dur": 3, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144536, "dur": 37, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144574, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144576, "dur": 74, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144652, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144653, "dur": 36, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144691, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144692, "dur": 28, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144723, "dur": 24, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144749, "dur": 31, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144783, "dur": 26, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144812, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144843, "dur": 24, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144870, "dur": 23, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144895, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144919, "dur": 20, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144942, "dur": 31, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001144976, "dur": 34, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145012, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145013, "dur": 26, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145042, "dur": 25, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145069, "dur": 36, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145106, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145108, "dur": 31, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145142, "dur": 53, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145197, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145227, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145229, "dur": 38, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145270, "dur": 27, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145301, "dur": 140, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145450, "dur": 5, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145456, "dur": 89, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145550, "dur": 4, "ph": "X", "name": "ProcessMessages 2488", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145556, "dur": 49, "ph": "X", "name": "ReadAsync 2488", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145610, "dur": 2, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145613, "dur": 63, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145680, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145683, "dur": 58, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145744, "dur": 2, "ph": "X", "name": "ProcessMessages 1526", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145747, "dur": 91, "ph": "X", "name": "ReadAsync 1526", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145841, "dur": 2, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145844, "dur": 42, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145889, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145890, "dur": 40, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145931, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145933, "dur": 34, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001145970, "dur": 36, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146009, "dur": 30, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146041, "dur": 29, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146073, "dur": 30, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146105, "dur": 52, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146160, "dur": 40, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146202, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146203, "dur": 31, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146237, "dur": 39, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146278, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146317, "dur": 33, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146352, "dur": 31, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146386, "dur": 31, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146422, "dur": 32, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146457, "dur": 37, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146497, "dur": 37, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146536, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146537, "dur": 33, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146571, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146573, "dur": 47, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146623, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146625, "dur": 55, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146692, "dur": 3, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146697, "dur": 60, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146758, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146760, "dur": 27, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146789, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146824, "dur": 39, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146865, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146899, "dur": 38, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146940, "dur": 23, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146966, "dur": 28, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001146997, "dur": 76, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147081, "dur": 4, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147086, "dur": 55, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147143, "dur": 1, "ph": "X", "name": "ProcessMessages 1278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147145, "dur": 21, "ph": "X", "name": "ReadAsync 1278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147168, "dur": 27, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147199, "dur": 31, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147232, "dur": 24, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147258, "dur": 28, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147290, "dur": 22, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147314, "dur": 27, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147343, "dur": 118, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147466, "dur": 1, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147468, "dur": 31, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147500, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147502, "dur": 25, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147529, "dur": 37, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147569, "dur": 20, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147591, "dur": 29, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147623, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147656, "dur": 29, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147688, "dur": 39, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147730, "dur": 31, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147763, "dur": 22, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001147788, "dur": 529, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148322, "dur": 2, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148325, "dur": 118, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148445, "dur": 5, "ph": "X", "name": "ProcessMessages 5566", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148451, "dur": 89, "ph": "X", "name": "ReadAsync 5566", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148544, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148546, "dur": 66, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148619, "dur": 4, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148625, "dur": 88, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148717, "dur": 2, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148721, "dur": 72, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148797, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148800, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148858, "dur": 2, "ph": "X", "name": "ProcessMessages 994", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148862, "dur": 61, "ph": "X", "name": "ReadAsync 994", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148926, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148927, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148991, "dur": 2, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001148995, "dur": 36, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149032, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149034, "dur": 65, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149102, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149139, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149141, "dur": 31, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149174, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149176, "dur": 30, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149208, "dur": 26, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149237, "dur": 67, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149308, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149346, "dur": 34, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149381, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149383, "dur": 32, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149418, "dur": 69, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149489, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149530, "dur": 32, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149565, "dur": 30, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149597, "dur": 67, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149667, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149707, "dur": 35, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149745, "dur": 76, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149825, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149876, "dur": 46, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149925, "dur": 34, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001149962, "dur": 73, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150037, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150078, "dur": 43, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150123, "dur": 29, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150155, "dur": 83, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150241, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150283, "dur": 33, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150319, "dur": 66, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150388, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150427, "dur": 32, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150461, "dur": 30, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150494, "dur": 68, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150565, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150602, "dur": 43, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150646, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150648, "dur": 19, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150670, "dur": 76, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150749, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150788, "dur": 43, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150832, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150834, "dur": 73, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150909, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150944, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150945, "dur": 31, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001150980, "dur": 28, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151011, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151071, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151106, "dur": 35, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151143, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151144, "dur": 30, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151177, "dur": 69, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151249, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151288, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151289, "dur": 31, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151325, "dur": 28, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151355, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151357, "dur": 67, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151426, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151467, "dur": 41, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151510, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151511, "dur": 33, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151546, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151547, "dur": 69, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151619, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151655, "dur": 33, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151691, "dur": 36, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151729, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151731, "dur": 36, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151769, "dur": 47, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151826, "dur": 3, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151831, "dur": 44, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151878, "dur": 30, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151912, "dur": 26, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001151941, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152032, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152070, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152071, "dur": 70, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152143, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152145, "dur": 53, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152203, "dur": 41, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152247, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152287, "dur": 33, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152321, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152323, "dur": 35, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152359, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152361, "dur": 51, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152415, "dur": 32, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152448, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152450, "dur": 40, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152498, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152504, "dur": 46, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152553, "dur": 98, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152655, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152657, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152714, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152716, "dur": 34, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152754, "dur": 69, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152826, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152872, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152873, "dur": 44, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152920, "dur": 35, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152957, "dur": 32, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001152992, "dur": 34, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153028, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153029, "dur": 29, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153061, "dur": 28, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153092, "dur": 96, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153190, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153224, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153226, "dur": 37, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153266, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153268, "dur": 64, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153334, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153390, "dur": 3, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153394, "dur": 48, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153444, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153446, "dur": 37, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153486, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153521, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153523, "dur": 32, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153558, "dur": 36, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153597, "dur": 83, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153683, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153686, "dur": 69, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153758, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153798, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153800, "dur": 42, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153844, "dur": 28, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153874, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153876, "dur": 109, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001153988, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154076, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154078, "dur": 33, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154114, "dur": 29, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154146, "dur": 84, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154233, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154272, "dur": 31, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154306, "dur": 29, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154338, "dur": 75, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154416, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154454, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154456, "dur": 29, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154487, "dur": 28, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154521, "dur": 59, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154583, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154616, "dur": 59, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154677, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154722, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154724, "dur": 36, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154761, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154763, "dur": 78, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154842, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154844, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154879, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154880, "dur": 32, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154915, "dur": 43, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154963, "dur": 33, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001154999, "dur": 69, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155070, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155111, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155112, "dur": 33, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155148, "dur": 67, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155218, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155255, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155257, "dur": 30, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155290, "dur": 58, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155352, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155384, "dur": 67, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155452, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155454, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155496, "dur": 36, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155535, "dur": 30, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155568, "dur": 65, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155635, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155704, "dur": 3, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155709, "dur": 51, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155762, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155764, "dur": 72, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155841, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155886, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155888, "dur": 75, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155970, "dur": 2, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001155974, "dur": 81, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156059, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156121, "dur": 1, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156123, "dur": 79, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156206, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156241, "dur": 82, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156326, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156379, "dur": 41, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156422, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156423, "dur": 33, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156459, "dur": 69, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156531, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156572, "dur": 44, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156617, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156619, "dur": 39, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156664, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156668, "dur": 53, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156725, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156775, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156776, "dur": 41, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156820, "dur": 2, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156823, "dur": 18, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156843, "dur": 72, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156918, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156955, "dur": 33, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001156991, "dur": 78, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157071, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157122, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157124, "dur": 106, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157234, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157336, "dur": 2, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157339, "dur": 39, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157382, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157427, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157472, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157474, "dur": 36, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157512, "dur": 60, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157575, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157615, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157616, "dur": 32, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157651, "dur": 32, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157686, "dur": 74, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157762, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157801, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157803, "dur": 96, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157901, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157941, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001157942, "dur": 81, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158026, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158075, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158076, "dur": 34, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158112, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158114, "dur": 33, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158149, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158151, "dur": 79, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158238, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158243, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158322, "dur": 2, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158326, "dur": 50, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158379, "dur": 2, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158383, "dur": 55, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158442, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158510, "dur": 2, "ph": "X", "name": "ProcessMessages 1201", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158513, "dur": 65, "ph": "X", "name": "ReadAsync 1201", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158580, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158618, "dur": 35, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158656, "dur": 36, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158693, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158695, "dur": 74, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158771, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158833, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158834, "dur": 45, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158882, "dur": 62, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158949, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158988, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001158990, "dur": 34, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159027, "dur": 31, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159061, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159130, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159170, "dur": 37, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159212, "dur": 59, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159273, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159275, "dur": 108, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159386, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159388, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159429, "dur": 35, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159466, "dur": 47, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159515, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159518, "dur": 78, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159598, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159651, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159654, "dur": 44, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159700, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159701, "dur": 45, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159750, "dur": 32, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159783, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159785, "dur": 25, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159813, "dur": 58, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159875, "dur": 77, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159955, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159994, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001159997, "dur": 48, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160051, "dur": 3, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160056, "dur": 44, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160105, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160150, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160153, "dur": 60, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160218, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160262, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160268, "dur": 36, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160306, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160308, "dur": 49, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160362, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160365, "dur": 45, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160412, "dur": 2, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160417, "dur": 55, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160474, "dur": 2, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160480, "dur": 63, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160547, "dur": 34, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160585, "dur": 68, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160655, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160657, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160697, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160700, "dur": 38, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160740, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160743, "dur": 34, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160779, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160780, "dur": 24, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160806, "dur": 26, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160838, "dur": 51, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160891, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160893, "dur": 43, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160943, "dur": 4, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160948, "dur": 40, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001160992, "dur": 23, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001161018, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001161020, "dur": 101, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001161125, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001161160, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001161162, "dur": 265, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001161437, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001161443, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001161522, "dur": 410, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162258, "dur": 113, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162377, "dur": 5, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162384, "dur": 55, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162443, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162448, "dur": 45, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162500, "dur": 4, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162506, "dur": 36, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162544, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162546, "dur": 35, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162585, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162588, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162642, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162645, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162673, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162713, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162716, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162760, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162763, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162828, "dur": 3, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162835, "dur": 32, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162869, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162871, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162914, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162918, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162957, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001162960, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163029, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163081, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163085, "dur": 55, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163144, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163148, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163195, "dur": 4, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163201, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163245, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163247, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163283, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163287, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163337, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163341, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163412, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163415, "dur": 32, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163449, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163450, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163487, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163490, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163534, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163537, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163585, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163590, "dur": 43, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163636, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163639, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163685, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163688, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163738, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163742, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163808, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163811, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163853, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163855, "dur": 47, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163906, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163911, "dur": 61, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163976, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001163980, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164027, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164030, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164076, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164080, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164116, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164153, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164156, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164201, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164204, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164258, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164263, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164313, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164316, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164346, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164354, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164389, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164391, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164433, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164437, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164489, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164493, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164539, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164542, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164595, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164598, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164650, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164656, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164698, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164700, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164742, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164745, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164786, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164788, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164842, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164845, "dur": 32, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164879, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164883, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164941, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164943, "dur": 44, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164992, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001164995, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165048, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165052, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165091, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165095, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165139, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165142, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165182, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165186, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165274, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165278, "dur": 68, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165351, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165354, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165408, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165411, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165464, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165467, "dur": 66, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165540, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165545, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165598, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165603, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165642, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165644, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165684, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165687, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165743, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165745, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165819, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165824, "dur": 53, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165879, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165881, "dur": 42, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165930, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165933, "dur": 40, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165975, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001165978, "dur": 38, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166021, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166025, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166085, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166090, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166148, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166153, "dur": 131, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166291, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166297, "dur": 65, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166366, "dur": 3, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166371, "dur": 52, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166426, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166429, "dur": 46, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166480, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166484, "dur": 48, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166535, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166538, "dur": 45, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166585, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166588, "dur": 40, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166630, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166632, "dur": 38, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166676, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166680, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166741, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166745, "dur": 52, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166802, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166806, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166852, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166855, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166908, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166912, "dur": 50, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166972, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001166976, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167017, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167022, "dur": 42, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167069, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167073, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167103, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167105, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167140, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167191, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167195, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167237, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167239, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167284, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167287, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167338, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167342, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167391, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167394, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167421, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167423, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167465, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167470, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167513, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167518, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167567, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167570, "dur": 53, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167627, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167630, "dur": 48, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167683, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167685, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167731, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167734, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167777, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167780, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167840, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167842, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167885, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167889, "dur": 37, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167929, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167931, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167952, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001167975, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168022, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168026, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168081, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168086, "dur": 101, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168192, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168194, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168341, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168343, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168401, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168405, "dur": 75, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168483, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168487, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168519, "dur": 291, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168819, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168824, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168876, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001168878, "dur": 17095, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001185982, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001185987, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001186081, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001186085, "dur": 155, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001186246, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001186249, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001186299, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001186302, "dur": 661, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001186966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001186968, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001187021, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001187023, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001187076, "dur": 137, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001187215, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001187243, "dur": 374, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001187618, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001187621, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001187665, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001187669, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001187719, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001187725, "dur": 234, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001187962, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001187965, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188008, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188010, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188051, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188055, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188142, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188146, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188205, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188208, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188244, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188247, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188333, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188335, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188368, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188370, "dur": 337, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188711, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188713, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188748, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188750, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188781, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188784, "dur": 167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188953, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001188955, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189000, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189005, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189047, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189049, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189093, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189095, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189174, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189202, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189249, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189250, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189285, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189288, "dur": 43, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189336, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189339, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189365, "dur": 28, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189398, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189432, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189435, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189469, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189471, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189521, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189524, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189559, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189561, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189627, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189657, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189659, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189694, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189781, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189783, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189843, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189845, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189986, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001189988, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190032, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190035, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190085, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190087, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190119, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190244, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190247, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190285, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190287, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190331, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190358, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190405, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190411, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190449, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190451, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190495, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190498, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190521, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190551, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190553, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190601, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190603, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190639, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190744, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190747, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190783, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190820, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190822, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190938, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190940, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190976, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001190978, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191046, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191048, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191089, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191091, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191136, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191171, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191174, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191296, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191298, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191336, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191340, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191384, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191424, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191427, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191487, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191489, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191545, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191548, "dur": 176, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191728, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191730, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191783, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191786, "dur": 66, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191857, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191904, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191906, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191947, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191949, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191985, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001191987, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192029, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192069, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192072, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192107, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192109, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192162, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192164, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192204, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192206, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192263, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192265, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192309, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192312, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192341, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192343, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192373, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192375, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192411, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192563, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192565, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192607, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192609, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192650, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192652, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192685, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192724, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192726, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192763, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192815, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192818, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192869, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192873, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192909, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192911, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192949, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192985, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001192988, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193023, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193067, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193069, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193107, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193109, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193154, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193195, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193197, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193265, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193300, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193338, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193374, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193508, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193558, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193560, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193596, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193598, "dur": 208, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193819, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193824, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193875, "dur": 5, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193881, "dur": 87, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193971, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001193973, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001194111, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001194114, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001194144, "dur": 776, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001194929, "dur": 132, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001195067, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001195072, "dur": 781, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001195863, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001195867, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001195914, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001195976, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001195981, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196054, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196057, "dur": 60, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196120, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196122, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196187, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196190, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196256, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196260, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196314, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196316, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196374, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196377, "dur": 489, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196875, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196879, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196928, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001196930, "dur": 105, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001197038, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001197040, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001197087, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001197089, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001197135, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001197138, "dur": 183, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001197324, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001197326, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001197376, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001197379, "dur": 586, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001197971, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001197975, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001198052, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001198055, "dur": 48, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001198109, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001198113, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001198184, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001198186, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001198238, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001198240, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001198277, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001198280, "dur": 279, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001198562, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001198607, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001198609, "dur": 292, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001198905, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001198939, "dur": 138, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001199080, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001199117, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001199184, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001199186, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001199231, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001199232, "dur": 399, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001199636, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001199639, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001199697, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001199700, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001199754, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001199877, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001199880, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001199935, "dur": 761, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001200701, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001200704, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001200743, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001200746, "dur": 61719, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001262480, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001262486, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001262607, "dur": 3621, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001266238, "dur": 13609, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001279856, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001279860, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001279937, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001279939, "dur": 417, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001280366, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001280370, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001280409, "dur": 287, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001280704, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001280708, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001280768, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001280773, "dur": 1459, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282248, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282252, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282308, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282310, "dur": 56, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282372, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282375, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282408, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282477, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282479, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282528, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282530, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282570, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282678, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282680, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282729, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282731, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282804, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282867, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001282869, "dur": 310, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001283193, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001283197, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001283233, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001283235, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001283422, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001283424, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001283477, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001283479, "dur": 1294, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001284784, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001284789, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001284862, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001284866, "dur": 142, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001285011, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001285013, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001285050, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001285052, "dur": 191, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001285245, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001285247, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001285290, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001285292, "dur": 179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001285475, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001285516, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001285518, "dur": 444, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001285966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001285968, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001286021, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001286024, "dur": 787, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001286818, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001286822, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001286888, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001286892, "dur": 394, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001287293, "dur": 1, "ph": "X", "name": "ProcessMessages 10", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001287341, "dur": 61, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001287411, "dur": 310, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001287725, "dur": 49, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001287778, "dur": 170, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001287953, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001287955, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001288023, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001288028, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001288084, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001288087, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001288181, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001288217, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001288224, "dur": 502, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001288737, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001288741, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001288808, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001288811, "dur": 1123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001289943, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001289947, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001289996, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001289998, "dur": 185, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001290192, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001290196, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001290259, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001290262, "dur": 257, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001290526, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001290529, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001290594, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001290597, "dur": 584, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001291193, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001291197, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001291241, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001291243, "dur": 1089, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292342, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292347, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292431, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292435, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292492, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292494, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292544, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292546, "dur": 227, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292777, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292820, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292822, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292856, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292858, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292905, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292907, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292955, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292957, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001292999, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293001, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293047, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293049, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293086, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293132, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293134, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293175, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293251, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293253, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293290, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293292, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293351, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293354, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293392, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293394, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293428, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293430, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293464, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293466, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293511, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293512, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293546, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293616, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293618, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293664, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293667, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293721, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293723, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293776, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293778, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293808, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293849, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293851, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293873, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293908, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293910, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293951, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293954, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001293997, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294000, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294040, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294043, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294082, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294086, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294128, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294131, "dur": 43, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294177, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294180, "dur": 46, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294229, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294232, "dur": 30, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294265, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294267, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294300, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294344, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294346, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294395, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294397, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294436, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294439, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294479, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294481, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294525, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294528, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294570, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294572, "dur": 52, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294627, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294630, "dur": 29, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294663, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294702, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294704, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294740, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294769, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294771, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294801, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294834, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294867, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294896, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294898, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294937, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294940, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294980, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001294983, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001295017, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001295019, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001295055, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001295057, "dur": 669, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001295743, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001295748, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001295856, "dur": 5, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001295863, "dur": 49, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001295916, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001295988, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001296063, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001296113, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001296163, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001296165, "dur": 157135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001453312, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001453316, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001453358, "dur": 23, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001453382, "dur": 19043, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001472436, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001472440, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001472473, "dur": 123274, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001595757, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001595762, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001595865, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001595870, "dur": 305, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001596185, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001596189, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001596267, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001596273, "dur": 96577, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001692862, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001692866, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001692912, "dur": 28, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001692942, "dur": 13993, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001706945, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001706950, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001707013, "dur": 24, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001707038, "dur": 18354, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001725406, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001725412, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001725462, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001725463, "dur": 8654, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001734128, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001734135, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001734194, "dur": 6404, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001740607, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001740611, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001740644, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001740647, "dur": 2188, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001742845, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001742849, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001742883, "dur": 31, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001742915, "dur": 98603, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001841528, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001841533, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001841574, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001841577, "dur": 1190, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001842782, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001842787, "dur": 104, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001842900, "dur": 34, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001842936, "dur": 701, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001843647, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001843652, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001843720, "dur": 704, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311001844433, "dur": 11124, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 19704, "tid": 2397, "ts": 1751311001874919, "dur": 2352, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 19704, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 19704, "tid": 8589934592, "ts": 1751311001118650, "dur": 125342, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 19704, "tid": 8589934592, "ts": 1751311001243998, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 19704, "tid": 8589934592, "ts": 1751311001244006, "dur": 1706, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 19704, "tid": 2397, "ts": 1751311001877276, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 19704, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 19704, "tid": 4294967296, "ts": 1751311001093074, "dur": 764235, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751311001097630, "dur": 10351, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751311001857397, "dur": 6465, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751311001860664, "dur": 57, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751311001863968, "dur": 23, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 19704, "tid": 2397, "ts": 1751311001877285, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751311001126734, "dur": 63, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311001126823, "dur": 3127, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311001129989, "dur": 975, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311001131121, "dur": 81, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751311001131202, "dur": 541, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311001132249, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_0E82E95F1D4B1B8B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751311001132704, "dur": 212, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9843DD54C5E6159E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751311001133845, "dur": 2448, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E689A207C3E7A0C2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751311001137726, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751311001139415, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751311001141379, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751311001143294, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751311001143991, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751311001144253, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751311001145758, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751311001146059, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751311001147109, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751311001147376, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751311001148984, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751311001151679, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751311001154468, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751311001155611, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751311001159216, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751311001159858, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751311001161595, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751311001162014, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751311001131787, "dur": 30962, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311001162766, "dur": 681614, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311001844382, "dur": 360, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311001844762, "dur": 64, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311001845013, "dur": 117, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311001845185, "dur": 1576, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751311001132182, "dur": 30603, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001162825, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001163026, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FECC8B8B813FB02A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001163620, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001163695, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_8DE949524442C9D8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001163894, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001163976, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_E5318FF293833B30.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001164092, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7E47E9336205E31D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001164187, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_EE062F8861099912.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001164246, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001164307, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D7031BE9F197A0A6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001164409, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D7031BE9F197A0A6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001164461, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F0EAC96F0603CD08.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001164647, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_D8FCB30ADD921A73.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001164869, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_F21194D2F617C9ED.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001164982, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_C01CA8838C1CE31C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001165089, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A28CE54978CD971D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001165182, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1948650C4D21EE20.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001165233, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001165335, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_12124AFB436F9D5D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001165405, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001165499, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_0D77228FB13B7FB6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001165754, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001165837, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001165921, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751311001166031, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_3D651E01942E03F8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001166186, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001166312, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001166465, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751311001166629, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001166772, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001166909, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751311001167292, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001167488, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001167651, "dur": 280, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751311001167977, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001168029, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751311001168427, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751311001168633, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001168815, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001169134, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001169409, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001169671, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001170227, "dur": 2095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001172323, "dur": 1790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001174114, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001174942, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001175188, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001175425, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001176190, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001176458, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001176723, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001177438, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001177695, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001177948, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001178305, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001178642, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001178882, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001179190, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001179493, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001179752, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001180131, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001180399, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001180632, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001180993, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001181237, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001181493, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001181856, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001182108, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001182465, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001182730, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001183190, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001184056, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001184898, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001185472, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001186017, "dur": 1958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001187976, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001188648, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001189357, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001189521, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001189607, "dur": 269, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001189878, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751311001190528, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001190933, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001191037, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001191535, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001191623, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001191813, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001191977, "dur": 870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751311001192848, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001193019, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001193094, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001193343, "dur": 1222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751311001194566, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001194708, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001194784, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001195185, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311001195372, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001195571, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751311001196111, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001196229, "dur": 55047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001251281, "dur": 27629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001278925, "dur": 2375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751311001281302, "dur": 979, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001282299, "dur": 2298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751311001284599, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001284781, "dur": 2230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751311001287012, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001287097, "dur": 2385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751311001289483, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001289560, "dur": 2156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751311001291790, "dur": 2341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751311001294133, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001294390, "dur": 2830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751311001297222, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001297299, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311001297355, "dur": 547019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001132326, "dur": 30514, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001162853, "dur": 929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_5F13DCF03A10B823.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001163783, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001163848, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_B83C6071BB394334.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001163912, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001163980, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_595F986B2A245834.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001164069, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001164152, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_A164BAE5670FC544.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001164256, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001164397, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_DB48149D0776AF57.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001164510, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_472976495F1230C7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001164604, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_472976495F1230C7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001164713, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_1A2A3B79BD3B4CFD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001165078, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_5FDF6992734E0361.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001165137, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001165187, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_5FDF6992734E0361.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001165277, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_1638DBF3D84589E5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001165361, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001165471, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_E106F825F731F4BC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001165631, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_07FA64190ECA4CC1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001165702, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001165891, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001165971, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001166112, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001166198, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001166255, "dur": 562, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751311001166821, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001166905, "dur": 280, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751311001167202, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001167331, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751311001167414, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001167533, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001167607, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001167670, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751311001167852, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751311001167954, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751311001168125, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001168175, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751311001168292, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001168431, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751311001168646, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751311001168833, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001168900, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751311001169035, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751311001169088, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001169182, "dur": 566, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751311001169751, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001170437, "dur": 1807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001172245, "dur": 1935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001174181, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001175002, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001175675, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001176235, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001176547, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001176777, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001177455, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001177687, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001178225, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001178611, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001178839, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001179080, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001179413, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001179685, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001179987, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001180251, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001180492, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001180802, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001181106, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001181370, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001181618, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001182064, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001182463, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001183155, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001184335, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001185456, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001185709, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001186177, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001186461, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001186906, "dur": 760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001187667, "dur": 939, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001188661, "dur": 909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001189571, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001189851, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001189948, "dur": 1708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751311001191657, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001192055, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751311001192792, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001193085, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001193339, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751311001194129, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001194274, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001194451, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001194607, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001194770, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001195258, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751311001195995, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001196093, "dur": 5217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001201312, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311001201498, "dur": 82988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001284488, "dur": 4500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751311001288989, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001289061, "dur": 8336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751311001297399, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311001297509, "dur": 546875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001132471, "dur": 30442, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001162927, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7B82E1658E753B21.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001163500, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001163598, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_467B09015CCE177C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001163702, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001163786, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_15A0A72BE0744047.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001164524, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_FE92BD4D4925733A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001164617, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_FE92BD4D4925733A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001164756, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_7DC1C7578DCAB919.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001164949, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E689A207C3E7A0C2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001165046, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001165118, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_815CA55F1B3FC2AD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001165222, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_20A6B658F2F96E47.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001165376, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A587944D9110748A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001165617, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001165852, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001165907, "dur": 21574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311001187628, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001187833, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311001188687, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001188831, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311001189337, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001189596, "dur": 865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311001190462, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001190676, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001190949, "dur": 1393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311001192437, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311001192659, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311001193607, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001193714, "dur": 1211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1751311001194967, "dur": 189, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001195994, "dur": 68056, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1751311001278885, "dur": 2415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751311001281302, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001281403, "dur": 2266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751311001283672, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001283784, "dur": 2465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751311001286251, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001286333, "dur": 2466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751311001288801, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001288969, "dur": 2402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751311001291373, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001291510, "dur": 2396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751311001293907, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001293988, "dur": 3078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751311001297067, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001297245, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311001297534, "dur": 546858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001132249, "dur": 30558, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001162825, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001163104, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F697CEAF687CB7A2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311001163703, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001163832, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F27EBD72F4881481.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311001164037, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001164173, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A619838E516A75AC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311001164332, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001164404, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8B3842F7B08DFF89.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311001164634, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001164822, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DA7D4247AC2DB3F7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311001165099, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001165218, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AFE94C31B12D3213.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311001165316, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001165393, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AFE94C31B12D3213.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311001165488, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_7AC1C063560F2BC6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311001165983, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001166168, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001166390, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001166561, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001166750, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001166843, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751311001167005, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001167062, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001167166, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001167232, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001167471, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001167543, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001167628, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751311001167837, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751311001168110, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001168414, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751311001168470, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751311001168557, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001168622, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751311001168781, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001169101, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001169180, "dur": 288, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751311001169505, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001169659, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001170374, "dur": 2510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001172885, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001173162, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001173369, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001173659, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001173881, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001174170, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001174937, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001175191, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001175429, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001176346, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001176573, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001176816, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001177583, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001177934, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001178672, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001178936, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001179278, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001179542, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001179782, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001180254, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001180531, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001180864, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001181157, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001181456, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001181807, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001182389, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001182949, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001183683, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001184682, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001185578, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001186015, "dur": 1917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001187933, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001188662, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001189345, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311001189515, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001189580, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751311001190238, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001190384, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311001190693, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311001190946, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001191014, "dur": 950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751311001191966, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001192218, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311001192473, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001192554, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311001192744, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001192936, "dur": 1177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751311001194114, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001194642, "dur": 780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001195423, "dur": 5127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001200553, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311001200687, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001200784, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751311001201241, "dur": 77718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001278970, "dur": 2330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751311001281302, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001281397, "dur": 2273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751311001283672, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001283769, "dur": 4559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751311001288329, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001288410, "dur": 3345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751311001291803, "dur": 2523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751311001294328, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001294523, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001294751, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751311001294802, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001294867, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001294970, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001295121, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751311001295183, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751311001295695, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001295863, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001296042, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001296187, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001296541, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001296618, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001297341, "dur": 444515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311001741894, "dur": 218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751311001741859, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751311001742152, "dur": 2253, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751311001744411, "dur": 99976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001132297, "dur": 30523, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001162832, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_82F8B03BA9650A81.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001163557, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001163624, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_1182EE28E68C62C8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001163733, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001163799, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_1DC8148833156840.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001163969, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_8EA224AC6679B7C2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001164043, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001164108, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_526B70D8724FDBD0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001164168, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001164230, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_60AF5DD29DC33809.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001164296, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001164414, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_658DA73782B14E20.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001164589, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_658DA73782B14E20.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001164677, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_8FE2697BF98A2193.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001164998, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_C1BEC5DDD68DA9AF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001165059, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001165140, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FEA05FC664382286.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001165265, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_1FC72A0013DC4EEB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001165360, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001165806, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001166033, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001166086, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751311001166149, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001166452, "dur": 361, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751311001166859, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751311001167056, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001167298, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001167457, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751311001167518, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001167659, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001167956, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751311001168075, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001168147, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001168537, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001168776, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001168972, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751311001169088, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001169465, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751311001169523, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001169992, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751311001170106, "dur": 2159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001172266, "dur": 1913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001174180, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001175220, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001176106, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001176357, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001176585, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001176829, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001177920, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001178186, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001178574, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001178822, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001179065, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001179429, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001179672, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001179929, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001180271, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001180516, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001180805, "dur": 836, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Runtime\\Debugging\\DebugUpdater.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751311001180765, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001181914, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001182183, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001182743, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001183058, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001183496, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001184684, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001185854, "dur": 1955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001187810, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001188630, "dur": 937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001189569, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001189745, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001189824, "dur": 1078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751311001190903, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001191090, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001191302, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001191600, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001191795, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001191860, "dur": 1100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751311001192962, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001193039, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751311001193098, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001193316, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001193467, "dur": 1160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751311001194628, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001194801, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001194871, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001195190, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001195377, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001195440, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751311001196194, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001196316, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001196372, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001196594, "dur": 906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751311001197501, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001197671, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001197736, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001197879, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001197959, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751311001198562, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001198689, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311001198880, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001198963, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751311001199742, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001199881, "dur": 79061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001278954, "dur": 2388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751311001281344, "dur": 2783, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001284141, "dur": 2621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751311001286764, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001286870, "dur": 2580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751311001289452, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001289686, "dur": 3002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751311001292691, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001292765, "dur": 3133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751311001295900, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001296316, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001296594, "dur": 719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311001297351, "dur": 547027, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001132397, "dur": 30469, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001162881, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_256223B421F6FD72.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001163500, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001163621, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_E681FA607883332E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001163747, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_D32EF602AAA54BE6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001163890, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001163972, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_51B6A25823037467.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001164039, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001164104, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_67CB883191F4D66D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001164201, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0FEAE2B241ACD344.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001164304, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_BFD5601BA05C7CD8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001164402, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_BEB6379C07D3D185.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001164461, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001164528, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_433711FCB6016F00.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001164617, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_433711FCB6016F00.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001164736, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_77F91A8BF54EFB7C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001164940, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001165046, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_48483D327EA17808.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001165131, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001165213, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_1FF1C2755BAA46D4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001165283, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001165335, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_1FF1C2755BAA46D4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001165436, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001165525, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001165623, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001165692, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311001165807, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311001166223, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311001166366, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001166439, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751311001166640, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311001166849, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311001167043, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001167101, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001167411, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311001167571, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001167655, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751311001167943, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001168039, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751311001168327, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001168394, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311001168522, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001168624, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001169005, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001169174, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311001169321, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311001169443, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1177933628359149393.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311001169514, "dur": 513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001170030, "dur": 369, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1177933628359149393.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311001170402, "dur": 2036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001172439, "dur": 2043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001174483, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001174817, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001175077, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001175406, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001176222, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001176579, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001176810, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001177475, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001177722, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001178011, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001178265, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001178669, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001178905, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001179173, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001179482, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001179751, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001180112, "dur": 735, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Meta\\EditorPrefMetadata.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751311001180042, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001181040, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001181286, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001181541, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001181879, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001182188, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001182565, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001182808, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001183136, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001183908, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001185001, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001185283, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001185747, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001186179, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001186445, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001186695, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001187151, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001187646, "dur": 966, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001188613, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001189331, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001189521, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001189601, "dur": 1190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751311001190793, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001190975, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001191305, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001191579, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001191720, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001191916, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001192024, "dur": 1471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751311001193496, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001193700, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001193825, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001194187, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001194780, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751311001195268, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001195408, "dur": 2186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001197599, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001197777, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001197883, "dur": 1663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751311001199547, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001199633, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001199696, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001199833, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751311001200441, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001200542, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001200694, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751311001201302, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311001201480, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751311001202236, "dur": 56, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001203624, "dur": 251259, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751311001461824, "dur": 12030, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751311001461424, "dur": 12503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751311001473928, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001474007, "dur": 119528, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751311001474004, "dur": 121455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751311001597240, "dur": 468, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311001598655, "dur": 95766, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751311001726449, "dur": 444, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751311001726431, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751311001726948, "dur": 117470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001132453, "dur": 30433, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001162900, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_CC3B7EF1A2C4EA40.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311001163714, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_857E61E190401437.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311001163800, "dur": 635, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001164436, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_857E61E190401437.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311001164698, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D7CA9FED890DDAB1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311001164995, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_0B2798972D9D68EB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311001165265, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001165425, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_0B2798972D9D68EB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311001165671, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001165742, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001165839, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001165999, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311001166055, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001166253, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311001166336, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311001166473, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311001166661, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311001166847, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751311001167050, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001167110, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001167229, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001167462, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001167527, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001167625, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751311001167832, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311001168207, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001168436, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311001168498, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311001168635, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751311001168754, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001168851, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311001168942, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311001169025, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001169114, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001169658, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001169987, "dur": 2139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001172128, "dur": 2075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001174204, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001175060, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001175404, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001176220, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001176497, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001176747, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001177436, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001177696, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001178134, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001178409, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001178713, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001178949, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001179316, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001179573, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001179817, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001180074, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001180307, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001180933, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001181162, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001181412, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001181662, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001182263, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001182937, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001183254, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001184033, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001184969, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001185319, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001185576, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001186014, "dur": 1878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001187892, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001188626, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001189349, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311001189515, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001189626, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311001189754, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751311001190316, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001190647, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311001190893, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001190995, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311001191118, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751311001191788, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001191877, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001191949, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751311001192809, "dur": 925, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001193805, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001193878, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001193937, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311001194679, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751311001195231, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001195403, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001195618, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311001195916, "dur": 1562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751311001197479, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001197675, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311001197886, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751311001198623, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001198746, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311001198933, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751311001199467, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001199589, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311001199780, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751311001200208, "dur": 81129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001281361, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751311001283747, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001283817, "dur": 2445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751311001286263, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001286351, "dur": 2448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751311001288801, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001288962, "dur": 2478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751311001291443, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001291532, "dur": 2527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751311001294061, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001294124, "dur": 2944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751311001297070, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001297241, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001297318, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311001297730, "dur": 546683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001132585, "dur": 30349, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001162946, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_8A89D5AFAE49E63E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001163702, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001163778, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_14A5559FFED55C50.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001163838, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001163919, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_33AFB99E7761AA5C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001164026, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_4E1AB9DAA17812DB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001164209, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BA0CB78B239CCACF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001164467, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_209987431F1A3EDC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001164638, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001164815, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7B8E45163E4907D2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001164930, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_EAAAA0867963BCF7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001164994, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001165059, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_CBABBF352E91654E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001165173, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3EF742C8CA4C5E1E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001165233, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001165372, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3EF742C8CA4C5E1E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001165462, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001165811, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001166147, "dur": 21265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751311001187415, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001187621, "dur": 993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001188614, "dur": 711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001189329, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001189511, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001189647, "dur": 2283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751311001191932, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001192374, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001192699, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001192903, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001193071, "dur": 1962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751311001195035, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001195183, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001195417, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751311001196088, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001196221, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001196468, "dur": 969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751311001197438, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001197586, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311001197728, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001197794, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751311001198320, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001198450, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001198515, "dur": 80351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001278871, "dur": 2472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751311001281345, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001281425, "dur": 2476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751311001283902, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001284420, "dur": 2059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751311001286480, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001286622, "dur": 2281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751311001288904, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001288965, "dur": 2475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751311001291451, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001291576, "dur": 2481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751311001294059, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001294127, "dur": 2938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751311001297066, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001297333, "dur": 429109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311001726488, "dur": 9114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751311001726446, "dur": 9159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751311001735651, "dur": 108743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001132665, "dur": 30286, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001162963, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_A1485D01311C863E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001163513, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001163603, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_69FC355D1BD4BB29.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001163682, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001163790, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_0E07881F85CDD302.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001164147, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_308026626194FDB8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001164246, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_7676AB112B74F121.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001164350, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8D9A2E8777AB6882.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001164434, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8D9A2E8777AB6882.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001164546, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1FE2264601546D0E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001164653, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1FE2264601546D0E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001164807, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_93B77B82222D1B4A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001165013, "dur": 273, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_93B77B82222D1B4A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001165289, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_486DF76931BD0557.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001165395, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_486DF76931BD0557.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001165589, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001165863, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001165950, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001166165, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001166471, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751311001166656, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751311001166718, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001166796, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001166856, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751311001166990, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001167051, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001167417, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001167507, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001167739, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751311001167837, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751311001168156, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001168222, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001168336, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001168537, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001168733, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001168796, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001169493, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001169655, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001170024, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751311001170131, "dur": 1521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001171653, "dur": 1996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001173650, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001173866, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001174083, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001174695, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001175076, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001175474, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001176170, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001176487, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001176728, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001177430, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001177676, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001178010, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001178313, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001178619, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001178856, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001179125, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001179394, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001179646, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001179913, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001180208, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001180454, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001180683, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001180950, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001181195, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001181443, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001181852, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001182196, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001182535, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001182776, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001183063, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001183368, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001184296, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001185252, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001185617, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001186042, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001187209, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001187633, "dur": 994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001188628, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001189325, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001189491, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001189577, "dur": 894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751311001190472, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001190640, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001190863, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001190977, "dur": 890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751311001191940, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001192064, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751311001192133, "dur": 758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751311001192892, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001193101, "dur": 1450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751311001194552, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001194738, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001195416, "dur": 3335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001198753, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311001198906, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001198971, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751311001199457, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001199570, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001199674, "dur": 79197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001278905, "dur": 2395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751311001281302, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001281515, "dur": 2232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751311001283749, "dur": 1258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001285027, "dur": 2434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751311001287462, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001287577, "dur": 2574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751311001290153, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001290316, "dur": 3451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751311001293769, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001293898, "dur": 3516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751311001297415, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311001297703, "dur": 546679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001132761, "dur": 30222, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001162991, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_0E82E95F1D4B1B8B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001163601, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_F8CCEDBE36EB62B5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001163684, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001163782, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_B566D0CB2E423B86.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001163901, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9843DD54C5E6159E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001163998, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_9D64899DE49D3B06.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001164112, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_0EEB0D491376C433.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001164220, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_91BF37D1F055EC15.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001164310, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_DBB153F07C192DFF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001164385, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E04F87D224606974.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001164572, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E04F87D224606974.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001164650, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_BB587A5119495C88.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001164832, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001164915, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_9870EDF59A107BDF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001165056, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_584BDBB0AACB340C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001165157, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_80843626DE95EA13.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001165212, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001165394, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_80843626DE95EA13.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001165577, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001165757, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001165853, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751311001165954, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001166087, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751311001166195, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001166410, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001166514, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001166600, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751311001166883, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751311001167067, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001167119, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751311001167259, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001167453, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751311001167553, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001167783, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751311001167914, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001168107, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001168252, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001168647, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751311001168946, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751311001169103, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001169170, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751311001169286, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001169669, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001169979, "dur": 2206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001172186, "dur": 2093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001174280, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001175133, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001175389, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001176064, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001176513, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001176787, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001177442, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001177700, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001178503, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001178740, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001178982, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001179302, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001179552, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001179862, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001180143, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001180395, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001180615, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001180962, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001181207, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001181499, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001181837, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001182634, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001183040, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001183307, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001184221, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001185086, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001185357, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001185677, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001186239, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001186502, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001186779, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001186917, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001187577, "dur": 1038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001188616, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001189334, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001189499, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001189560, "dur": 391, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001189953, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751311001190521, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001190876, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001191023, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001191277, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001191400, "dur": 1238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751311001192639, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001192760, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001192940, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001193000, "dur": 2302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751311001195304, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001195492, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001195715, "dur": 1541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751311001197258, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001197429, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001197502, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311001197669, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001197744, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751311001198351, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001198478, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001198539, "dur": 80323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001278864, "dur": 3299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751311001282164, "dur": 1876, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001284077, "dur": 2298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751311001286422, "dur": 2196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751311001288619, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001288885, "dur": 5008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751311001293894, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001294050, "dur": 3161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751311001297213, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311001297343, "dur": 547033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001132795, "dur": 30203, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001163008, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_44462716F1E6187F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001163654, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_596B249A76101BE6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001163792, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001163851, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DAD9D37CE1A4BA55.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001164097, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C8A867A03915DFB5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001164289, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001164372, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_64D9AC7F4C4AC8A8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001164481, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3E1DF6609ACAC61F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001164575, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001164631, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3E1DF6609ACAC61F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001164788, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B6153E13308654D1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001165010, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B6153E13308654D1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001165142, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_7E691CD5ECB673D9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001165372, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001165478, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001165538, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_BFEF6BF3A445E475.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001165674, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7F3203F2669A3DC6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001165780, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001166010, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001166175, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001166368, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001166431, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751311001166761, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001166876, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751311001167127, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001167403, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751311001167567, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001167632, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751311001167729, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001167796, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751311001168063, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001168199, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751311001168338, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001168394, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751311001168627, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751311001168799, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001168849, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751311001168966, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751311001169057, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751311001169242, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001169402, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001169670, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001169972, "dur": 2457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001172430, "dur": 2014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001174445, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001174901, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001175157, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001175384, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001176111, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001176473, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001176717, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001177377, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001177669, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001178133, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001178480, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001178817, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001179099, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001179383, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001179631, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001179914, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001180211, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001180460, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001180801, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerField.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751311001180714, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001181517, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001181839, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001182493, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001182745, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001183106, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001183904, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001185103, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001185449, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001185759, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001187336, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001187636, "dur": 974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001188663, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001189339, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001189514, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001189620, "dur": 1013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751311001190635, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001190791, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001191061, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001191152, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751311001191823, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001192057, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751311001192166, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751311001192804, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001192921, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751311001193870, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001194014, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001194270, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001194385, "dur": 708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751311001195094, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001195192, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311001195445, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751311001196208, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001196320, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001196376, "dur": 82551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001278942, "dur": 2882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751311001281871, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751311001281953, "dur": 2229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751311001284183, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001284284, "dur": 5446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751311001289793, "dur": 2362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751311001292157, "dur": 2803, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001295142, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001295242, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001295340, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001295474, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001295612, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001295929, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Updater.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751311001295992, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001296141, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001296237, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001296393, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001296604, "dur": 615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001297224, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001297300, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311001297404, "dur": 547013, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001132839, "dur": 30240, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001163081, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_8325F75A1495950D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001163719, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0333895C18D609C9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001163798, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001163858, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_76EB92ADF25C401C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001163984, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_C047F56604413718.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001164643, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001164810, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_D4344483B74B03A7.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001164944, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001165042, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_D1635D1E3A6F959D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001165167, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_922751811D286E63.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001165237, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001165340, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_228E07120E318326.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001165471, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_228E07120E318326.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001165538, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_715C92D4D50EF494.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001165865, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001166120, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001166222, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001166296, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001166362, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751311001166524, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001166646, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001166747, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001166896, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751311001167198, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001167339, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751311001167496, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001167610, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001167723, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001167782, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751311001167862, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751311001167914, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001168173, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751311001168331, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001168433, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751311001168495, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751311001168638, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001168717, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001168951, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001169517, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001169930, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001170917, "dur": 2382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001173300, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001173565, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001173794, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001174038, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001174620, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001175137, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001175634, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001176273, "dur": 664, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@89399b10acbb\\Editor\\RendererFeatures\\ScreenSpaceAmbientOcclusionEditor.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751311001176050, "dur": 1374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001177425, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001177671, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001178140, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001178516, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001178782, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001179049, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001179360, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001179609, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001179883, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001180187, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001180435, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001180661, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001180955, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001181209, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001181485, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001181868, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001182402, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001182661, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001183110, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001184468, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001185334, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001185647, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001185886, "dur": 2163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001188049, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001188620, "dur": 760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001189383, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001189537, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001189673, "dur": 1041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751311001190720, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001190818, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001190888, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001191166, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001191250, "dur": 1843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751311001193095, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001193384, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001193985, "dur": 867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751311001194853, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001194974, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001195117, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001195187, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311001195357, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001195430, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751311001195988, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001196083, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001196148, "dur": 51165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001249593, "dur": 319, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 12, "ts": 1751311001249913, "dur": 1224, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 12, "ts": 1751311001251137, "dur": 123, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 12, "ts": 1751311001247315, "dur": 3955, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001251270, "dur": 30105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001281406, "dur": 2446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751311001283855, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001283988, "dur": 4984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751311001288973, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001289130, "dur": 2526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751311001291657, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001292150, "dur": 3471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751311001295623, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001295965, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001296259, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001296500, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751311001296573, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001296650, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001297329, "dur": 164101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001461460, "dur": 132042, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751311001461432, "dur": 133860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751311001596751, "dur": 342, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311001598260, "dur": 110260, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751311001741841, "dur": 101180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751311001741831, "dur": 101192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751311001843074, "dur": 1243, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751311001854124, "dur": 2367, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 19704, "tid": 2397, "ts": 1751311001878047, "dur": 8388, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 19704, "tid": 2397, "ts": 1751311001886526, "dur": 3458, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 19704, "tid": 2397, "ts": 1751311001872013, "dur": 19283, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}