{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 19704, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 19704, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 19704, "tid": 1426, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 19704, "tid": 1426, "ts": 1751308119674983, "dur": 16, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 19704, "tid": 1426, "ts": 1751308119675013, "dur": 6, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 19704, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 19704, "tid": 1, "ts": 1751308118933848, "dur": 1833, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19704, "tid": 1, "ts": 1751308118935686, "dur": 48155, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19704, "tid": 1, "ts": 1751308118983845, "dur": 48141, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 19704, "tid": 1426, "ts": 1751308119675021, "dur": 14, "ph": "X", "name": "", "args": {}}, {"pid": 19704, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118933817, "dur": 19940, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118953759, "dur": 720408, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118953774, "dur": 55, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118953831, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118953834, "dur": 869, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118954715, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118954720, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118954786, "dur": 8, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118954796, "dur": 5096, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118959918, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118959923, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960043, "dur": 4, "ph": "X", "name": "ProcessMessages 2352", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960049, "dur": 46, "ph": "X", "name": "ReadAsync 2352", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960098, "dur": 1, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960100, "dur": 49, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960152, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960155, "dur": 35, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960193, "dur": 2, "ph": "X", "name": "ProcessMessages 109", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960197, "dur": 35, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960235, "dur": 1, "ph": "X", "name": "ProcessMessages 59", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960238, "dur": 44, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960285, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960287, "dur": 42, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960332, "dur": 2, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960335, "dur": 53, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960391, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960393, "dur": 40, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960439, "dur": 34, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960478, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960481, "dur": 134, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960619, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960622, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960677, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960680, "dur": 39, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960721, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960723, "dur": 34, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960760, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960762, "dur": 39, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960804, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960807, "dur": 42, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960852, "dur": 2, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960854, "dur": 34, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960891, "dur": 1, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960892, "dur": 55, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960951, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960953, "dur": 38, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960994, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118960997, "dur": 44, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961042, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961045, "dur": 41, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961089, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961092, "dur": 47, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961143, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961147, "dur": 41, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961190, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961192, "dur": 40, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961234, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961236, "dur": 30, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961269, "dur": 37, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961310, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961313, "dur": 42, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961357, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961360, "dur": 129, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961495, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961556, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961560, "dur": 42, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961604, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961606, "dur": 44, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961653, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961655, "dur": 48, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961706, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961709, "dur": 37, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961749, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961751, "dur": 45, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961798, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961800, "dur": 38, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961841, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961843, "dur": 40, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961885, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961887, "dur": 36, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961926, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118961928, "dur": 340, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962275, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962280, "dur": 152, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962434, "dur": 5, "ph": "X", "name": "ProcessMessages 4399", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962440, "dur": 53, "ph": "X", "name": "ReadAsync 4399", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962502, "dur": 48, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962556, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962560, "dur": 25, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962587, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962588, "dur": 45, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962637, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962640, "dur": 40, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962684, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962685, "dur": 146, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962838, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962869, "dur": 2, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962872, "dur": 38, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962913, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962915, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962977, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118962980, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963035, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963037, "dur": 68, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963109, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963112, "dur": 52, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963167, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963169, "dur": 43, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963217, "dur": 45, "ph": "X", "name": "ReadAsync 9", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963270, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963272, "dur": 53, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963329, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963331, "dur": 51, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963385, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963388, "dur": 37, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963428, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963467, "dur": 40, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963510, "dur": 55, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963569, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963571, "dur": 51, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963626, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963629, "dur": 56, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963689, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963692, "dur": 49, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963744, "dur": 2, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963747, "dur": 86, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963844, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963848, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963957, "dur": 5, "ph": "X", "name": "ProcessMessages 2120", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118963965, "dur": 63, "ph": "X", "name": "ReadAsync 2120", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964032, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964034, "dur": 42, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964078, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964080, "dur": 57, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964140, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964142, "dur": 57, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964201, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964204, "dur": 48, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964255, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964257, "dur": 44, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964303, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964305, "dur": 48, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964355, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964357, "dur": 54, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964415, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964418, "dur": 141, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964566, "dur": 3, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964571, "dur": 72, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964647, "dur": 2, "ph": "X", "name": "ProcessMessages 1549", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964650, "dur": 94, "ph": "X", "name": "ReadAsync 1549", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964747, "dur": 2, "ph": "X", "name": "ProcessMessages 1178", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964751, "dur": 78, "ph": "X", "name": "ReadAsync 1178", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964832, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964839, "dur": 51, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964892, "dur": 1, "ph": "X", "name": "ProcessMessages 1135", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964894, "dur": 44, "ph": "X", "name": "ReadAsync 1135", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964941, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964944, "dur": 49, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964995, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118964997, "dur": 48, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965047, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965050, "dur": 46, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965098, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965100, "dur": 43, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965147, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965150, "dur": 49, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965201, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965204, "dur": 42, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965248, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965250, "dur": 104, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965365, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965369, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965451, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965456, "dur": 54, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965513, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965515, "dur": 46, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965563, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965565, "dur": 43, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965610, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965612, "dur": 45, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965660, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965662, "dur": 42, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965706, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965709, "dur": 118, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965835, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965839, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965917, "dur": 2, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965920, "dur": 48, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965970, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118965973, "dur": 54, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966030, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966032, "dur": 42, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966076, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966079, "dur": 56, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966137, "dur": 3, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966142, "dur": 32, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966179, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966259, "dur": 2, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966264, "dur": 64, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966330, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966335, "dur": 49, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966385, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966389, "dur": 42, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966433, "dur": 3, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966437, "dur": 54, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966495, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966502, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966574, "dur": 2, "ph": "X", "name": "ProcessMessages 1016", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966578, "dur": 46, "ph": "X", "name": "ReadAsync 1016", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966626, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966628, "dur": 42, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966672, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966674, "dur": 66, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966742, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966746, "dur": 45, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966793, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966797, "dur": 48, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966847, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966849, "dur": 52, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966904, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966906, "dur": 51, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966959, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118966962, "dur": 52, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967017, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967021, "dur": 41, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967064, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967068, "dur": 67, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967138, "dur": 1, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967141, "dur": 41, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967185, "dur": 2, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967189, "dur": 93, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967287, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967371, "dur": 4, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967378, "dur": 44, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967423, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967424, "dur": 52, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967481, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967483, "dur": 48, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967534, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967536, "dur": 39, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967578, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967581, "dur": 40, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967623, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967625, "dur": 46, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967674, "dur": 3, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967678, "dur": 42, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967725, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967727, "dur": 77, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967811, "dur": 2, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967815, "dur": 71, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967888, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967891, "dur": 51, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967944, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118967946, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968014, "dur": 2, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968018, "dur": 78, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968098, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968101, "dur": 46, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968149, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968152, "dur": 49, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968204, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968207, "dur": 57, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968267, "dur": 2, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968271, "dur": 83, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968360, "dur": 3, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968364, "dur": 59, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968429, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968432, "dur": 56, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968489, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968492, "dur": 65, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968559, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968561, "dur": 37, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968600, "dur": 2, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968603, "dur": 37, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968642, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968644, "dur": 34, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968680, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968682, "dur": 37, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968722, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968759, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968762, "dur": 34, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968798, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968800, "dur": 70, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968873, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968875, "dur": 105, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968982, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118968984, "dur": 58, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969050, "dur": 4, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969055, "dur": 68, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969125, "dur": 2, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969127, "dur": 78, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969207, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969211, "dur": 49, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969263, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969265, "dur": 41, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969309, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969312, "dur": 72, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969389, "dur": 2, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969393, "dur": 61, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969456, "dur": 1, "ph": "X", "name": "ProcessMessages 1036", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969458, "dur": 38, "ph": "X", "name": "ReadAsync 1036", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969498, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969500, "dur": 50, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969553, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969556, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969622, "dur": 2, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969625, "dur": 51, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969678, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969680, "dur": 42, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969724, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969726, "dur": 43, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969772, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969774, "dur": 44, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969820, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969822, "dur": 49, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969874, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969877, "dur": 73, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969952, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118969955, "dur": 44, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970000, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970003, "dur": 62, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970075, "dur": 3, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970080, "dur": 77, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970161, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970163, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970225, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970227, "dur": 56, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970287, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970289, "dur": 67, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970362, "dur": 2, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970367, "dur": 72, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970441, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970443, "dur": 40, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970485, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970487, "dur": 54, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970543, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970545, "dur": 38, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970584, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970586, "dur": 59, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970650, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970685, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970686, "dur": 29, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970720, "dur": 36, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970757, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970759, "dur": 39, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970801, "dur": 34, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970838, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970840, "dur": 38, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970880, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118970882, "dur": 196, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971082, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971127, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971130, "dur": 34, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971167, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971168, "dur": 37, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971208, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971210, "dur": 71, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971296, "dur": 4, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971305, "dur": 107, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971414, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971419, "dur": 91, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971513, "dur": 1, "ph": "X", "name": "ProcessMessages 1300", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971515, "dur": 89, "ph": "X", "name": "ReadAsync 1300", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971609, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971613, "dur": 44, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971661, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971664, "dur": 96, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971763, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971766, "dur": 109, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971879, "dur": 2, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971883, "dur": 50, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971934, "dur": 1, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971937, "dur": 39, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118971979, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972017, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972019, "dur": 26, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972046, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972048, "dur": 64, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972115, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972118, "dur": 42, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972162, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972164, "dur": 34, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972202, "dur": 43, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972248, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972250, "dur": 48, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972301, "dur": 1, "ph": "X", "name": "ProcessMessages 1021", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972303, "dur": 42, "ph": "X", "name": "ReadAsync 1021", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972347, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972349, "dur": 36, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972388, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972390, "dur": 38, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972432, "dur": 33, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972469, "dur": 37, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972508, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972509, "dur": 38, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972550, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972554, "dur": 39, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972596, "dur": 2, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972599, "dur": 38, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972639, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972641, "dur": 38, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972681, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972683, "dur": 37, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972722, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972725, "dur": 39, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972766, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972767, "dur": 39, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972809, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972811, "dur": 28, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972842, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972844, "dur": 18, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972866, "dur": 45, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972913, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972916, "dur": 34, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972953, "dur": 40, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972997, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118972999, "dur": 36, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973037, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973040, "dur": 38, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973080, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973082, "dur": 34, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973118, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973120, "dur": 35, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973158, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973160, "dur": 40, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973202, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973204, "dur": 35, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973241, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973243, "dur": 41, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973285, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973287, "dur": 60, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973351, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973354, "dur": 46, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973403, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973405, "dur": 24, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973432, "dur": 20, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973454, "dur": 22, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973480, "dur": 38, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973521, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973523, "dur": 49, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973576, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973578, "dur": 50, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973631, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973633, "dur": 52, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973688, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973690, "dur": 42, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973736, "dur": 3, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973740, "dur": 77, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973823, "dur": 104, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973931, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973933, "dur": 52, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973989, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118973991, "dur": 40, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974036, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974089, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974124, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974126, "dur": 26, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974156, "dur": 29, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974189, "dur": 26, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974216, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974219, "dur": 36, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974257, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974261, "dur": 190, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974464, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974469, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974564, "dur": 4, "ph": "X", "name": "ProcessMessages 2341", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974570, "dur": 47, "ph": "X", "name": "ReadAsync 2341", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974622, "dur": 56, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974680, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974683, "dur": 38, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974724, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974726, "dur": 47, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974774, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974777, "dur": 51, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974830, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974833, "dur": 51, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974886, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974888, "dur": 64, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974958, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118974963, "dur": 62, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975028, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975031, "dur": 41, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975073, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975076, "dur": 38, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975117, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975119, "dur": 38, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975160, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975162, "dur": 45, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975210, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975212, "dur": 42, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975259, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975302, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975304, "dur": 38, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975347, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975386, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975388, "dur": 56, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975447, "dur": 1, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975449, "dur": 34, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975485, "dur": 1, "ph": "X", "name": "ProcessMessages 194", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975486, "dur": 36, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975526, "dur": 75, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975604, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975607, "dur": 44, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975653, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975655, "dur": 40, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975698, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975700, "dur": 38, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975740, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975742, "dur": 50, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975796, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975837, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975839, "dur": 35, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975877, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975878, "dur": 98, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975990, "dur": 4, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118975996, "dur": 75, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976073, "dur": 2, "ph": "X", "name": "ProcessMessages 1043", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976077, "dur": 60, "ph": "X", "name": "ReadAsync 1043", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976142, "dur": 2, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976145, "dur": 93, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976240, "dur": 2, "ph": "X", "name": "ProcessMessages 887", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976243, "dur": 46, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976291, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976294, "dur": 52, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976349, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976351, "dur": 46, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976401, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976453, "dur": 2, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976457, "dur": 58, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976519, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976521, "dur": 102, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976634, "dur": 3, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976639, "dur": 61, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976701, "dur": 1, "ph": "X", "name": "ProcessMessages 963", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976703, "dur": 54, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976762, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976764, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976825, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976827, "dur": 92, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976924, "dur": 3, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118976928, "dur": 71, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977004, "dur": 3, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977009, "dur": 56, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977070, "dur": 1, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977073, "dur": 65, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977144, "dur": 52, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977201, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977204, "dur": 60, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977267, "dur": 2, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977270, "dur": 47, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977321, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977324, "dur": 48, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977375, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977378, "dur": 57, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977440, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977500, "dur": 1, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977503, "dur": 43, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977549, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977551, "dur": 43, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977600, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977602, "dur": 36, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977642, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977684, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977686, "dur": 44, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977732, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977734, "dur": 40, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977776, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977778, "dur": 39, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977819, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977821, "dur": 37, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977861, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977863, "dur": 42, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977908, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977949, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118977951, "dur": 47, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978002, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978005, "dur": 48, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978055, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978058, "dur": 40, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978100, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978102, "dur": 33, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978154, "dur": 37, "ph": "X", "name": "ReadAsync 14", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978193, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978196, "dur": 37, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978235, "dur": 2, "ph": "X", "name": "ProcessMessages 110", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978237, "dur": 41, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978280, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978282, "dur": 37, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978321, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978323, "dur": 39, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978365, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978367, "dur": 37, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978407, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978409, "dur": 89, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978501, "dur": 2, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978504, "dur": 43, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978550, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978552, "dur": 37, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978591, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978593, "dur": 337, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978939, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118978942, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979058, "dur": 6, "ph": "X", "name": "ProcessMessages 4074", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979065, "dur": 47, "ph": "X", "name": "ReadAsync 4074", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979116, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979118, "dur": 137, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979263, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979315, "dur": 48, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979366, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979369, "dur": 41, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979414, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979416, "dur": 66, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979486, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979488, "dur": 41, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979533, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979535, "dur": 89, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979628, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979630, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979722, "dur": 4, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979728, "dur": 83, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979813, "dur": 3, "ph": "X", "name": "ProcessMessages 1435", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979817, "dur": 54, "ph": "X", "name": "ReadAsync 1435", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979875, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979877, "dur": 67, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979949, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118979952, "dur": 67, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980024, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980092, "dur": 2, "ph": "X", "name": "ProcessMessages 1076", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980095, "dur": 83, "ph": "X", "name": "ReadAsync 1076", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980182, "dur": 2, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980185, "dur": 103, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980290, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980292, "dur": 51, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980348, "dur": 43, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980393, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980395, "dur": 31, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980429, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980435, "dur": 33, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980470, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980472, "dur": 41, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980519, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980567, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980570, "dur": 32, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980606, "dur": 51, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980659, "dur": 2, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980664, "dur": 44, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980711, "dur": 40, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980756, "dur": 119, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980879, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980881, "dur": 58, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980945, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980948, "dur": 45, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980996, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118980998, "dur": 41, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981043, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981045, "dur": 42, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981091, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981093, "dur": 43, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981138, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981140, "dur": 81, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981229, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981233, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981322, "dur": 2, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981327, "dur": 70, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981400, "dur": 43, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981447, "dur": 62, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981512, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981514, "dur": 47, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981563, "dur": 1, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981567, "dur": 41, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981613, "dur": 34, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981650, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981654, "dur": 25, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981680, "dur": 27, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981711, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981763, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981765, "dur": 49, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981818, "dur": 3, "ph": "X", "name": "ProcessMessages 1038", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981822, "dur": 51, "ph": "X", "name": "ReadAsync 1038", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981877, "dur": 55, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981933, "dur": 1, "ph": "X", "name": "ProcessMessages 1268", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981935, "dur": 45, "ph": "X", "name": "ReadAsync 1268", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118981983, "dur": 31, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982018, "dur": 32, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982053, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982054, "dur": 21, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982078, "dur": 50, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982133, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982203, "dur": 3, "ph": "X", "name": "ProcessMessages 1150", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982208, "dur": 64, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982273, "dur": 1, "ph": "X", "name": "ProcessMessages 911", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982276, "dur": 64, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982343, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982345, "dur": 102, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982450, "dur": 1, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982453, "dur": 42, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982496, "dur": 1, "ph": "X", "name": "ProcessMessages 943", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982499, "dur": 29, "ph": "X", "name": "ReadAsync 943", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982529, "dur": 2, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982532, "dur": 31, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982564, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982566, "dur": 34, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982601, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982603, "dur": 30, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982634, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982636, "dur": 24, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982662, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982664, "dur": 35, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982701, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982703, "dur": 18, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982723, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982725, "dur": 27, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982756, "dur": 22, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982781, "dur": 78, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982871, "dur": 3, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982876, "dur": 89, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982968, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118982971, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983029, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983031, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983080, "dur": 1, "ph": "X", "name": "ProcessMessages 997", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983083, "dur": 68, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983155, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983193, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983195, "dur": 26, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983224, "dur": 44, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983271, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983273, "dur": 132, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983410, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983413, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983480, "dur": 2, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983484, "dur": 57, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983544, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983546, "dur": 79, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983629, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983686, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983687, "dur": 41, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983731, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983733, "dur": 116, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983854, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983940, "dur": 1, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983942, "dur": 36, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983981, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118983982, "dur": 70, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984055, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984112, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984114, "dur": 50, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984170, "dur": 2, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984174, "dur": 47, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984224, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984226, "dur": 79, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984307, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984349, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984351, "dur": 29, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984382, "dur": 41, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984424, "dur": 2, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984427, "dur": 26, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984456, "dur": 65, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984524, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984561, "dur": 32, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984594, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984596, "dur": 69, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984668, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984669, "dur": 57, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984730, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984775, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984776, "dur": 29, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984806, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984808, "dur": 64, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984875, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984909, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984911, "dur": 31, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984949, "dur": 25, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118984976, "dur": 59, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985041, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985074, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985076, "dur": 30, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985109, "dur": 27, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985139, "dur": 64, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985206, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985284, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985286, "dur": 53, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985343, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985345, "dur": 37, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985384, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985425, "dur": 34, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985463, "dur": 64, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985529, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985567, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985568, "dur": 35, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985605, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985607, "dur": 72, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985682, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985714, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985715, "dur": 38, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985757, "dur": 68, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985828, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985885, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985887, "dur": 50, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985944, "dur": 2, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118985947, "dur": 68, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986017, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986019, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986061, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986063, "dur": 41, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986106, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986108, "dur": 68, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986179, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986182, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986235, "dur": 1, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986237, "dur": 71, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986310, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986352, "dur": 43, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986399, "dur": 1, "ph": "X", "name": "ProcessMessages 133", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986400, "dur": 32, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986435, "dur": 53, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986490, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986542, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986544, "dur": 42, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986587, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986589, "dur": 25, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986615, "dur": 70, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986688, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986759, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986762, "dur": 46, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986809, "dur": 1, "ph": "X", "name": "ProcessMessages 1280", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986811, "dur": 30, "ph": "X", "name": "ReadAsync 1280", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986843, "dur": 41, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986887, "dur": 27, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986916, "dur": 24, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118986942, "dur": 83, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987029, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987058, "dur": 30, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987092, "dur": 95, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987189, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987220, "dur": 2, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987222, "dur": 37, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987262, "dur": 45, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987309, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987310, "dur": 57, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987374, "dur": 3, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987378, "dur": 46, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987425, "dur": 1, "ph": "X", "name": "ProcessMessages 919", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987426, "dur": 139, "ph": "X", "name": "ReadAsync 919", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987571, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987574, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987635, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987637, "dur": 71, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987713, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987758, "dur": 1, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987760, "dur": 33, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987796, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987798, "dur": 131, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987933, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118987979, "dur": 32, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988015, "dur": 25, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988043, "dur": 30, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988076, "dur": 73, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988153, "dur": 35, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988190, "dur": 25, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988216, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988218, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988240, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988261, "dur": 155, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988420, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988455, "dur": 33, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988491, "dur": 21, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988515, "dur": 80, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988597, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988629, "dur": 31, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988664, "dur": 69, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988737, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988765, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988767, "dur": 53, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988823, "dur": 28, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988852, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988854, "dur": 28, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988884, "dur": 79, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988966, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118988997, "dur": 22, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989023, "dur": 25, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989051, "dur": 81, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989134, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989167, "dur": 33, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989203, "dur": 76, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989281, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989314, "dur": 101, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989424, "dur": 4, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989429, "dur": 40, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989472, "dur": 53, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989532, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989600, "dur": 2, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989604, "dur": 69, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989676, "dur": 2, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989678, "dur": 83, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989764, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989804, "dur": 38, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989846, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989887, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989889, "dur": 41, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989931, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118989933, "dur": 114, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990051, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990088, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990090, "dur": 30, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990122, "dur": 38, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990163, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990164, "dur": 37, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990203, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990205, "dur": 31, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990239, "dur": 109, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990351, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990389, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990390, "dur": 35, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990427, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990429, "dur": 33, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990464, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990466, "dur": 123, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990592, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990633, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990635, "dur": 73, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990713, "dur": 2, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990716, "dur": 67, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990796, "dur": 2, "ph": "X", "name": "ProcessMessages 104", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990800, "dur": 92, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990895, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990897, "dur": 79, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118990979, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991070, "dur": 2, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991075, "dur": 60, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991138, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991141, "dur": 191, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991335, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991337, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991397, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991400, "dur": 48, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991452, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991455, "dur": 24, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991483, "dur": 34, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991521, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991523, "dur": 154, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991682, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991739, "dur": 3, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991744, "dur": 45, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991799, "dur": 4, "ph": "X", "name": "ProcessMessages 125", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991805, "dur": 82, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991888, "dur": 2, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118991891, "dur": 157, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992052, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992055, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992114, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992116, "dur": 51, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992171, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992173, "dur": 39, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992218, "dur": 38, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992260, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992264, "dur": 130, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992398, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992401, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992490, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992492, "dur": 43, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992538, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992541, "dur": 282, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992826, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992828, "dur": 147, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118992992, "dur": 6, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993001, "dur": 95, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993097, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993104, "dur": 191, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993300, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993303, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993399, "dur": 2, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993402, "dur": 74, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993481, "dur": 2, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993485, "dur": 216, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993708, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993711, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993784, "dur": 2, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993786, "dur": 48, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993838, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993841, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993916, "dur": 42, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993962, "dur": 2, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118993965, "dur": 182, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118994153, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118994213, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118994216, "dur": 171, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118994391, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118994392, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118994456, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118994459, "dur": 205, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118994678, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118994684, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118994768, "dur": 2, "ph": "X", "name": "ProcessMessages 1129", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118994771, "dur": 198, "ph": "X", "name": "ReadAsync 1129", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118994975, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118994978, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118995033, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118995035, "dur": 68, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118995108, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118995110, "dur": 48, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118995160, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118995162, "dur": 510, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118995680, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118995685, "dur": 130, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118995819, "dur": 3, "ph": "X", "name": "ProcessMessages 1134", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118995823, "dur": 44, "ph": "X", "name": "ReadAsync 1134", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118995870, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118995872, "dur": 127, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996005, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996091, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996093, "dur": 110, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996206, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996210, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996258, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996260, "dur": 329, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996596, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996599, "dur": 104, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996705, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996708, "dur": 57, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996776, "dur": 5, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996782, "dur": 107, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996893, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996897, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996964, "dur": 2, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118996967, "dur": 38, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997008, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997010, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997080, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997128, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997131, "dur": 34, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997166, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997168, "dur": 31, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997203, "dur": 75, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997282, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997287, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997311, "dur": 33, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997347, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997382, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997384, "dur": 24, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997410, "dur": 63, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997477, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997498, "dur": 49, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997551, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997553, "dur": 37, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997593, "dur": 64, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997659, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997694, "dur": 36, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997733, "dur": 24, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997761, "dur": 76, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997842, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997920, "dur": 3, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997924, "dur": 54, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997981, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118997984, "dur": 98, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998085, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998113, "dur": 34, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998149, "dur": 43, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998196, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998199, "dur": 47, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998250, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998252, "dur": 38, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998293, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998294, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998347, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998350, "dur": 34, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998387, "dur": 35, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998425, "dur": 29, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998456, "dur": 27, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998486, "dur": 24, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998515, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998554, "dur": 109, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998666, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998709, "dur": 26, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998739, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998741, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998784, "dur": 26, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998813, "dur": 80, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998895, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998938, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998942, "dur": 41, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998985, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118998986, "dur": 39, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999028, "dur": 46, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999075, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999077, "dur": 31, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999111, "dur": 29, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999143, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999205, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999268, "dur": 44, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999315, "dur": 2, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999319, "dur": 50, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999370, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999373, "dur": 31, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999406, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999407, "dur": 28, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999438, "dur": 83, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999526, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999578, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999581, "dur": 228, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999814, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999817, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999882, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999885, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308118999947, "dur": 368, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000331, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000335, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000399, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000403, "dur": 34, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000440, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000441, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000481, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000515, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000517, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000546, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000579, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000581, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000613, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000644, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000674, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000703, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000751, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000797, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000800, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000844, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000846, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000883, "dur": 27, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000913, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000915, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000950, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119000952, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001003, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001005, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001034, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001064, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001065, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001124, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001127, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001177, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001179, "dur": 32, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001214, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001252, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001254, "dur": 27, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001284, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001325, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001328, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001378, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001380, "dur": 125, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001510, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001548, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001549, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001587, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001589, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001627, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001629, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001665, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001667, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001700, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001703, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001727, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001728, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001753, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001786, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001820, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001821, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001860, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001890, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001892, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001923, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001952, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119001979, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002011, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002013, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002040, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002042, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002074, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002075, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002108, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002137, "dur": 79, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002227, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002232, "dur": 63, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002298, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002300, "dur": 42, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002347, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002350, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002401, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002403, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002437, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002440, "dur": 34, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002477, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002479, "dur": 31, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002516, "dur": 34, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002552, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002554, "dur": 26, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002583, "dur": 69, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002661, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002665, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002716, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002718, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002758, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002761, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002811, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002813, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002853, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002855, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002897, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002899, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002939, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002941, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002978, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119002980, "dur": 36, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003018, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003020, "dur": 33, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003055, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003058, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003108, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003110, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003144, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003175, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003203, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003205, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003236, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003264, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003265, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003299, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003301, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003331, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003333, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003363, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003365, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003393, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003395, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003430, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003432, "dur": 172, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003609, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003613, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003671, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003673, "dur": 30, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003707, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003710, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003749, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003752, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003774, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003776, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003806, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003809, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003841, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003872, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003873, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003916, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003918, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003963, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119003965, "dur": 47, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004014, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004016, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004054, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004056, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004089, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004091, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004125, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004127, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004152, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004155, "dur": 63, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004221, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004223, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004267, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004270, "dur": 44, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004327, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004331, "dur": 51, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004383, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004385, "dur": 108, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004497, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004500, "dur": 51, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004555, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004558, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004607, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004609, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004654, "dur": 10, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004666, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004700, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004702, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004746, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004748, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004784, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004787, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004826, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004828, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004860, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004863, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004908, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004910, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004947, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004950, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004985, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119004987, "dur": 75, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005067, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005070, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005122, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005125, "dur": 59, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005186, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005188, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005231, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005233, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005273, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005275, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005323, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005325, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005377, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005379, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005412, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005415, "dur": 68, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005486, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005488, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005560, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005565, "dur": 55, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005626, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005686, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005689, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005740, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005743, "dur": 32, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005777, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005806, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005809, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005838, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005840, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005867, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005892, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119005970, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119006038, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119006079, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119006083, "dur": 7665, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119013758, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119013764, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119013805, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119013807, "dur": 950, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119014770, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119014774, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119014827, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119014830, "dur": 389, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119015226, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119015230, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119015291, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119015293, "dur": 4316, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119019625, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119019630, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119019692, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119019694, "dur": 124, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119019823, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119019859, "dur": 521, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119020385, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119020425, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119020500, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119020580, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119020586, "dur": 350, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119020948, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119020954, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021065, "dur": 3, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021069, "dur": 33, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021106, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021140, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021142, "dur": 523, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021669, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021717, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021721, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021788, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021826, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021830, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021874, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021876, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021915, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021943, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119021972, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022004, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022028, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022030, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022056, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022083, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022108, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022167, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022195, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022223, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022247, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022309, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022338, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022384, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022410, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022435, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022458, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022514, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022561, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022622, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022626, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022661, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022729, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022731, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022755, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022803, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022832, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022873, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119022898, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023046, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023080, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023082, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023114, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023173, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023210, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023396, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023427, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023428, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023484, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023545, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023547, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023576, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023680, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023712, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023714, "dur": 156, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023874, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023914, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023917, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023950, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023952, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119023987, "dur": 21, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024012, "dur": 178, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024193, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024220, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024245, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024292, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024325, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024327, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024367, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024438, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024440, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024481, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024507, "dur": 97, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024608, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024641, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024643, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024674, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024705, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024730, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024753, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024778, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024888, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119024919, "dur": 150, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025072, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025075, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025105, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025107, "dur": 66, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025179, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025233, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025238, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025286, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025311, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025313, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025348, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025350, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025393, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025395, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025424, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025454, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025493, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025495, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025535, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025579, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025580, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025615, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025618, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025653, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025681, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025739, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025775, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025805, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025893, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025896, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025955, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119025958, "dur": 179, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026141, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026143, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026182, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026185, "dur": 81, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026271, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026310, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026436, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026439, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026482, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026484, "dur": 49, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026537, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026575, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026608, "dur": 235, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026853, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026856, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026901, "dur": 4, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119026906, "dur": 128, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119027039, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119027041, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119027097, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119027099, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119027135, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119027137, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119027176, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119027222, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119027262, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119027264, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119027303, "dur": 167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119027480, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119027484, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119027524, "dur": 783, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119028318, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119028328, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119028420, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119028425, "dur": 118, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119028549, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119028552, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119028595, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119028597, "dur": 57, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119028660, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119028663, "dur": 255, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119028925, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119028928, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119028976, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119028980, "dur": 289, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119029279, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119029284, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119029355, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119029358, "dur": 49, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119029410, "dur": 170, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119029584, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119029586, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119029627, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119029630, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119029888, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119029930, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119029962, "dur": 204, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030168, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030200, "dur": 210, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030415, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030446, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030476, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030537, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030569, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030596, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030597, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030650, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030654, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030705, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030800, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030834, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030944, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030947, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119030999, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119031002, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119031039, "dur": 193, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119031236, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119031238, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119031282, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119031285, "dur": 259, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119031547, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119031626, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119031629, "dur": 125, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119031762, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119031766, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119031817, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119031850, "dur": 286, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119032139, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119032185, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119032187, "dur": 160, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119032350, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119032382, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119032423, "dur": 212, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119032638, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119032670, "dur": 626, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119033309, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119033314, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119033367, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119033372, "dur": 69943, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119103326, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119103331, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119103377, "dur": 27, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119103406, "dur": 12571, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119115989, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119115993, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119116033, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119116041, "dur": 37, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119116082, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119116084, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119116155, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119116182, "dur": 513, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119116699, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119116734, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119116736, "dur": 1538, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119118284, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119118288, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119118327, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119118329, "dur": 74, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119118408, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119118444, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119118549, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119118582, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119118611, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119118671, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119118700, "dur": 125, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119118829, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119118856, "dur": 313, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119119173, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119119209, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119119211, "dur": 334, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119119548, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119119551, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119119581, "dur": 1232, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119120818, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119120821, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119120843, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119120845, "dur": 195, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119121046, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119121111, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119121114, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119121184, "dur": 1, "ph": "X", "name": "ProcessMessages 10", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119121209, "dur": 37, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119121247, "dur": 2, "ph": "X", "name": "ProcessMessages 22", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119121251, "dur": 178, "ph": "X", "name": "ReadAsync 22", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119121433, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119121437, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119121505, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119121510, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119121550, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119121582, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119121584, "dur": 974, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119122570, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119122573, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119122608, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119122610, "dur": 502, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119123115, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119123117, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119123172, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119123176, "dur": 203, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119123383, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119123421, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119123424, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119123450, "dur": 315, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119123769, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119123804, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119123893, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119123918, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119123921, "dur": 189, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119124113, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119124131, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119124221, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119124247, "dur": 328, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119124578, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119124605, "dur": 435, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119125044, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119125070, "dur": 514, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119125589, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119125622, "dur": 248, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119125874, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119125904, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119125938, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119126083, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119126099, "dur": 173, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119126276, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119126307, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119126309, "dur": 503, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119126818, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119126821, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119126869, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119126872, "dur": 131, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127009, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127050, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127052, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127086, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127130, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127132, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127196, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127201, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127280, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127282, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127316, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127318, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127369, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127371, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127408, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127435, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127437, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127464, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127490, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127514, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127538, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127598, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127634, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127742, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127770, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127772, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127819, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127854, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127885, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127923, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119127960, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128042, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128064, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128105, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128128, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128161, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128192, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128219, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128241, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128277, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128279, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128340, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128371, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128417, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128487, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128492, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128533, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128535, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128571, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128574, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128609, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128611, "dur": 324, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128939, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119128943, "dur": 69, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129016, "dur": 4, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129022, "dur": 46, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129070, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129072, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129120, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129122, "dur": 60, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129192, "dur": 4, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129198, "dur": 69, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129269, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129271, "dur": 124, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129400, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129402, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129452, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129454, "dur": 40, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129497, "dur": 355, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129861, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129865, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129906, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119129908, "dur": 120977, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119250897, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119250901, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119250941, "dur": 25, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119250967, "dur": 17299, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119268276, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119268280, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119268367, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119268373, "dur": 159516, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119427905, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119427912, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119427999, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119428015, "dur": 22979, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119451005, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119451011, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119451108, "dur": 35, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119451145, "dur": 52269, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119503426, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119503433, "dur": 105, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119503543, "dur": 7, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119503551, "dur": 15649, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119519211, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119519215, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119519292, "dur": 35, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119519328, "dur": 31650, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119550988, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119550994, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119551036, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119551040, "dur": 2259, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119553309, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119553313, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119553386, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119553410, "dur": 49029, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119602447, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119602451, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119602519, "dur": 20, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119602540, "dur": 28563, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119631115, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119631119, "dur": 135, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119631261, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119631266, "dur": 1028, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119632306, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119632312, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119632403, "dur": 41, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119632446, "dur": 1202, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119633660, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119633665, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119633768, "dur": 42, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119633812, "dur": 26235, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119660056, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119660060, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119660156, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119660162, "dur": 980, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119661158, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119661165, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119661224, "dur": 44, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119661270, "dur": 550, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119661826, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119661832, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119661914, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 19704, "tid": 34359738368, "ts": 1751308119661917, "dur": 12238, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 19704, "tid": 1426, "ts": 1751308119675037, "dur": 3442, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 19704, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 19704, "tid": 30064771072, "ts": 1751308118933764, "dur": 98232, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 19704, "tid": 30064771072, "ts": 1751308119031998, "dur": 41, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 19704, "tid": 1426, "ts": 1751308119678481, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 19704, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 19704, "tid": 25769803776, "ts": 1751308118928390, "dur": 745823, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 19704, "tid": 25769803776, "ts": 1751308118928522, "dur": 5179, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 19704, "tid": 25769803776, "ts": 1751308119674220, "dur": 73, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 19704, "tid": 25769803776, "ts": 1751308119674233, "dur": 19, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 19704, "tid": 25769803776, "ts": 1751308119674294, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 19704, "tid": 1426, "ts": 1751308119678488, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751308118952732, "dur": 124, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308118952901, "dur": 3722, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308118956647, "dur": 1226, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308118958074, "dur": 102, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751308118958177, "dur": 707, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308118959130, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_1FC72A0013DC4EEB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118959266, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AFE94C31B12D3213.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118959427, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_9870EDF59A107BDF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118959535, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_82F8B03BA9650A81.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118959823, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_CC3B7EF1A2C4EA40.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118959891, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7B82E1658E753B21.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118959995, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_A1485D01311C863E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118960053, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_0E82E95F1D4B1B8B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118960150, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FECC8B8B813FB02A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118960383, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_1182EE28E68C62C8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118961410, "dur": 278, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A619838E516A75AC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118961744, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0FEAE2B241ACD344.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118961978, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BA0CB78B239CCACF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118962076, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_91BF37D1F055EC15.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118962173, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_7676AB112B74F121.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118962299, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D7031BE9F197A0A6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118962520, "dur": 158, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E04F87D224606974.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118963074, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_D8FCB30ADD921A73.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118963602, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_D1635D1E3A6F959D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118965311, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_4C98D3F7040CD4F5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118966091, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751308118966596, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_3D651E01942E03F8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118967944, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751308118969333, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751308118970635, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118971489, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751308118972614, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751308118972674, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751308118972985, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751308118973052, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751308118973263, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751308118973735, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_12B7E1785E41BE0E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118974195, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751308118974695, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751308118975581, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751308118975653, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751308118975902, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Editor.ref.dll_0F50152946DB09D0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118977141, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751308118977584, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751308118978397, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751308118978934, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118979313, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118979855, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751308118980330, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308118980486, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751308118980705, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751308118981412, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751308118983746, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13909235412005675431.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751308118985017, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751308118986473, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751308118987237, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751308118988677, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751308118990183, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751308118990580, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751308118990876, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751308118991195, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751308118991303, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751308118991536, "dur": 152, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751308118991971, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751308118992081, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751308118992923, "dur": 157, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751308118994184, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751308118995135, "dur": 232, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751308118995727, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751308118996848, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751308118958929, "dur": 39767, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308118998715, "dur": 661584, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308119660300, "dur": 298, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308119660601, "dur": 92, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308119660853, "dur": 78, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308119660968, "dur": 2262, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751308118959732, "dur": 39098, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308118998842, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_CC3B7EF1A2C4EA40.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308118999431, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308118999506, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_E681FA607883332E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308118999710, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_15A0A72BE0744047.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308118999993, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119000057, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_EE062F8861099912.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308119000159, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_7676AB112B74F121.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308119000252, "dur": 252, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_7676AB112B74F121.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308119000507, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_7DC1C7578DCAB919.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308119000724, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119000853, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_48483D327EA17808.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308119000966, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3EF742C8CA4C5E1E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308119001060, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_486DF76931BD0557.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308119001164, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_7AC1C063560F2BC6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308119001420, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119001681, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119001749, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751308119001839, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119001906, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119002152, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119002236, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119002352, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119002418, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119002623, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119002721, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119002782, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751308119002921, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119003111, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119003288, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119003354, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119003475, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119004028, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119004177, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119004263, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119004441, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119004654, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119004950, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119005045, "dur": 1409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119006455, "dur": 1876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119008332, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119008665, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119008901, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119009138, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119009377, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119009681, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119009915, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119010595, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119010925, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119011217, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119011844, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119012135, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119012430, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119012820, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119013232, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119013936, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119014206, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119014550, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119014822, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119015083, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119015396, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119015636, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119015921, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119016228, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119016483, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119016722, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119017010, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119017275, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119018118, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119018762, "dur": 1009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119019774, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308119019967, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119020072, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751308119020719, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119020889, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119020987, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308119021256, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119021424, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308119021790, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119021888, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308119022205, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119022264, "dur": 696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751308119022962, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119023036, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751308119023832, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119023919, "dur": 587, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119024518, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119024620, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119024701, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119024762, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119024833, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119025359, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308119025538, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119025602, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751308119026335, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119026403, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308119026556, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119026620, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751308119027322, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119027466, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308119027636, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119027718, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751308119028282, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119028423, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119028488, "dur": 84050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119112540, "dur": 2463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751308119115005, "dur": 2363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119117395, "dur": 2439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751308119119836, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119120003, "dur": 2468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751308119122472, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119122566, "dur": 2418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751308119124985, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119125092, "dur": 2810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751308119127903, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119128077, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751308119128235, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119128333, "dur": 128264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119256634, "dur": 166644, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751308119256599, "dur": 168511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751308119426615, "dur": 322, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308119427911, "dur": 90387, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751308119549629, "dur": 109492, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751308119549619, "dur": 109504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751308119659149, "dur": 1092, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751308118959442, "dur": 39325, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308118998787, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308118998975, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FECC8B8B813FB02A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308118999575, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308118999687, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FECC8B8B813FB02A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308118999801, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_33AFB99E7761AA5C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308118999918, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119000007, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_A164BAE5670FC544.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308119000125, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119000217, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_A164BAE5670FC544.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308119000279, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BADDD17E000661FB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308119000389, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_472976495F1230C7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308119000495, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_1A2A3B79BD3B4CFD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308119000715, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119000929, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FEA05FC664382286.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308119001041, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_1638DBF3D84589E5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308119001154, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_E106F825F731F4BC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308119001217, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119001616, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119001769, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751308119001901, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119002007, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751308119002077, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119002150, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119002247, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751308119002358, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119002498, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119002647, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751308119002748, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751308119002812, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119002891, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751308119002993, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119003148, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119003337, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119003487, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119003538, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751308119003862, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119004188, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751308119004330, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119004523, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119004894, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119005007, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119005184, "dur": 1807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119006992, "dur": 1412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119008405, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119008703, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119008960, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119009317, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119009647, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119009907, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119010529, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119010853, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119011173, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119011496, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119012126, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119012427, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119013070, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119013332, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119013647, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119014277, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119014618, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119014896, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119015201, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119015505, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119015919, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119016215, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119016559, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119016790, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119017056, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119017723, "dur": 1032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119018756, "dur": 979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119019738, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308119019978, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119020102, "dur": 2226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751308119022330, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119022590, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119022698, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308119022990, "dur": 404, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119023400, "dur": 2224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751308119025625, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119025762, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308119025920, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119025992, "dur": 1269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751308119027263, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119027431, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119027492, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308119027702, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751308119028312, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119028437, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119028512, "dur": 84029, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119112544, "dur": 2439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751308119114985, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119115083, "dur": 2634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751308119117718, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119117913, "dur": 2265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751308119120180, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119120383, "dur": 2483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751308119122867, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119123414, "dur": 2471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751308119125887, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119125959, "dur": 2946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751308119128912, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308119129013, "dur": 531312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308118959490, "dur": 39292, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308118998797, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_82F8B03BA9650A81.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308118999470, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_F8CCEDBE36EB62B5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308118999552, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308118999742, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_B83C6071BB394334.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308118999916, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C8A867A03915DFB5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119000039, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119000143, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_91BF37D1F055EC15.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119000244, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_91BF37D1F055EC15.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119000303, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_BEB6379C07D3D185.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119000431, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B1D6DCAB2D5A18DA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119000488, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119000690, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_D4344483B74B03A7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119000801, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_0B2798972D9D68EB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119000932, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_7E691CD5ECB673D9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119001027, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AFE94C31B12D3213.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119001274, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119001452, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119001572, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119001739, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119001854, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119001953, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119002134, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119002212, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119002273, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751308119002354, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119002619, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751308119002698, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119002863, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119003005, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119003213, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751308119003356, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119003604, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751308119003770, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119003873, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119004121, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119004270, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119004411, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119004528, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751308119004636, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119004951, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751308119005123, "dur": 2199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119007323, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119008720, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119008961, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119009201, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119009468, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119009720, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119009968, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119010679, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119011013, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119011298, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119011815, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119012116, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119012378, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119012807, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119013060, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119013320, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119013625, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119013926, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119014587, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119014841, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119015163, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119015431, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119015689, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119016142, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119016378, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119016755, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119017074, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119017322, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119017716, "dur": 1042, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119018758, "dur": 1001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119019762, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119019956, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119020055, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119020144, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751308119020719, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119021093, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119021177, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119021545, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119021602, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119021966, "dur": 1355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751308119023322, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119023396, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119023761, "dur": 502, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119024265, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751308119024950, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119025076, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119025358, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119025527, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119025589, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751308119026092, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119026321, "dur": 5269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119031592, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308119031809, "dur": 80721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119112545, "dur": 2412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751308119114959, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119115053, "dur": 2279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751308119117334, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119117404, "dur": 2430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751308119119836, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119120195, "dur": 2675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751308119122872, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119122960, "dur": 4181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751308119127143, "dur": 793, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119128037, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119128150, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751308119128335, "dur": 421291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308119549675, "dur": 334, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751308119549629, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751308119550077, "dur": 2341, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751308119552425, "dur": 107890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308118959367, "dur": 39375, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308118998787, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308118998943, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751308118999038, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F697CEAF687CB7A2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308118999581, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_857E61E190401437.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308118999642, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308118999708, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_857E61E190401437.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308118999824, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_51B6A25823037467.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308118999965, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_0EEB0D491376C433.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119000094, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_0EEB0D491376C433.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119000196, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_BFD5601BA05C7CD8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119000306, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8B3842F7B08DFF89.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119000407, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8B3842F7B08DFF89.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119000493, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D7CA9FED890DDAB1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119000778, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_EAAAA0867963BCF7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119000888, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A28CE54978CD971D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119000998, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A28CE54978CD971D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119001214, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_715C92D4D50EF494.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119001371, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7F3203F2669A3DC6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119001546, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119001672, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119001902, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119002019, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751308119002119, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119002250, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119002508, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119002667, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751308119002808, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119002942, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119003213, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751308119003591, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751308119004145, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119004373, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119004476, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119004608, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751308119004718, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119004947, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751308119005029, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119006293, "dur": 1770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119008064, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119008306, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119008537, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119008819, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119009063, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119009292, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119009531, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119009764, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119010015, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119010714, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119011025, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119011330, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119011665, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119011951, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119012209, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119013251, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119013550, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119013803, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119014245, "dur": 786, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Runtime\\RenderGraph\\RenderGraphProfileId.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751308119014088, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119015228, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119015487, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119015828, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119016169, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119016515, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119017124, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119017608, "dur": 1145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119018753, "dur": 976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119019733, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119019948, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119020118, "dur": 733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751308119020853, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119021021, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119021412, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119021480, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751308119022200, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119022346, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751308119022975, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119023055, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119023287, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119023365, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119023658, "dur": 1158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751308119024817, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119024947, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119025355, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308119025525, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119025620, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751308119026094, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119026192, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119026254, "dur": 4975, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119033474, "dur": 426, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 4, "ts": 1751308119033901, "dur": 1302, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 4, "ts": 1751308119035204, "dur": 144, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 4, "ts": 1751308119031230, "dur": 4125, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119035355, "dur": 77167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119112537, "dur": 2437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751308119114976, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119115238, "dur": 2256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751308119117558, "dur": 2278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751308119119837, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119119974, "dur": 2398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751308119122374, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119122583, "dur": 2380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751308119124964, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119125051, "dur": 3434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751308119128486, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308119128577, "dur": 531724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308118959653, "dur": 39147, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308118998809, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_5F13DCF03A10B823.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308118999440, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308118999533, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_596B249A76101BE6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308118999643, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308118999712, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_0E07881F85CDD302.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308118999836, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_595F986B2A245834.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308118999967, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119000046, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A619838E516A75AC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308119000198, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A619838E516A75AC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308119000267, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_64D9AC7F4C4AC8A8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308119000406, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_433711FCB6016F00.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308119000520, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B6153E13308654D1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308119000740, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119000822, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_C1BEC5DDD68DA9AF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308119001029, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_20A6B658F2F96E47.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308119001126, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119001213, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308119001396, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119001553, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119001643, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119001746, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119001971, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751308119002121, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119002253, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751308119002448, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119002641, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751308119002981, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119003117, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751308119003196, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751308119003310, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119003436, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119003535, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751308119003898, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751308119003979, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119004072, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119004226, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119004343, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119004487, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119004671, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119004988, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119005328, "dur": 2272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119007601, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119007774, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119007975, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119008215, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119008489, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119008730, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119008958, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119009178, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119009455, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119009715, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119009977, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119010764, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119011107, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119011385, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119011726, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119012289, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119012674, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119013234, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119013562, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119013969, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119014389, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119014757, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119015139, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119015426, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119015700, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119015949, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119016427, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119016731, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119017057, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119017438, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119017623, "dur": 1116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119018740, "dur": 999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119019742, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308119019979, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119020121, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751308119020773, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119021033, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119021163, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119021228, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308119021585, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119021682, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751308119022525, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119022652, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751308119023401, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119023564, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119023651, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119023897, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119024060, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308119024456, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751308119025172, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119025289, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119025360, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119025800, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308119025955, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119026056, "dur": 1559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751308119027616, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119027766, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308119027937, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119028054, "dur": 872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751308119028935, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119029056, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119029124, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308119029346, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751308119029990, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119030101, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119030159, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308119030380, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751308119030830, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119030976, "dur": 81534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119112512, "dur": 2491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751308119115005, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119115337, "dur": 3189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751308119118528, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119118718, "dur": 2874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751308119121594, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119121698, "dur": 2443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751308119124143, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119124213, "dur": 2633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751308119126970, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751308119127101, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cinemachine.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751308119127218, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119127418, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119127503, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119127584, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119127994, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119128080, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751308119128264, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308119128619, "dur": 531675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308118959696, "dur": 39118, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308118998826, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_256223B421F6FD72.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308118999471, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308118999685, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_256223B421F6FD72.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308118999815, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_8EA224AC6679B7C2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308118999912, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7E47E9336205E31D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308118999976, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119000118, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BA0CB78B239CCACF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308119000203, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119000330, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_658DA73782B14E20.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308119000444, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_D8FCB30ADD921A73.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308119000502, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119000666, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_93B77B82222D1B4A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308119000782, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E689A207C3E7A0C2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308119000894, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_815CA55F1B3FC2AD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308119001138, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119001516, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308119001749, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119001822, "dur": 10962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751308119012958, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119013230, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119013505, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119013777, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119014058, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119014506, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119014769, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119015000, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119015308, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119015547, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119015866, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119016149, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119016453, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119016721, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119017148, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119017599, "dur": 1138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119018791, "dur": 1024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119019818, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308119019987, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119020113, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751308119020921, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119021026, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308119021212, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119021384, "dur": 1164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751308119022549, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119022837, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_E2F92DAB6C167CC9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308119023029, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119023132, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_E2F92DAB6C167CC9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308119023200, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308119023353, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119023474, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751308119024135, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119024253, "dur": 955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1751308119025209, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119025316, "dur": 180, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119026034, "dur": 76399, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1751308119112523, "dur": 2461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751308119114985, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119115105, "dur": 2528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751308119117635, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119117772, "dur": 2406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751308119120179, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119120599, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751308119122973, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119123063, "dur": 2243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751308119125307, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119125447, "dur": 2750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751308119128204, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119128341, "dur": 501371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308119629740, "dur": 1724, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751308119629713, "dur": 1752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751308119631483, "dur": 1287, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751308119632778, "dur": 27525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308118959766, "dur": 39080, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308118998860, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7B82E1658E753B21.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308118999354, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308118999464, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_467B09015CCE177C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308118999559, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308118999723, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_1DC8148833156840.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308118999843, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_C047F56604413718.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308118999922, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119000081, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0FEAE2B241ACD344.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119000143, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119000217, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D7031BE9F197A0A6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119000323, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D7031BE9F197A0A6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119000393, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_FE92BD4D4925733A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119000477, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119000752, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_9870EDF59A107BDF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119000884, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_5FDF6992734E0361.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119000987, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1948650C4D21EE20.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119001091, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_228E07120E318326.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119001183, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_0D77228FB13B7FB6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119001482, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119001744, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_3D651E01942E03F8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119001833, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119001920, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119002050, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308119002105, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119002198, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119002371, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119002444, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119002565, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308119002626, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119002682, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308119002870, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119003109, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308119003180, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119003243, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308119003296, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119003525, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119003585, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308119003710, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119004286, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119004485, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119004613, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119004998, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119005931, "dur": 1736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119007668, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119007903, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119008144, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119008368, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119008652, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119008890, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119009132, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119009357, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119009699, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119009950, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119010571, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119010862, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119011118, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119011769, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119012112, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119012371, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119012842, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119013113, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119013349, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119013676, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119013969, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119014481, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119014752, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119015015, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119015448, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119015714, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119016055, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119016338, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119016595, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119016869, "dur": 72, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119016977, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119017224, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119017642, "dur": 1125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119018768, "dur": 975, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119019746, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119019956, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119020051, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119020310, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308119020944, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119021061, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119021174, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119021393, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119021477, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119021638, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119021775, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119021978, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119022041, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308119022607, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119022870, "dur": 787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308119023659, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119023945, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119024120, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119024268, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119024478, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119024642, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308119025241, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119025301, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119025385, "dur": 2097, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119027485, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119027661, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119027723, "dur": 1891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308119029616, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119029708, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119029815, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119029962, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308119030639, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119030758, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119030916, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308119031449, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119031575, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308119031803, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308119032323, "dur": 71, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119032398, "dur": 57, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119032553, "dur": 217459, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308119257069, "dur": 9649, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751308119256584, "dur": 10209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308119267217, "dur": 104, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119268330, "dur": 181748, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308119456786, "dur": 41962, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751308119456773, "dur": 43923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751308119502135, "dur": 330, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308119503443, "dur": 98123, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751308119629701, "dur": 446, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751308119629691, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751308119630186, "dur": 1221, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751308119631414, "dur": 28910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308118959848, "dur": 39031, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308118998898, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_8A89D5AFAE49E63E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308118999468, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308118999536, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_8DE949524442C9D8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308118999653, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_D32EF602AAA54BE6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308118999758, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_76EB92ADF25C401C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119000002, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_308026626194FDB8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119000099, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119000219, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_DBB153F07C192DFF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119000309, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_DBB153F07C192DFF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119000375, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_209987431F1A3EDC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119000443, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119000737, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_F21194D2F617C9ED.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119000878, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_CBABBF352E91654E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119000951, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119001023, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_1FF1C2755BAA46D4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119001225, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_BFEF6BF3A445E475.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119001295, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119001363, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_07FA64190ECA4CC1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119001620, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119001688, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119001775, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751308119001956, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119002636, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119002856, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119003506, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119003608, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751308119004087, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119004250, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119004363, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119004516, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119004572, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751308119004661, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119004910, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119004978, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119005341, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119007144, "dur": 1359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119008504, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119008797, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119009020, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119009258, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119009625, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119009903, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119010527, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119010862, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119011323, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119011665, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119011938, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119012185, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119012439, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119012875, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119013135, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119013541, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119013778, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119014040, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119014392, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119014684, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119014941, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119015200, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119015659, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119016091, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119016397, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119016640, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119016996, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119017234, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119017584, "dur": 1157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119018742, "dur": 1020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119019765, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119019956, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119020096, "dur": 721, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119020821, "dur": 691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751308119021513, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119021586, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119021742, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119022114, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119022250, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751308119023022, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119023132, "dur": 321, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ref.dll_7989A1C8D57EF9BF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119023462, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119023734, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119023849, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751308119024529, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119024812, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119024899, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119025357, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308119025533, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119025606, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751308119026150, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119026348, "dur": 9014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119035362, "dur": 77155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119112521, "dur": 2440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751308119114962, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119115051, "dur": 2273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751308119117326, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119117717, "dur": 2117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751308119119835, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119119977, "dur": 2492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751308119122471, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119122552, "dur": 2642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751308119125253, "dur": 3287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751308119128542, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308119128645, "dur": 531666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308118959918, "dur": 38995, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308118998928, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_A1485D01311C863E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308118999473, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_69FC355D1BD4BB29.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308118999564, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308118999728, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F27EBD72F4881481.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308118999859, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_4E1AB9DAA17812DB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308118999969, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_5DFD03B93DA29B4D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119000097, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119000172, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0999ABDFA115F312.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119000230, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119000289, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E04F87D224606974.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119000378, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3E1DF6609ACAC61F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119000491, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_8FE2697BF98A2193.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119000785, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_C01CA8838C1CE31C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119001013, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119001072, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_12124AFB436F9D5D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119001134, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119001576, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119001737, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119001801, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119001882, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119002155, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119002382, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119002621, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751308119002734, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751308119002785, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119002935, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119003016, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119003156, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119003387, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119003489, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119003563, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751308119004195, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751308119004364, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119004493, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751308119004606, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8863518860715653438.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751308119004704, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119005003, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119005137, "dur": 1459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119006596, "dur": 1567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119008164, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119008385, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119008745, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119008988, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119009227, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119009592, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119009830, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119010474, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119010766, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119011035, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119011313, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119011700, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119011977, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119012296, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119012729, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119013058, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119013299, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119013601, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119013983, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119014310, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119014654, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119014950, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119015481, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119015887, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119016183, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119016524, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119016780, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119017240, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119018263, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119018744, "dur": 1012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119019758, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119019956, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119020050, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119020105, "dur": 834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751308119020940, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119021119, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119021325, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119021608, "dur": 1471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751308119023088, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119023178, "dur": 290, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751308119023538, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119023667, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751308119024245, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119024339, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PerformanceTesting.ref.dll_F99DC928B1D72E5E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119024474, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119024585, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119024846, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119024910, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751308119025345, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119025419, "dur": 5345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119030766, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308119030884, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119030939, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751308119031301, "dur": 82144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119113449, "dur": 2334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751308119115784, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119115862, "dur": 2298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751308119118162, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119118336, "dur": 2276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751308119120613, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119120722, "dur": 2438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751308119123169, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119123283, "dur": 2571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751308119125856, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119126020, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119126299, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119126757, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119127091, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119127293, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119127615, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1751308119128074, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1751308119128296, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308119128636, "dur": 531655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308118959963, "dur": 38968, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308118998944, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_0E82E95F1D4B1B8B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308118999510, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_1182EE28E68C62C8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308118999576, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308118999705, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_B566D0CB2E423B86.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308118999828, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_E5318FF293833B30.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308118999895, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308118999961, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_526B70D8724FDBD0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308119000100, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_526B70D8724FDBD0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308119000231, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8D9A2E8777AB6882.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308119000332, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8D9A2E8777AB6882.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308119000471, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_BB587A5119495C88.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308119000732, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DA7D4247AC2DB3F7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308119000841, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_D1635D1E3A6F959D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308119000942, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_80843626DE95EA13.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308119001034, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_1FC72A0013DC4EEB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308119001490, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119001744, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751308119001913, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119002184, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119002258, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751308119002360, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119002476, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119002813, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119003022, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119003317, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119003475, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119003548, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751308119003635, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751308119003833, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119004249, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119004445, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119004520, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15875538839575725175.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751308119004648, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119004910, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119004974, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119005226, "dur": 1909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119007136, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119008473, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119008837, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119009064, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119009286, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119009552, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119009791, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119010411, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119010789, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119011309, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119012107, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119012631, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119012887, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119013146, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119014333, "dur": 747, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@8a519b6be09c\\Runtime\\TMP\\TMP_FontAssetUtilities.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751308119013954, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119015212, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119015475, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119015718, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119015992, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119016317, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119016570, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119016823, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119017003, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119017241, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119018142, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119018760, "dur": 1006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119019769, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308119019979, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119020200, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751308119020773, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119021205, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308119021485, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119021576, "dur": 1391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751308119022968, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119023121, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308119023382, "dur": 991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751308119024374, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119024489, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751308119025224, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119025299, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119025389, "dur": 3742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119029133, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308119029261, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119029332, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751308119029780, "dur": 85240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119115026, "dur": 2269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751308119117297, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119117414, "dur": 4766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751308119122181, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119122279, "dur": 2263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751308119124543, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119124746, "dur": 2725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751308119127473, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119127997, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119128230, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119128294, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308119128639, "dur": 531693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308118960039, "dur": 38909, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308118998957, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_44462716F1E6187F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308118999515, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308118999648, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0333895C18D609C9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308118999732, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308118999797, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9843DD54C5E6159E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308118999921, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_67CB883191F4D66D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308119000048, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119000155, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_67CB883191F4D66D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308119000300, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_DB48149D0776AF57.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308119000427, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1FE2264601546D0E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308119000482, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119000718, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7B8E45163E4907D2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308119000861, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119000958, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_922751811D286E63.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308119001041, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119001143, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119001261, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308119001402, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751308119001549, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119001668, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119001759, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751308119001867, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119002065, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119002174, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119002373, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119002666, "dur": 264, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751308119002934, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119003028, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119003152, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119003320, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751308119003377, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119003494, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119003828, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751308119004043, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119004480, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119004607, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119004868, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119004939, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751308119005000, "dur": 2032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119007033, "dur": 1364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119008398, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119008696, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119008941, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119009160, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119009742, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119010002, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119010793, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119011121, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119011398, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119011912, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119012208, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119012638, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119012973, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119013236, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119013548, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119013793, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119014152, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119014524, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119014780, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119015050, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119015338, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119015610, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119015890, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119016226, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119016486, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119016773, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119017070, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119017324, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119017473, "dur": 86, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119017560, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119017621, "dur": 1113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119018742, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119018802, "dur": 1176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119019980, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308119020089, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119020148, "dur": 1478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751308119021627, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119021917, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751308119022545, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119022731, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308119023091, "dur": 727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751308119023819, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119024405, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119024768, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119025375, "dur": 1028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119026444, "dur": 86081, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119112538, "dur": 2379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751308119114926, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119115064, "dur": 2679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751308119117745, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119117948, "dur": 2270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751308119120276, "dur": 4639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751308119124916, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119125042, "dur": 3407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751308119128450, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308119128584, "dur": 531712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308118960094, "dur": 38895, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308118998991, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_8325F75A1495950D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308118999663, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_14A5559FFED55C50.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308118999755, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DAD9D37CE1A4BA55.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308118999855, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_9D64899DE49D3B06.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119000043, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119000146, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_60AF5DD29DC33809.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119000249, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_60AF5DD29DC33809.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119000353, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F0EAC96F0603CD08.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119000435, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119000498, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_77F91A8BF54EFB7C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119000715, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119000871, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_584BDBB0AACB340C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119001041, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119001368, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119001511, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119001588, "dur": 12219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751308119013951, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119014248, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119014384, "dur": 4245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751308119018762, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119018833, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119018982, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751308119019409, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119019576, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119019761, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119019977, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119020142, "dur": 816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751308119020959, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119021188, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119021559, "dur": 1400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751308119022960, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119023160, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119023366, "dur": 1494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751308119024862, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119025354, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119025535, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119025660, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751308119026278, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119026395, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119026571, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119026633, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751308119027394, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119027530, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119027696, "dur": 710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751308119028408, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119028516, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308119028746, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751308119029621, "dur": 82892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119112514, "dur": 2470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751308119114985, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119115077, "dur": 2609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751308119117695, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119117879, "dur": 2709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751308119120589, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119120699, "dur": 2863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751308119123564, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119123750, "dur": 2351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751308119126197, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119126572, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119126657, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119126989, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119127420, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119127735, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119128241, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119128307, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308119129047, "dur": 531266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308119670557, "dur": 2165, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 19704, "tid": 1426, "ts": 1751308119678554, "dur": 26, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 19704, "tid": 1426, "ts": 1751308119678626, "dur": 7849, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 19704, "tid": 1426, "ts": 1751308119675001, "dur": 11525, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}