{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 19704, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 19704, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 19704, "tid": 1470, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 19704, "tid": 1470, "ts": 1751308336619854, "dur": 889, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 19704, "tid": 1470, "ts": 1751308336625533, "dur": 907, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 19704, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 19704, "tid": 1, "ts": 1751308335833361, "dur": 6216, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19704, "tid": 1, "ts": 1751308335839582, "dur": 55148, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19704, "tid": 1, "ts": 1751308335894753, "dur": 45554, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 19704, "tid": 1470, "ts": 1751308336626446, "dur": 14, "ph": "X", "name": "", "args": {}}, {"pid": 19704, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335831143, "dur": 10735, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335841881, "dur": 766519, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335843195, "dur": 2555, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335845764, "dur": 1406, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335847175, "dur": 395, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335847575, "dur": 493, "ph": "X", "name": "ProcessMessages 20500", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848071, "dur": 182, "ph": "X", "name": "ReadAsync 20500", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848257, "dur": 9, "ph": "X", "name": "ProcessMessages 14223", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848267, "dur": 55, "ph": "X", "name": "ReadAsync 14223", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848325, "dur": 1, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848326, "dur": 77, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848406, "dur": 1, "ph": "X", "name": "ProcessMessages 1234", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848408, "dur": 99, "ph": "X", "name": "ReadAsync 1234", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848511, "dur": 1, "ph": "X", "name": "ProcessMessages 1657", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848513, "dur": 72, "ph": "X", "name": "ReadAsync 1657", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848588, "dur": 1, "ph": "X", "name": "ProcessMessages 1046", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848590, "dur": 42, "ph": "X", "name": "ReadAsync 1046", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848636, "dur": 65, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848705, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848774, "dur": 1, "ph": "X", "name": "ProcessMessages 1504", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848776, "dur": 66, "ph": "X", "name": "ReadAsync 1504", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848844, "dur": 1, "ph": "X", "name": "ProcessMessages 1075", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848846, "dur": 82, "ph": "X", "name": "ReadAsync 1075", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848936, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335848940, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849043, "dur": 2, "ph": "X", "name": "ProcessMessages 1321", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849046, "dur": 66, "ph": "X", "name": "ReadAsync 1321", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849116, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849118, "dur": 71, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849193, "dur": 1, "ph": "X", "name": "ProcessMessages 969", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849196, "dur": 73, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849273, "dur": 2, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849276, "dur": 65, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849347, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849349, "dur": 72, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849424, "dur": 1, "ph": "X", "name": "ProcessMessages 1127", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849427, "dur": 74, "ph": "X", "name": "ReadAsync 1127", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849504, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849507, "dur": 60, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849569, "dur": 59, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849630, "dur": 1, "ph": "X", "name": "ProcessMessages 1118", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849632, "dur": 33, "ph": "X", "name": "ReadAsync 1118", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849668, "dur": 61, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849732, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849733, "dur": 68, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849803, "dur": 1, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849805, "dur": 59, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849866, "dur": 59, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849927, "dur": 1, "ph": "X", "name": "ProcessMessages 943", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849929, "dur": 50, "ph": "X", "name": "ReadAsync 943", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849983, "dur": 2, "ph": "X", "name": "ProcessMessages 1005", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335849986, "dur": 49, "ph": "X", "name": "ReadAsync 1005", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850037, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850040, "dur": 85, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850127, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850129, "dur": 57, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850191, "dur": 67, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850260, "dur": 1, "ph": "X", "name": "ProcessMessages 1378", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850262, "dur": 58, "ph": "X", "name": "ReadAsync 1378", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850323, "dur": 66, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850390, "dur": 1, "ph": "X", "name": "ProcessMessages 1126", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850392, "dur": 55, "ph": "X", "name": "ReadAsync 1126", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850449, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850451, "dur": 59, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850512, "dur": 1, "ph": "X", "name": "ProcessMessages 1134", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850513, "dur": 58, "ph": "X", "name": "ReadAsync 1134", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850573, "dur": 1, "ph": "X", "name": "ProcessMessages 1059", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850574, "dur": 36, "ph": "X", "name": "ReadAsync 1059", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850612, "dur": 1, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850614, "dur": 44, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850659, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850661, "dur": 37, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850700, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850701, "dur": 40, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850743, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850745, "dur": 41, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850788, "dur": 1, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850790, "dur": 39, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850831, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850832, "dur": 43, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850878, "dur": 41, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850921, "dur": 1, "ph": "X", "name": "ProcessMessages 938", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850922, "dur": 41, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850966, "dur": 1, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335850968, "dur": 53, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851027, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851029, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851086, "dur": 1, "ph": "X", "name": "ProcessMessages 1311", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851088, "dur": 44, "ph": "X", "name": "ReadAsync 1311", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851134, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851135, "dur": 39, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851177, "dur": 38, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851218, "dur": 43, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851262, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851264, "dur": 35, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851302, "dur": 314, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851621, "dur": 2, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851625, "dur": 87, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851715, "dur": 3, "ph": "X", "name": "ProcessMessages 3914", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851720, "dur": 48, "ph": "X", "name": "ReadAsync 3914", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851770, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851772, "dur": 35, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851810, "dur": 47, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851859, "dur": 1, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851861, "dur": 38, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335851903, "dur": 256, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852167, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852170, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852237, "dur": 2, "ph": "X", "name": "ProcessMessages 1366", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852241, "dur": 41, "ph": "X", "name": "ReadAsync 1366", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852285, "dur": 34, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852321, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852323, "dur": 46, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852371, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852373, "dur": 43, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852420, "dur": 39, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852461, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852463, "dur": 34, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852501, "dur": 36, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852540, "dur": 39, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852581, "dur": 1, "ph": "X", "name": "ProcessMessages 938", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852583, "dur": 39, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852623, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852625, "dur": 36, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852664, "dur": 35, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852702, "dur": 40, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852744, "dur": 1, "ph": "X", "name": "ProcessMessages 1068", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852746, "dur": 46, "ph": "X", "name": "ReadAsync 1068", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852794, "dur": 1, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852796, "dur": 34, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852833, "dur": 41, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852876, "dur": 1, "ph": "X", "name": "ProcessMessages 1165", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852878, "dur": 38, "ph": "X", "name": "ReadAsync 1165", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852919, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852920, "dur": 34, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852956, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335852958, "dur": 41, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853002, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853004, "dur": 78, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853091, "dur": 4, "ph": "X", "name": "ProcessMessages 1095", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853096, "dur": 73, "ph": "X", "name": "ReadAsync 1095", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853173, "dur": 1, "ph": "X", "name": "ProcessMessages 1076", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853175, "dur": 67, "ph": "X", "name": "ReadAsync 1076", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853245, "dur": 1, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853248, "dur": 41, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853291, "dur": 1, "ph": "X", "name": "ProcessMessages 1002", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853293, "dur": 40, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853335, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853336, "dur": 41, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853379, "dur": 1, "ph": "X", "name": "ProcessMessages 1154", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853381, "dur": 39, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853422, "dur": 1, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853424, "dur": 34, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853459, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853461, "dur": 44, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853506, "dur": 1, "ph": "X", "name": "ProcessMessages 941", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853508, "dur": 34, "ph": "X", "name": "ReadAsync 941", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853544, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853546, "dur": 39, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853588, "dur": 1, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853589, "dur": 33, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853625, "dur": 42, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853669, "dur": 1, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853671, "dur": 39, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853712, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853714, "dur": 34, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853752, "dur": 40, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853794, "dur": 1, "ph": "X", "name": "ProcessMessages 1037", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853795, "dur": 92, "ph": "X", "name": "ReadAsync 1037", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853889, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853891, "dur": 44, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853938, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853939, "dur": 42, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335853986, "dur": 69, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854057, "dur": 1, "ph": "X", "name": "ProcessMessages 991", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854059, "dur": 80, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854140, "dur": 1, "ph": "X", "name": "ProcessMessages 1032", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854142, "dur": 42, "ph": "X", "name": "ReadAsync 1032", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854188, "dur": 35, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854226, "dur": 43, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854271, "dur": 1, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854272, "dur": 39, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854313, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854315, "dur": 33, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854351, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854388, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854389, "dur": 39, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854430, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854432, "dur": 36, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854469, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854471, "dur": 42, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854516, "dur": 39, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854556, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854558, "dur": 40, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854600, "dur": 1, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854602, "dur": 34, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854641, "dur": 41, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854684, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854686, "dur": 41, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854728, "dur": 1, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854730, "dur": 35, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854767, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854768, "dur": 34, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854805, "dur": 61, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854868, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854870, "dur": 60, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854937, "dur": 3, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335854942, "dur": 61, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855006, "dur": 1, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855008, "dur": 42, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855052, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855053, "dur": 37, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855093, "dur": 32, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855128, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855131, "dur": 30, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855164, "dur": 37, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855202, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855204, "dur": 35, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855242, "dur": 33, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855278, "dur": 31, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855312, "dur": 46, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855359, "dur": 1, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855361, "dur": 36, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855400, "dur": 32, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855434, "dur": 32, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855469, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855525, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855527, "dur": 48, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855577, "dur": 1, "ph": "X", "name": "ProcessMessages 1180", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855580, "dur": 40, "ph": "X", "name": "ReadAsync 1180", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855622, "dur": 1, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855623, "dur": 37, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855664, "dur": 41, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855707, "dur": 1, "ph": "X", "name": "ProcessMessages 959", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855709, "dur": 52, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855767, "dur": 2, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855770, "dur": 48, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855821, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855822, "dur": 74, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855899, "dur": 2, "ph": "X", "name": "ProcessMessages 1137", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855902, "dur": 39, "ph": "X", "name": "ReadAsync 1137", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855944, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855945, "dur": 41, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855989, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335855990, "dur": 40, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856032, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856034, "dur": 39, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856094, "dur": 40, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856136, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856139, "dur": 41, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856182, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856184, "dur": 62, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856247, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856249, "dur": 38, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856291, "dur": 45, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856337, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856339, "dur": 39, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856380, "dur": 1, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856382, "dur": 34, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856420, "dur": 39, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856461, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856463, "dur": 42, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856507, "dur": 1, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856509, "dur": 34, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856546, "dur": 40, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856588, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856590, "dur": 36, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856628, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856629, "dur": 36, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856667, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856669, "dur": 33, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856705, "dur": 38, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856746, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856789, "dur": 1, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856791, "dur": 42, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856834, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856836, "dur": 33, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856872, "dur": 40, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856915, "dur": 1, "ph": "X", "name": "ProcessMessages 1100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856917, "dur": 41, "ph": "X", "name": "ReadAsync 1100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856959, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856961, "dur": 36, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335856999, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857001, "dur": 47, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857052, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857107, "dur": 1, "ph": "X", "name": "ProcessMessages 1542", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857109, "dur": 51, "ph": "X", "name": "ReadAsync 1542", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857163, "dur": 52, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857219, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857266, "dur": 1, "ph": "X", "name": "ProcessMessages 1521", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857269, "dur": 36, "ph": "X", "name": "ReadAsync 1521", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857308, "dur": 40, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857351, "dur": 37, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857390, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857391, "dur": 40, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857433, "dur": 1, "ph": "X", "name": "ProcessMessages 1048", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857435, "dur": 58, "ph": "X", "name": "ReadAsync 1048", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857495, "dur": 1, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857497, "dur": 47, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857547, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857549, "dur": 56, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857608, "dur": 1, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857610, "dur": 32, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857646, "dur": 31, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857680, "dur": 55, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857737, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857739, "dur": 52, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857793, "dur": 3, "ph": "X", "name": "ProcessMessages 1421", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857798, "dur": 38, "ph": "X", "name": "ReadAsync 1421", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857837, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857839, "dur": 40, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857882, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857883, "dur": 38, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857923, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857925, "dur": 36, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857963, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335857964, "dur": 37, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858004, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858006, "dur": 39, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858046, "dur": 1, "ph": "X", "name": "ProcessMessages 1038", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858049, "dur": 36, "ph": "X", "name": "ReadAsync 1038", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858086, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858088, "dur": 38, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858128, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858130, "dur": 38, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858169, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858171, "dur": 37, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858210, "dur": 1, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858212, "dur": 36, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858249, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858251, "dur": 39, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858293, "dur": 1, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858295, "dur": 39, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858336, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858337, "dur": 35, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858375, "dur": 41, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858418, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858420, "dur": 37, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858459, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858461, "dur": 40, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858503, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858505, "dur": 42, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858549, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858551, "dur": 40, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858593, "dur": 1, "ph": "X", "name": "ProcessMessages 1056", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858595, "dur": 39, "ph": "X", "name": "ReadAsync 1056", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858636, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858638, "dur": 34, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858675, "dur": 68, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858745, "dur": 1, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858747, "dur": 42, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858791, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858793, "dur": 35, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858832, "dur": 36, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858869, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858871, "dur": 35, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858909, "dur": 66, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858980, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335858982, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859030, "dur": 1, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859032, "dur": 52, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859088, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859090, "dur": 53, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859146, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859202, "dur": 41, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859246, "dur": 1, "ph": "X", "name": "ProcessMessages 938", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859248, "dur": 34, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859286, "dur": 53, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859342, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859381, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859382, "dur": 34, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859418, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859420, "dur": 79, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859502, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859544, "dur": 37, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859584, "dur": 73, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859660, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859700, "dur": 35, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859736, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859738, "dur": 53, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859795, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859797, "dur": 43, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859844, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859883, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859885, "dur": 35, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859922, "dur": 69, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335859994, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860033, "dur": 53, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860089, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860091, "dur": 40, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860134, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860136, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860180, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860220, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860222, "dur": 33, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860258, "dur": 70, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860330, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860369, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860371, "dur": 35, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860409, "dur": 72, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860484, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860523, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860526, "dur": 34, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860563, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860564, "dur": 71, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860638, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860677, "dur": 84, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860763, "dur": 2, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860767, "dur": 37, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860806, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860809, "dur": 44, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860856, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860858, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860906, "dur": 2, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860909, "dur": 33, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860943, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335860945, "dur": 60, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861007, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861047, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861049, "dur": 36, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861088, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861090, "dur": 86, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861178, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861181, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861233, "dur": 46, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861282, "dur": 1, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861284, "dur": 34, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861321, "dur": 58, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861383, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861385, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861428, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861430, "dur": 58, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861495, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861498, "dur": 45, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861547, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861638, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861642, "dur": 37, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861682, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861684, "dur": 57, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861746, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861790, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861793, "dur": 34, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861829, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335861832, "dur": 725, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335862564, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335862568, "dur": 132, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335862702, "dur": 5, "ph": "X", "name": "ProcessMessages 5859", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335862709, "dur": 62, "ph": "X", "name": "ReadAsync 5859", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335862780, "dur": 3, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335862784, "dur": 61, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335862849, "dur": 41, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335862891, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335862893, "dur": 30, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335862927, "dur": 65, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335862996, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863038, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863042, "dur": 38, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863082, "dur": 3, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863086, "dur": 40, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863130, "dur": 63, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863197, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863200, "dur": 43, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863247, "dur": 34, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863286, "dur": 60, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863352, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863399, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863402, "dur": 35, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863438, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863441, "dur": 57, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863503, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863541, "dur": 4, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863548, "dur": 92, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863645, "dur": 4, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863651, "dur": 56, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863709, "dur": 2, "ph": "X", "name": "ProcessMessages 1517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863713, "dur": 30, "ph": "X", "name": "ReadAsync 1517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863745, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863747, "dur": 80, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863831, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863876, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863878, "dur": 35, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335863916, "dur": 79, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864000, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864050, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864053, "dur": 33, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864088, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864089, "dur": 98, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864191, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864196, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864261, "dur": 2, "ph": "X", "name": "ProcessMessages 1886", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864264, "dur": 43, "ph": "X", "name": "ReadAsync 1886", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864310, "dur": 36, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864348, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864350, "dur": 33, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864387, "dur": 62, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864453, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864456, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864519, "dur": 1, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864522, "dur": 32, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864556, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864558, "dur": 55, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864616, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864657, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864659, "dur": 32, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864695, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864698, "dur": 31, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864734, "dur": 62, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864800, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864854, "dur": 1, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864856, "dur": 33, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864894, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864954, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335864990, "dur": 40, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865032, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865034, "dur": 31, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865068, "dur": 2, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865070, "dur": 58, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865131, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865179, "dur": 2, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865182, "dur": 38, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865222, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865227, "dur": 31, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865260, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865262, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865323, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865364, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865366, "dur": 34, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865401, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865403, "dur": 69, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865477, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865532, "dur": 1, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865535, "dur": 31, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865570, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865572, "dur": 62, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865637, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865640, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865709, "dur": 3, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865713, "dur": 60, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865776, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865778, "dur": 36, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865818, "dur": 57, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865878, "dur": 37, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865917, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865919, "dur": 61, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335865986, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866036, "dur": 1, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866040, "dur": 33, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866077, "dur": 56, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866135, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866137, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866222, "dur": 3, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866225, "dur": 36, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866265, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866268, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866324, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866373, "dur": 1, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866375, "dur": 32, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866408, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866410, "dur": 66, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866479, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866518, "dur": 3, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866522, "dur": 36, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866559, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866561, "dur": 69, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866634, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866675, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866677, "dur": 36, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866715, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866718, "dur": 69, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866792, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866829, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866831, "dur": 35, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866868, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866870, "dur": 29, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866902, "dur": 66, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335866972, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867029, "dur": 1, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867032, "dur": 31, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867065, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867067, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867126, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867128, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867180, "dur": 1, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867182, "dur": 32, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867218, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867221, "dur": 61, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867285, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867344, "dur": 1, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867346, "dur": 33, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867384, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867443, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867484, "dur": 41, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867527, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867528, "dur": 62, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867594, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867692, "dur": 2, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867696, "dur": 42, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867743, "dur": 35, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867782, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867833, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867835, "dur": 33, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335867871, "dur": 138, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868013, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868100, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868103, "dur": 46, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868152, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868154, "dur": 37, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868194, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868240, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868241, "dur": 88, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868333, "dur": 1, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868335, "dur": 62, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868401, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868460, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868462, "dur": 36, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868503, "dur": 69, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868577, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868645, "dur": 1, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868648, "dur": 38, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868690, "dur": 111, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868807, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868811, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868863, "dur": 1, "ph": "X", "name": "ProcessMessages 1062", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868867, "dur": 121, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335868992, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869029, "dur": 38, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869068, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869071, "dur": 35, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869110, "dur": 114, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869228, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869231, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869322, "dur": 3, "ph": "X", "name": "ProcessMessages 1682", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869328, "dur": 54, "ph": "X", "name": "ReadAsync 1682", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869384, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869386, "dur": 42, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869432, "dur": 29, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869465, "dur": 61, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869529, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869571, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869573, "dur": 32, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869607, "dur": 34, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869646, "dur": 67, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869717, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869773, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869775, "dur": 87, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869868, "dur": 2, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869871, "dur": 65, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869939, "dur": 2, "ph": "X", "name": "ProcessMessages 1048", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869943, "dur": 44, "ph": "X", "name": "ReadAsync 1048", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335869991, "dur": 46, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870040, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870042, "dur": 103, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870148, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870238, "dur": 3, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870242, "dur": 55, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870302, "dur": 2, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870305, "dur": 49, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870356, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870358, "dur": 41, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870402, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870404, "dur": 33, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870441, "dur": 32, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870475, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870478, "dur": 104, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870584, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870587, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870626, "dur": 215, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870852, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870856, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335870921, "dur": 484, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335871416, "dur": 128, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335871553, "dur": 8, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335871564, "dur": 88, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335871654, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335871657, "dur": 58, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335871723, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335871727, "dur": 64, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335871796, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335871800, "dur": 45, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335871848, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335871850, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335871919, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335871971, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335871973, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872017, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872021, "dur": 35, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872058, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872062, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872103, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872105, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872140, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872143, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872184, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872185, "dur": 67, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872262, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872267, "dur": 58, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872329, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872332, "dur": 38, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872374, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872378, "dur": 108, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872488, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872490, "dur": 42, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872535, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872538, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872583, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872587, "dur": 49, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872639, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872642, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872684, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872686, "dur": 93, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872790, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872795, "dur": 77, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872878, "dur": 3, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872883, "dur": 45, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872932, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872934, "dur": 56, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872993, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335872996, "dur": 66, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873065, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873067, "dur": 56, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873125, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873128, "dur": 63, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873200, "dur": 5, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873207, "dur": 70, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873281, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873285, "dur": 73, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873362, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873367, "dur": 59, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873429, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873433, "dur": 42, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873478, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873482, "dur": 42, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873528, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873530, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873582, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873585, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873611, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873613, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873657, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873661, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873740, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873743, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873801, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873807, "dur": 62, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873872, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873877, "dur": 46, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873925, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873928, "dur": 48, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873980, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335873984, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874036, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874040, "dur": 46, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874090, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874092, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874156, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874159, "dur": 73, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874236, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874238, "dur": 58, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874301, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874304, "dur": 77, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874384, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874386, "dur": 61, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874451, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874453, "dur": 51, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874508, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874511, "dur": 41, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874555, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874557, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874601, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874605, "dur": 61, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874679, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874686, "dur": 60, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874748, "dur": 3, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874752, "dur": 40, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874796, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874799, "dur": 43, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874846, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874849, "dur": 51, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874905, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874907, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874953, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335874956, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875007, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875010, "dur": 60, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875073, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875075, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875112, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875115, "dur": 54, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875181, "dur": 6, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875188, "dur": 53, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875244, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875246, "dur": 189, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875441, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875488, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875491, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875529, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875531, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875569, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875571, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875655, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875700, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875704, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875747, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875826, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875828, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875867, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335875902, "dur": 7940, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335883851, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335883855, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335883894, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335883896, "dur": 1116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335885023, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335885028, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335885102, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335885105, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335885146, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335885148, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335885187, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335885311, "dur": 9793, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335895115, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335895119, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335895176, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335895178, "dur": 83, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335895266, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335895286, "dur": 398, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335895687, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335895754, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335895756, "dur": 210, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335895972, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896015, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896018, "dur": 31, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896052, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896082, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896197, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896225, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896399, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896418, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896660, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896698, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896700, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896727, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896845, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896883, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896885, "dur": 64, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896952, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896954, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335896998, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897032, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897034, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897068, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897146, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897217, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897249, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897251, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897290, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897320, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897322, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897375, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897422, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897424, "dur": 154, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897582, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897610, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897637, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897640, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897741, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897768, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897815, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897873, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335897877, "dur": 161, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898043, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898082, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898112, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898144, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898236, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898265, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898314, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898344, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898410, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898444, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898472, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898474, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898503, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898504, "dur": 290, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898800, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898838, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898840, "dur": 87, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898932, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898933, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898969, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335898971, "dur": 211, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899186, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899219, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899269, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899302, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899354, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899356, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899396, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899428, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899461, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899531, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899562, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899564, "dur": 160, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899728, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899760, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899808, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899810, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899849, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899853, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899889, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899891, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899924, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335899976, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900013, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900015, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900044, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900087, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900089, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900127, "dur": 107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900238, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900290, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900291, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900326, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900374, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900405, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900434, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900592, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900594, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900642, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900643, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900689, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335900723, "dur": 287, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901017, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901058, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901060, "dur": 127, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901192, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901228, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901261, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901263, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901291, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901319, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901348, "dur": 33, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901383, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901385, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901422, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901512, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901570, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901577, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901626, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901628, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901663, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901667, "dur": 192, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901865, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901911, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335901913, "dur": 416, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335902334, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335902336, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335902377, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335902380, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335902411, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335902522, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335902552, "dur": 164, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335902721, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335902776, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335902778, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335902816, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335902875, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335902877, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335902910, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335902950, "dur": 220, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335903173, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335903175, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335903218, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335903220, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335903252, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335903364, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335903405, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335903408, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335903438, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335903476, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335903521, "dur": 196, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335903723, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335903775, "dur": 820, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335904607, "dur": 151, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335904764, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335904768, "dur": 66, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335904841, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335904844, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335904929, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335904931, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335904997, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335904999, "dur": 225, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335905230, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335905234, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335905299, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335905301, "dur": 145, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335905449, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335905450, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335905500, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335905502, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335905538, "dur": 168, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335905710, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335905752, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335905754, "dur": 176, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335905933, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335905972, "dur": 392, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335906370, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335906373, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335906421, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335906423, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335906465, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335906520, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335906597, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335906600, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335906640, "dur": 718, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335907366, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335907371, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335907430, "dur": 102, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335907537, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335907577, "dur": 369, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335907952, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335907957, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335908006, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335908008, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335908089, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335908134, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335908136, "dur": 148, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335908290, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335908332, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335908334, "dur": 947, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335909286, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335909323, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335909327, "dur": 71961, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335981303, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335981308, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335981382, "dur": 1659, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335983049, "dur": 11188, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335994249, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335994254, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335994313, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335994320, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335994364, "dur": 2429, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335996803, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335996807, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335996851, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335996853, "dur": 91, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335996949, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335996984, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335996986, "dur": 138, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335997128, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335997160, "dur": 895, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335998068, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335998072, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335998148, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335998152, "dur": 1294, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335999460, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335999466, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335999519, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335999522, "dur": 83, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335999613, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335999616, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335999648, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335999768, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308335999815, "dur": 922, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336000746, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336000750, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336000785, "dur": 657, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336001456, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336001462, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336001504, "dur": 188, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336001722, "dur": 47, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336001773, "dur": 351, "ph": "X", "name": "ProcessMessages 6", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336002130, "dur": 146, "ph": "X", "name": "ReadAsync 6", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336002280, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336002282, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336002325, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336002367, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336002427, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336002465, "dur": 721, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336003198, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336003203, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336003256, "dur": 9, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336003267, "dur": 1639, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336004917, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336004922, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336004962, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336004964, "dur": 292, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336005267, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336005270, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336005322, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336005324, "dur": 1243, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336006582, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336006585, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336006626, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336006627, "dur": 484, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336007118, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336007149, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336007292, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336007327, "dur": 172, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336007502, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336007530, "dur": 289, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336007824, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336007864, "dur": 205, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336008074, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336008105, "dur": 713, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336008822, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336008824, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336008868, "dur": 224, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336009095, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336009124, "dur": 648, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336009778, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336009781, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336009836, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336009838, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336009877, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336009908, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336009987, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010020, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010181, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010208, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010260, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010287, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010334, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010360, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010398, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010422, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010465, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010495, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010527, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010555, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010605, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010632, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010655, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010691, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010717, "dur": 248, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010969, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336010995, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011027, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011071, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011095, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011154, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011192, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011194, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011222, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011255, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011257, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011299, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011301, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011343, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011345, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011376, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011402, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011429, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011462, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011464, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011497, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011536, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011554, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011607, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011647, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011649, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011687, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011689, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011730, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011763, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011765, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336011797, "dur": 202, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012003, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012005, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012088, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012092, "dur": 57, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012153, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012156, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012203, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012205, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012251, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012253, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012291, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012293, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012326, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012364, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012401, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012403, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012435, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012464, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012466, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012492, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012523, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012527, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012557, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012590, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012592, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012629, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012690, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012695, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012732, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012734, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012761, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012794, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012795, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012826, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012858, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012859, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012893, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012925, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012927, "dur": 28, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012957, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336012959, "dur": 46, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013013, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013019, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013077, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013080, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013133, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013166, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013197, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013245, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013247, "dur": 354, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013612, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013615, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013676, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013679, "dur": 48, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013731, "dur": 195, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013932, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013934, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013996, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336013998, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336014092, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336014097, "dur": 146408, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336160514, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336160518, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336160556, "dur": 21, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336160577, "dur": 19737, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336180325, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336180330, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336180392, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336180397, "dur": 175544, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336355955, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336355959, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336356051, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336356059, "dur": 51239, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336407310, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336407315, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336407405, "dur": 30, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336407437, "dur": 35298, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336442751, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336442756, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336442809, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336442815, "dur": 12616, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336455446, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336455451, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336455492, "dur": 39, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336455533, "dur": 35876, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336491420, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336491424, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336491459, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336491463, "dur": 2157, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336493633, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336493637, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336493719, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336493748, "dur": 35421, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336529181, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336529185, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336529267, "dur": 33, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336529301, "dur": 31688, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336561003, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336561008, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336561055, "dur": 4, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336561060, "dur": 1111, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336562184, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336562188, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336562257, "dur": 52, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336562311, "dur": 31206, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336593528, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336593532, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336593569, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336593572, "dur": 1292, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336594881, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336594886, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336594972, "dur": 34, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336595008, "dur": 762, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336595782, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336595787, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336595866, "dur": 539, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751308336596411, "dur": 11241, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 19704, "tid": 1470, "ts": 1751308336626461, "dur": 1871, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 19704, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 19704, "tid": 8589934592, "ts": 1751308335828393, "dur": 111999, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 19704, "tid": 8589934592, "ts": 1751308335940396, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 19704, "tid": 8589934592, "ts": 1751308335940405, "dur": 1498, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 19704, "tid": 1470, "ts": 1751308336628335, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 19704, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 19704, "tid": 4294967296, "ts": 1751308335808832, "dur": 800758, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751308335812901, "dur": 9257, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751308336609664, "dur": 6062, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751308336613426, "dur": 65, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751308336615817, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 19704, "tid": 1470, "ts": 1751308336628364, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751308335838711, "dur": 67, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308335838806, "dur": 3558, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308335842388, "dur": 1045, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308335843593, "dur": 84, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751308335843678, "dur": 673, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308335845706, "dur": 1368, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_60AF5DD29DC33809.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308335847636, "dur": 142, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3EF742C8CA4C5E1E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308335849594, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751308335851191, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_CE6A42C97D96EB0A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751308335852708, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751308335858674, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751308335860698, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751308335861098, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751308335862155, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751308335863094, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751308335864173, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751308335865342, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751308335868810, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751308335869295, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751308335869725, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751308335844406, "dur": 25724, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308335870147, "dur": 724234, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308336594383, "dur": 312, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308336594696, "dur": 97, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308336594827, "dur": 52, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308336595088, "dur": 112, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308336595246, "dur": 1725, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751308335844559, "dur": 25643, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335870228, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335870372, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_44462716F1E6187F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335870543, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_E681FA607883332E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335870619, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_D32EF602AAA54BE6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335870807, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_595F986B2A245834.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335870949, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BA0CB78B239CCACF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335871100, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_658DA73782B14E20.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335871191, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_433711FCB6016F00.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335871273, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_1A2A3B79BD3B4CFD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335871526, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_C1BEC5DDD68DA9AF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335871636, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_922751811D286E63.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335871713, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_922751811D286E63.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335871841, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335871903, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335871967, "dur": 329, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335872316, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335872497, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335872723, "dur": 10546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751308335883442, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335883734, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335885111, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335885933, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335886255, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335886585, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335886953, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335887285, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335887538, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335887972, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335888866, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335889373, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335890503, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335891316, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335892621, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335893706, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335894346, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335894643, "dur": 915, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335895560, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751308335896310, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335896392, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335896499, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335896566, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335896909, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335897138, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335897381, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335897604, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335897988, "dur": 912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751308335898901, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335899344, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335899783, "dur": 859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751308335900643, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335900787, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335901031, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335901182, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751308335901397, "dur": 834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751308335902232, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335902333, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335902395, "dur": 88643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335991042, "dur": 2563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751308335993606, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308335993661, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751308335993777, "dur": 6371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751308336000151, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308336000237, "dur": 4073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751308336004312, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308336004712, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751308336004783, "dur": 2779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751308336007614, "dur": 3002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751308336010618, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308336010724, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751308336010819, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308336010919, "dur": 58, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751308336010977, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308336011077, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308336011300, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308336011734, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308336011980, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308336012167, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751308336012405, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308336012528, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308336012730, "dur": 477764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751308336490556, "dur": 304, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751308336490496, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751308336490912, "dur": 2193, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751308336493110, "dur": 101309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335844644, "dur": 25570, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335870222, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_82F8B03BA9650A81.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335870662, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335870725, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_1DC8148833156840.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335870816, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_C047F56604413718.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335870888, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_308026626194FDB8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335870962, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0999ABDFA115F312.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335871048, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_DBB153F07C192DFF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335871127, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3E1DF6609ACAC61F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335871210, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1FE2264601546D0E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335871304, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_93B77B82222D1B4A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335871521, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_0B2798972D9D68EB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335871599, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FEA05FC664382286.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335871736, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_12124AFB436F9D5D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335871952, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751308335872166, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751308335872391, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751308335872636, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751308335872858, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751308335873066, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751308335873117, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335873205, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751308335873575, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751308335873884, "dur": 263, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751308335874275, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751308335874364, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751308335874500, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15875538839575725175.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751308335874700, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335875030, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751308335875149, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335875571, "dur": 1530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335877102, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335878465, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335879201, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335879802, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335880053, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335880993, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335881294, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335881699, "dur": 560, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\Inspector\\PropertyDrawers\\BoolPropertyDrawer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751308335881552, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335882428, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335882675, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335882911, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335884174, "dur": 790, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Editor\\LookDev\\ComparisonGizmoState.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751308335883741, "dur": 1516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335885257, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335885647, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335885985, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335886452, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335886846, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335887244, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335887491, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335888833, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335889583, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335890503, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335891461, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335892533, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335893705, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335894187, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335894629, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335895304, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335895507, "dur": 802, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751308335896310, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335896427, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335896772, "dur": 2201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751308335898974, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335899106, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335899424, "dur": 1542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751308335900967, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335901088, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335901145, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335901393, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751308335901993, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335902087, "dur": 4820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335906909, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751308335907062, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751308335907470, "dur": 83558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335991031, "dur": 2614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751308335993647, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335993755, "dur": 2428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751308335996184, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335996293, "dur": 2521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751308335998816, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308335998880, "dur": 2029, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751308336000920, "dur": 6901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751308336007823, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308336008254, "dur": 3274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751308336011529, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308336011684, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308336011849, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308336012021, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308336012539, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751308336013179, "dur": 581197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335844730, "dur": 25497, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335870236, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_5F13DCF03A10B823.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335870501, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_F8CCEDBE36EB62B5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335870587, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_8DE949524442C9D8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335870666, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335870731, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F27EBD72F4881481.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335870821, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_9D64899DE49D3B06.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335870904, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_A164BAE5670FC544.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335871094, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8B3842F7B08DFF89.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335871171, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8B3842F7B08DFF89.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335871293, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_7DC1C7578DCAB919.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335871483, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E689A207C3E7A0C2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335871557, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_5FDF6992734E0361.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335871743, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_486DF76931BD0557.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335871823, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_E106F825F731F4BC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335871933, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_0854A01FF08BC637.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335872561, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335872714, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751308335872868, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751308335873080, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751308335873229, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751308335873449, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751308335873557, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751308335873666, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751308335873881, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751308335874369, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751308335874660, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335874966, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751308335875165, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335875598, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335876645, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335878247, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335878805, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335879766, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335880034, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335880303, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335880764, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335881707, "dur": 1142, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\Inspector\\PropertyDrawers\\SampleTexture2DNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751308335881490, "dur": 1450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335882941, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335883544, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335884180, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Editor\\Camera\\ISerializedCamera.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751308335883848, "dur": 1563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335885412, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335885695, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335885980, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335886551, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335886908, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335887326, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335887658, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335888151, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335888489, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335888750, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335889582, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335890747, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335891999, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335892913, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335894190, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335894628, "dur": 662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335895300, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335895503, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751308335896205, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335896399, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335896944, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335897147, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335897320, "dur": 758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751308335898079, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335898340, "dur": 747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751308335899088, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335899339, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335899655, "dur": 1023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751308335900679, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335900903, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335901055, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335901176, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335901378, "dur": 500, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335901881, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751308335902579, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335902749, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335902894, "dur": 796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751308335903691, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335903781, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335903964, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751308335904777, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751308335904990, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751308335905913, "dur": 87767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335993696, "dur": 2482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751308335996179, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335996278, "dur": 2536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751308335998816, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308335998915, "dur": 2738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751308336001656, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751308336001733, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751308336001798, "dur": 5199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751308336007042, "dur": 6006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751308336013163, "dur": 581238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308335844548, "dur": 25630, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308335870227, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308335870353, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_0E82E95F1D4B1B8B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308335870734, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_B83C6071BB394334.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308335870845, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_67CB883191F4D66D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308335871085, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_BEB6379C07D3D185.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308335871150, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_BEB6379C07D3D185.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308335871343, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7B8E45163E4907D2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308335871541, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_584BDBB0AACB340C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308335871747, "dur": 305, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_20A6B658F2F96E47.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308335872072, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308335872218, "dur": 12176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751308335884473, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308335884660, "dur": 9883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751308335894675, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308335894798, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751308335895300, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308335895493, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751308335896386, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308335896690, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_E8EFDB1B8D5C174E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308335896841, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308335897133, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751308335897956, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308335898033, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751308335898803, "dur": 1043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751308335899847, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308335899948, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751308335900494, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308335900915, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308335901390, "dur": 811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751308335902202, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308335902269, "dur": 5377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308335907648, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751308335907830, "dur": 83222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308335991055, "dur": 2549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751308335993606, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308335993659, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751308335993733, "dur": 2444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751308335996179, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308335996293, "dur": 2569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751308335998863, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308335998966, "dur": 2938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751308336001963, "dur": 4569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751308336006534, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751308336006809, "dur": 2483, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751308336009299, "dur": 3360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751308336012748, "dur": 581641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335844821, "dur": 25421, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335870256, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_256223B421F6FD72.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335870676, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_0E07881F85CDD302.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335870805, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_E5318FF293833B30.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335870883, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_5DFD03B93DA29B4D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335870959, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_7676AB112B74F121.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335871151, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_7676AB112B74F121.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335871271, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D7CA9FED890DDAB1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335871487, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D7CA9FED890DDAB1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335871553, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_CBABBF352E91654E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335871735, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A587944D9110748A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335871831, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_7AC1C063560F2BC6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335871940, "dur": 284, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_7AC1C063560F2BC6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335872382, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751308335872638, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751308335873011, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751308335873156, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335873210, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751308335873316, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751308335873506, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335873575, "dur": 379, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751308335873956, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751308335874044, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751308335874322, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751308335874660, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335874997, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751308335875155, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335875644, "dur": 1653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335877297, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335877898, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335878571, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335879678, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335879936, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335880209, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335880959, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335881707, "dur": 593, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Generation\\GraphCode.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751308335881274, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335882338, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335882592, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335882828, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335883265, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335883581, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335884224, "dur": 686, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.State\\OnEnterState.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751308335883981, "dur": 1439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335885421, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335885690, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335886136, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335886563, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335886928, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335887282, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335887617, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335888191, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335888540, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335888840, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335889144, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335890093, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335891163, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335892475, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335893430, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335893852, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335894319, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335894646, "dur": 687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335895335, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335895532, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335895702, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335896264, "dur": 1065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751308335897338, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335897778, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335897979, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751308335898763, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335898899, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335899084, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751308335899300, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335899370, "dur": 1307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751308335900678, "dur": 494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335901240, "dur": 1086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1751308335902328, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335902405, "dur": 262, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335903263, "dur": 77535, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1751308335991050, "dur": 2596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751308335993648, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335993763, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751308335993840, "dur": 2562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751308335996405, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335996478, "dur": 2396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751308335998876, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308335998972, "dur": 2804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751308336001779, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308336001882, "dur": 2428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751308336004312, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308336004411, "dur": 2834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751308336007247, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751308336007357, "dur": 2307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751308336009723, "dur": 2942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751308336012750, "dur": 581629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335844875, "dur": 25394, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335870283, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_CC3B7EF1A2C4EA40.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335870750, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_76EB92ADF25C401C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335870838, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7E47E9336205E31D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335870940, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_EE062F8861099912.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335871059, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_64D9AC7F4C4AC8A8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335871189, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_FE92BD4D4925733A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335871278, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_77F91A8BF54EFB7C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335871506, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_C01CA8838C1CE31C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335871731, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_1638DBF3D84589E5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335871822, "dur": 208, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_1638DBF3D84589E5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335872451, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751308335872643, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751308335873196, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751308335873322, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751308335873433, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751308335873656, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751308335873872, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751308335874085, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751308335874407, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6154019870087291391.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751308335874608, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14402696373757716438.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751308335874677, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335874971, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14402696373757716438.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751308335875171, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335876040, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335877486, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335878266, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335878887, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335879605, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335880046, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335880532, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335880965, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335881251, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335881699, "dur": 574, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\Colors\\UserColors.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751308335881685, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335882505, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335882746, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335883265, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335883553, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335883816, "dur": 1877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335885694, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335885958, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335886344, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335886670, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335887169, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335887403, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335887766, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335888136, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335888613, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335888878, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335889263, "dur": 1429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335890693, "dur": 1424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335892118, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335892971, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335893894, "dur": 67, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335894192, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335894658, "dur": 1072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335895731, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335895935, "dur": 2641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751308335898578, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335898889, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335899152, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335899341, "dur": 1134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751308335900476, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335900795, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335900903, "dur": 886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335901887, "dur": 2113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335904002, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335904184, "dur": 1657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751308335905844, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335905952, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335906120, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751308335906900, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335907058, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751308335907639, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751308335907807, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751308335908720, "dur": 74, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308335910320, "dur": 249689, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751308336166736, "dur": 12499, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751308336166336, "dur": 12974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751308336179668, "dur": 104, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308336180748, "dur": 226027, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751308336413845, "dur": 24660, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751308336413834, "dur": 26462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751308336441770, "dur": 387, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751308336443071, "dur": 85580, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751308336560066, "dur": 353, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751308336560054, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751308336560448, "dur": 1213, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751308336561669, "dur": 32728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335844944, "dur": 25343, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335870302, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7B82E1658E753B21.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308335870753, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9843DD54C5E6159E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308335870945, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0FEAE2B241ACD344.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308335871065, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E04F87D224606974.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308335871236, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E04F87D224606974.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308335871356, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DA7D4247AC2DB3F7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308335871582, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_815CA55F1B3FC2AD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308335871658, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_1FF1C2755BAA46D4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308335871981, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308335872373, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751308335872503, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308335872693, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308335873014, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308335873132, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308335873199, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308335873341, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751308335873467, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308335873544, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308335873819, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335873877, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308335874259, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308335874725, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335875049, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14502112590723880366.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751308335875184, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335876453, "dur": 1751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335878205, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335878611, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335879545, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335879869, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335880168, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335881108, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335881704, "dur": 591, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Extensions\\IConditionalExtensions.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751308335881363, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335882376, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335882618, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335882851, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335883267, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335883614, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335884816, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335885457, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335885846, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335886331, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335886886, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335887224, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335887470, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335888301, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335888544, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335888779, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335889677, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335890728, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335891849, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335892918, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335894062, "dur": 84, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335894185, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335894635, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335895287, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308335895522, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308335896244, "dur": 1024, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335897343, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308335897591, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335897680, "dur": 1110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308335898790, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335898970, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308335899512, "dur": 1422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308335900935, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335901134, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308335901406, "dur": 1013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308335902420, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335902739, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308335902915, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308335903837, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335903985, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751308335904179, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751308335904740, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335905022, "dur": 86024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335991048, "dur": 2557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751308335993606, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308335993659, "dur": 3874, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751308335997543, "dur": 3287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751308336000832, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308336001221, "dur": 4747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751308336005971, "dur": 656, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308336006643, "dur": 2625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751308336009270, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751308336009418, "dur": 3630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751308336013151, "dur": 581222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335844999, "dur": 25316, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335870330, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_8A89D5AFAE49E63E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335870495, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_467B09015CCE177C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335870575, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_1182EE28E68C62C8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335870660, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_14A5559FFED55C50.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335870738, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DAD9D37CE1A4BA55.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335870826, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_4E1AB9DAA17812DB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335870908, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A619838E516A75AC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335870984, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D7031BE9F197A0A6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335871061, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BADDD17E000661FB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335871224, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BADDD17E000661FB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335871326, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_D4344483B74B03A7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335871562, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A28CE54978CD971D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335871787, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751308335871934, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_07FA64190ECA4CC1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335872161, "dur": 286, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751308335872505, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751308335872734, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335872898, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751308335873061, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751308335873241, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751308335873393, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751308335873518, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335873582, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751308335873820, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751308335874400, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2897256077774953845.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751308335874490, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751308335874667, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335874970, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1177933628359149393.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751308335875214, "dur": 1657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335876873, "dur": 1874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335878748, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335879717, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335880051, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335880374, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335880738, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335881575, "dur": 1235, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\Interfaces\\ISGViewModel.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751308335881470, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335882945, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335883342, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335883758, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335885202, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335885471, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335885793, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335886040, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335886489, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335886834, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335887791, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335888200, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335888508, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335888994, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335889982, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335890993, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335892250, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335893176, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335893551, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335893766, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335894197, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335894641, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335895290, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335895499, "dur": 2020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751308335897520, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335897611, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335897854, "dur": 2229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751308335900234, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335900541, "dur": 1747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751308335902289, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335902454, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751308335902727, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751308335903454, "dur": 87578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335991034, "dur": 2612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751308335993648, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335993764, "dur": 2659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751308335996424, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335996487, "dur": 2707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751308335999195, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308335999306, "dur": 3239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751308336002593, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308336002690, "dur": 3235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751308336005929, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308336006040, "dur": 5670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751308336011806, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308336011953, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308336012373, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308336012533, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308336013136, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751308336013243, "dur": 581162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335845084, "dur": 25252, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335870350, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_A1485D01311C863E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335870760, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_8EA224AC6679B7C2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335870841, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C8A867A03915DFB5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335870955, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_60AF5DD29DC33809.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335871069, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_DB48149D0776AF57.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335871145, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_DB48149D0776AF57.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335871216, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B1D6DCAB2D5A18DA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335871295, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B6153E13308654D1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335871480, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_EAAAA0867963BCF7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335871709, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_1FC72A0013DC4EEB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335871788, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_1FC72A0013DC4EEB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335871936, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7F3203F2669A3DC6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335872068, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751308335872416, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751308335872517, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751308335872860, "dur": 308, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751308335873377, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751308335873562, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751308335873732, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751308335873992, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751308335874152, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335874399, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751308335874627, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10719215101466552486.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751308335874712, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335874999, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751308335875122, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751308335875367, "dur": 1409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335876777, "dur": 1461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335878238, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335878503, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335879392, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335879903, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335880187, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335880985, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335881698, "dur": 595, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Generation\\TargetResources\\Structs.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751308335881273, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335882355, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335882590, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335882828, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335883351, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335884216, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Editor\\RenderGraph\\RenderGraphViewer.PanManipulator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751308335883671, "dur": 1642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335885313, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335885979, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335886407, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335886864, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335887206, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335887446, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335888143, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335888477, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335888932, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335889838, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335890869, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335892094, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335892935, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335893802, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335894365, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335894656, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335895331, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335895516, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335895735, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751308335896527, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335896723, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335896830, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335897119, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751308335897915, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335898034, "dur": 674, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335898722, "dur": 1011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751308335899734, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335899938, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335900146, "dur": 965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751308335901112, "dur": 806, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335901924, "dur": 2343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335904273, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751308335904493, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751308335904970, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335905054, "dur": 86038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308335991094, "dur": 2510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751308335993702, "dur": 2476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751308335996179, "dur": 6484, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308336002665, "dur": 1714, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751308336004389, "dur": 4153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751308336008544, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308336008639, "dur": 3692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751308336012332, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308336012478, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308336012663, "dur": 153678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308336166394, "dur": 166205, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751308336166346, "dur": 180388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751308336354223, "dur": 1142, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308336355380, "dur": 206, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751308336357065, "dur": 97862, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751308336490480, "dur": 102465, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751308336490468, "dur": 102479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751308336592974, "dur": 1347, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751308335845178, "dur": 25270, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335870450, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F697CEAF687CB7A2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335870579, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_596B249A76101BE6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335870673, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_15A0A72BE0744047.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335870797, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_51B6A25823037467.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335870874, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_526B70D8724FDBD0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335870952, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_91BF37D1F055EC15.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335871026, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_91BF37D1F055EC15.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335871102, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F0EAC96F0603CD08.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335871187, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_472976495F1230C7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335871267, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_8FE2697BF98A2193.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335871455, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_F21194D2F617C9ED.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335871536, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_D1635D1E3A6F959D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335871681, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AFE94C31B12D3213.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335871905, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335871964, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335872249, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751308335872375, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751308335872644, "dur": 294, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751308335873018, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751308335873218, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751308335873381, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751308335873565, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751308335873753, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751308335874493, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751308335874584, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751308335874713, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335874995, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751308335875120, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751308335875276, "dur": 1872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335877148, "dur": 1486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335878635, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335879629, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335879886, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335880145, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335880869, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335881214, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335881589, "dur": 715, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\Views\\ContextView.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751308335881460, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335882443, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335882701, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335882964, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335883531, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335883853, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335884743, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335885157, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335885438, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335885757, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335886033, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335886484, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335886831, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335887173, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335887918, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335888372, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335888642, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335889233, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335890074, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335890926, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335892109, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335893143, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335894199, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335894656, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335895316, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335895489, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335895599, "dur": 2054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751308335897654, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335898036, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335898333, "dur": 851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751308335899185, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335899263, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751308335899390, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335899809, "dur": 726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751308335900537, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335900923, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335901178, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751308335901395, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751308335902145, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335902278, "dur": 39151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335943895, "dur": 469, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 10, "ts": 1751308335944365, "dur": 1924, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 10, "ts": 1751308335946290, "dur": 174, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 10, "ts": 1751308335941430, "dur": 5045, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335946476, "dur": 47244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308335993723, "dur": 5270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751308335999044, "dur": 2682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751308336001728, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308336001802, "dur": 5758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751308336007562, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308336007632, "dur": 2895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751308336010529, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308336010625, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308336010875, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308336010977, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308336011128, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308336011333, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308336011757, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751308336011875, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Updater.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751308336012114, "dur": 441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308336012558, "dur": 897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751308336013515, "dur": 580908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335845240, "dur": 25142, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335870391, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FECC8B8B813FB02A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335870518, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_69FC355D1BD4BB29.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335870597, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0333895C18D609C9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335870668, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_B566D0CB2E423B86.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335870757, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_33AFB99E7761AA5C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335871052, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8D9A2E8777AB6882.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335871249, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_D8FCB30ADD921A73.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335871310, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335871460, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_9870EDF59A107BDF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335871739, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_228E07120E318326.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335871835, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_0D77228FB13B7FB6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335871950, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_0D77228FB13B7FB6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335872115, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751308335872193, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751308335872282, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_3D651E01942E03F8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335872385, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_3D651E01942E03F8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335872568, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751308335872702, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751308335872949, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751308335873309, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751308335873435, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751308335873564, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751308335873729, "dur": 250, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751308335873981, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751308335874081, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751308335874297, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751308335874599, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751308335874655, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335874966, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751308335875188, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335876941, "dur": 1756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335878698, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335879752, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335880281, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335881151, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335881706, "dur": 587, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\Views\\Slots\\GradientSlotControlView.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751308335881391, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335882478, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335882722, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335883039, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335883586, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335884738, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335885218, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335885474, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335885756, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335886273, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335886576, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335887203, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335887426, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335888370, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335888615, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335888864, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335889381, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335890599, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335891516, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335892637, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335893724, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335894430, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335894659, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335895299, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335895535, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751308335896385, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335896544, "dur": 775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335897371, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335897622, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751308335898377, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335898856, "dur": 912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751308335899769, "dur": 766, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335900550, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335900798, "dur": 1977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751308335902776, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335903016, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335903237, "dur": 903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751308335904237, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335904449, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751308335905166, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335905268, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751308335905468, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751308335906074, "dur": 84986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335991063, "dur": 2582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751308335993647, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335993732, "dur": 2542, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751308335996329, "dur": 2644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751308335998974, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308335999044, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751308335999156, "dur": 7489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751308336006648, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308336006807, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751308336006903, "dur": 2520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751308336009425, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308336009525, "dur": 3832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751308336013359, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751308336013481, "dur": 580932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335845304, "dur": 25128, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335870443, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_8325F75A1495950D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335870590, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_857E61E190401437.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335870967, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_BFD5601BA05C7CD8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335871043, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_BFD5601BA05C7CD8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335871122, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_209987431F1A3EDC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335871254, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_BB587A5119495C88.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335871310, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335871441, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_BB587A5119495C88.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335871539, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_48483D327EA17808.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335871639, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3EF742C8CA4C5E1E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335871960, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751308335872167, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751308335872387, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751308335872632, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751308335872869, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751308335873136, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751308335873337, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751308335873420, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751308335873561, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751308335874221, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751308335874381, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751308335874521, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751308335874605, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751308335874714, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335875003, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751308335875126, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751308335875410, "dur": 1875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335877286, "dur": 1359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335878645, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335879816, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335880069, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335880987, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335881293, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335881702, "dur": 577, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\Controls\\IControlAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751308335881577, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335882476, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335882715, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335882958, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335883300, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335883817, "dur": 1926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335885744, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335886247, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335886586, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335886932, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335887241, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335887486, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335888319, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335888574, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335888876, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335889198, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335890050, "dur": 1380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335891430, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335892491, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335893727, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335894430, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335894634, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335895315, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335895478, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335895538, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751308335896142, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335896573, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335896930, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335897156, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751308335897956, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335898020, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335898319, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335898433, "dur": 721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751308335899154, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335899303, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751308335899418, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335899628, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335899852, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335900116, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751308335900210, "dur": 1162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751308335901373, "dur": 966, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335902349, "dur": 44134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335946484, "dur": 44559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335991047, "dur": 2557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751308335993606, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335993664, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751308335993922, "dur": 2630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751308335996554, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335996661, "dur": 2215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751308335998878, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308335998963, "dur": 3535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751308336002500, "dur": 1899, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336004414, "dur": 5314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751308336009729, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336009815, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336010084, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336010251, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336010508, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336010601, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336010719, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336010948, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336011030, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336011124, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336011213, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336011556, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336011790, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336012041, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336012151, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751308336012273, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336012520, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336012743, "dur": 547339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751308336560115, "dur": 322, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751308336560083, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751308336560479, "dur": 1177, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751308336561665, "dur": 32756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751308336604110, "dur": 2417, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 19704, "tid": 1470, "ts": 1751308336628957, "dur": 2869, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 19704, "tid": 1470, "ts": 1751308336631906, "dur": 2737, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 19704, "tid": 1470, "ts": 1751308336623425, "dur": 12727, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}