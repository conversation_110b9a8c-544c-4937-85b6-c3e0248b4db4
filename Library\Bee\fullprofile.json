{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 19704, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 19704, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 19704, "tid": 2513, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 19704, "tid": 2513, "ts": 1751311374856399, "dur": 844, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 19704, "tid": 2513, "ts": 1751311374862218, "dur": 828, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 19704, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 19704, "tid": 1, "ts": 1751311374127911, "dur": 6837, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19704, "tid": 1, "ts": 1751311374134753, "dur": 56753, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19704, "tid": 1, "ts": 1751311374191531, "dur": 41572, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 19704, "tid": 2513, "ts": 1751311374863050, "dur": 13, "ph": "X", "name": "", "args": {}}, {"pid": 19704, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374125663, "dur": 4590, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374130258, "dur": 714727, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374131503, "dur": 3169, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374134681, "dur": 2068, "ph": "X", "name": "ProcessMessages 17533", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374136757, "dur": 384, "ph": "X", "name": "ReadAsync 17533", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137145, "dur": 12, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137158, "dur": 88, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137249, "dur": 1, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137251, "dur": 70, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137324, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137326, "dur": 70, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137399, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137401, "dur": 51, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137455, "dur": 91, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137548, "dur": 1, "ph": "X", "name": "ProcessMessages 1200", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137551, "dur": 51, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137604, "dur": 1, "ph": "X", "name": "ProcessMessages 1371", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137605, "dur": 26, "ph": "X", "name": "ReadAsync 1371", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137634, "dur": 114, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137753, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137812, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137814, "dur": 103, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137920, "dur": 1, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137922, "dur": 62, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137987, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374137989, "dur": 66, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138057, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138060, "dur": 36, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138098, "dur": 47, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138147, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138150, "dur": 43, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138195, "dur": 1, "ph": "X", "name": "ProcessMessages 915", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138197, "dur": 50, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138250, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138253, "dur": 55, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138311, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138313, "dur": 60, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138378, "dur": 1, "ph": "X", "name": "ProcessMessages 1158", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138380, "dur": 44, "ph": "X", "name": "ReadAsync 1158", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138426, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138428, "dur": 50, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138480, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138483, "dur": 128, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138612, "dur": 1, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138614, "dur": 38, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138654, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138656, "dur": 54, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138712, "dur": 1, "ph": "X", "name": "ProcessMessages 1150", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138714, "dur": 32, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138748, "dur": 34, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138784, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138786, "dur": 52, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138842, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138845, "dur": 41, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138887, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138889, "dur": 28, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138920, "dur": 32, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138954, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374138990, "dur": 31, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139024, "dur": 29, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139058, "dur": 36, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139097, "dur": 27, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139126, "dur": 32, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139159, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139161, "dur": 30, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139194, "dur": 31, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139227, "dur": 46, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139275, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139276, "dur": 24, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139303, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139354, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139394, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139395, "dur": 33, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139430, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139431, "dur": 39, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139473, "dur": 29, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139504, "dur": 31, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139538, "dur": 31, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139571, "dur": 31, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139603, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139606, "dur": 34, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139642, "dur": 28, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139674, "dur": 36, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139712, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139747, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139748, "dur": 30, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139781, "dur": 33, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139817, "dur": 30, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139850, "dur": 28, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139880, "dur": 86, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139968, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374139969, "dur": 50, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140021, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140023, "dur": 32, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140057, "dur": 34, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140094, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140129, "dur": 33, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140165, "dur": 30, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140198, "dur": 31, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140235, "dur": 28, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140266, "dur": 31, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140299, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140301, "dur": 41, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140346, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140348, "dur": 51, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140401, "dur": 4, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140407, "dur": 29, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140437, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140439, "dur": 31, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140473, "dur": 48, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140524, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140561, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140562, "dur": 34, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140599, "dur": 42, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140644, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140645, "dur": 24, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140673, "dur": 39, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140715, "dur": 35, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140752, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140754, "dur": 42, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140799, "dur": 27, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140830, "dur": 28, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140861, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140863, "dur": 30, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140895, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140896, "dur": 84, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140987, "dur": 2, "ph": "X", "name": "ProcessMessages 1097", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374140990, "dur": 36, "ph": "X", "name": "ReadAsync 1097", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141028, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141030, "dur": 263, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141311, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141380, "dur": 2, "ph": "X", "name": "ProcessMessages 2434", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141383, "dur": 45, "ph": "X", "name": "ReadAsync 2434", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141432, "dur": 37, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141471, "dur": 1, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141473, "dur": 41, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141517, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141519, "dur": 29, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141551, "dur": 49, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141609, "dur": 3, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141614, "dur": 48, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141666, "dur": 33, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141701, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141703, "dur": 40, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141745, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141746, "dur": 40, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141789, "dur": 23, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141814, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141844, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141910, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141912, "dur": 81, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374141998, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142044, "dur": 2, "ph": "X", "name": "ProcessMessages 2011", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142048, "dur": 46, "ph": "X", "name": "ReadAsync 2011", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142096, "dur": 1, "ph": "X", "name": "ProcessMessages 1061", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142098, "dur": 35, "ph": "X", "name": "ReadAsync 1061", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142135, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142136, "dur": 27, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142166, "dur": 40, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142209, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142211, "dur": 46, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142260, "dur": 46, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142308, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142310, "dur": 37, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142348, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142350, "dur": 38, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142392, "dur": 46, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142443, "dur": 1, "ph": "X", "name": "ProcessMessages 1109", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142445, "dur": 33, "ph": "X", "name": "ReadAsync 1109", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142481, "dur": 32, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142517, "dur": 31, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142552, "dur": 36, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142589, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142591, "dur": 32, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142624, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142625, "dur": 36, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142663, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142665, "dur": 29, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142698, "dur": 36, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142736, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142737, "dur": 30, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142771, "dur": 32, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142807, "dur": 30, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142838, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142841, "dur": 33, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142876, "dur": 65, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142945, "dur": 36, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142982, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374142984, "dur": 41, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143026, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143027, "dur": 25, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143055, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143094, "dur": 28, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143124, "dur": 66, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143192, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143194, "dur": 34, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143230, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143233, "dur": 30, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143266, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143315, "dur": 1, "ph": "X", "name": "ProcessMessages 1079", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143317, "dur": 39, "ph": "X", "name": "ReadAsync 1079", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143359, "dur": 36, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143398, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143399, "dur": 31, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143432, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143434, "dur": 39, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143476, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143478, "dur": 50, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143529, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143532, "dur": 34, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143571, "dur": 31, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143605, "dur": 32, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143639, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143641, "dur": 42, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143686, "dur": 48, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143735, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143737, "dur": 32, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143771, "dur": 27, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143801, "dur": 33, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143837, "dur": 37, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143877, "dur": 31, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143911, "dur": 28, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143942, "dur": 35, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374143980, "dur": 33, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144016, "dur": 31, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144050, "dur": 27, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144079, "dur": 29, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144111, "dur": 34, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144147, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144149, "dur": 27, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144179, "dur": 37, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144218, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144219, "dur": 30, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144253, "dur": 40, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144296, "dur": 40, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144337, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144339, "dur": 46, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144388, "dur": 38, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144428, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144431, "dur": 42, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144475, "dur": 31, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144509, "dur": 37, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144548, "dur": 2, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144551, "dur": 39, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144594, "dur": 26, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144622, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144624, "dur": 34, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144661, "dur": 42, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144707, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144709, "dur": 64, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144779, "dur": 3, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144783, "dur": 47, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144835, "dur": 22, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144859, "dur": 41, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144902, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144905, "dur": 34, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144941, "dur": 1, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144943, "dur": 35, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374144981, "dur": 41, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145024, "dur": 2, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145027, "dur": 52, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145081, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145082, "dur": 44, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145129, "dur": 30, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145162, "dur": 30, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145195, "dur": 35, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145231, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145233, "dur": 34, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145270, "dur": 31, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145306, "dur": 28, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145337, "dur": 44, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145383, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145384, "dur": 35, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145420, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145422, "dur": 34, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145458, "dur": 31, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145512, "dur": 35, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145548, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145551, "dur": 35, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145589, "dur": 48, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145638, "dur": 1, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145640, "dur": 30, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145673, "dur": 33, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145709, "dur": 33, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145745, "dur": 72, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145819, "dur": 32, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145855, "dur": 44, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145901, "dur": 38, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145942, "dur": 43, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374145988, "dur": 40, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146033, "dur": 32, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146068, "dur": 30, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146101, "dur": 33, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146135, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146137, "dur": 35, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146175, "dur": 32, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146209, "dur": 50, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146262, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146265, "dur": 37, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146304, "dur": 38, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146345, "dur": 43, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146391, "dur": 48, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146442, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146444, "dur": 44, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146490, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146494, "dur": 37, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146533, "dur": 1, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146535, "dur": 46, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146583, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146584, "dur": 38, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146624, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146625, "dur": 35, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146662, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146719, "dur": 2, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146723, "dur": 42, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146767, "dur": 1, "ph": "X", "name": "ProcessMessages 1006", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146769, "dur": 35, "ph": "X", "name": "ReadAsync 1006", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146805, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146807, "dur": 31, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146841, "dur": 44, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146886, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146887, "dur": 35, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146925, "dur": 32, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146960, "dur": 37, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374146999, "dur": 27, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147029, "dur": 35, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147068, "dur": 34, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147104, "dur": 40, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147147, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147149, "dur": 65, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147217, "dur": 35, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147254, "dur": 31, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147288, "dur": 41, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147332, "dur": 27, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147361, "dur": 31, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147394, "dur": 57, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147453, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147455, "dur": 37, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147494, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147495, "dur": 49, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147549, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147553, "dur": 41, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147595, "dur": 1, "ph": "X", "name": "ProcessMessages 911", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147597, "dur": 32, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147632, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147634, "dur": 29, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147664, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147666, "dur": 38, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147708, "dur": 42, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147751, "dur": 1, "ph": "X", "name": "ProcessMessages 1128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147753, "dur": 36, "ph": "X", "name": "ReadAsync 1128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147792, "dur": 29, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147824, "dur": 42, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147868, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147869, "dur": 39, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147909, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147911, "dur": 32, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147946, "dur": 31, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374147980, "dur": 28, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148011, "dur": 43, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148056, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148057, "dur": 24, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148084, "dur": 32, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148119, "dur": 31, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148153, "dur": 26, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148182, "dur": 35, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148220, "dur": 37, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148260, "dur": 32, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148294, "dur": 32, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148329, "dur": 36, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148368, "dur": 30, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148401, "dur": 50, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148453, "dur": 33, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148488, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148489, "dur": 31, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148522, "dur": 32, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148557, "dur": 27, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148586, "dur": 30, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148619, "dur": 34, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148655, "dur": 38, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148697, "dur": 28, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148727, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148729, "dur": 32, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148763, "dur": 28, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148795, "dur": 37, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148834, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148865, "dur": 42, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148910, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148943, "dur": 40, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374148986, "dur": 107, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149097, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149155, "dur": 1, "ph": "X", "name": "ProcessMessages 1159", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149157, "dur": 61, "ph": "X", "name": "ReadAsync 1159", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149221, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149256, "dur": 42, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149299, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149300, "dur": 78, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149380, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149417, "dur": 30, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149450, "dur": 26, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149478, "dur": 74, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149556, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149589, "dur": 41, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149633, "dur": 77, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149712, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149746, "dur": 29, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149778, "dur": 70, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149851, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149885, "dur": 28, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149916, "dur": 31, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374149949, "dur": 106, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150068, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150074, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150153, "dur": 4, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150158, "dur": 93, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150256, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150258, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150313, "dur": 2, "ph": "X", "name": "ProcessMessages 931", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150316, "dur": 33, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150352, "dur": 58, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150413, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150493, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150494, "dur": 34, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150530, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150532, "dur": 63, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150597, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150630, "dur": 30, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150662, "dur": 28, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150692, "dur": 73, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150768, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150802, "dur": 46, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150851, "dur": 74, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150928, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374150965, "dur": 64, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151032, "dur": 31, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151066, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151067, "dur": 38, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151108, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151165, "dur": 1, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151166, "dur": 33, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151202, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151267, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151303, "dur": 51, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151357, "dur": 30, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151390, "dur": 62, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151455, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151492, "dur": 37, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151531, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151532, "dur": 76, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151611, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151648, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151649, "dur": 37, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151688, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151689, "dur": 72, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151764, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151817, "dur": 29, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151848, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151849, "dur": 78, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151930, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151966, "dur": 30, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374151998, "dur": 29, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152030, "dur": 67, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152100, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152148, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152150, "dur": 54, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152207, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152208, "dur": 32, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152244, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152305, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152340, "dur": 33, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152376, "dur": 32, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152409, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152411, "dur": 50, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152468, "dur": 2, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152474, "dur": 46, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152524, "dur": 32, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152559, "dur": 30, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152592, "dur": 28, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152623, "dur": 62, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152688, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152741, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152743, "dur": 51, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152799, "dur": 33, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152834, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152836, "dur": 30, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152869, "dur": 34, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152906, "dur": 34, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152943, "dur": 33, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152977, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374152978, "dur": 36, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153018, "dur": 37, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153056, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153057, "dur": 34, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153094, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153126, "dur": 63, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153192, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153234, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153236, "dur": 36, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153276, "dur": 67, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153345, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153383, "dur": 42, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153426, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153428, "dur": 40, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153471, "dur": 55, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153528, "dur": 30, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153562, "dur": 27, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153591, "dur": 95, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153688, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153724, "dur": 34, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153762, "dur": 27, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153792, "dur": 65, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153860, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153895, "dur": 35, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153933, "dur": 26, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374153962, "dur": 63, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154028, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154063, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154064, "dur": 33, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154100, "dur": 36, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154137, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154139, "dur": 33, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154175, "dur": 63, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154240, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154275, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154276, "dur": 30, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154309, "dur": 79, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154390, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154391, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154426, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154428, "dur": 34, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154464, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154466, "dur": 27, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154496, "dur": 64, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154563, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154626, "dur": 39, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154668, "dur": 33, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154703, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154704, "dur": 79, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154786, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154838, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154840, "dur": 33, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154877, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154878, "dur": 65, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154945, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374154978, "dur": 33, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155014, "dur": 37, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155053, "dur": 79, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155135, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155185, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155188, "dur": 41, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155230, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155234, "dur": 31, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155269, "dur": 63, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155336, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155373, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155375, "dur": 36, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155414, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155415, "dur": 74, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155493, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155528, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155531, "dur": 31, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155565, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155567, "dur": 30, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155603, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155670, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155729, "dur": 1, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155732, "dur": 34, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155769, "dur": 56, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155828, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155870, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155872, "dur": 35, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155910, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155911, "dur": 84, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374155998, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156001, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156032, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156034, "dur": 48, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156085, "dur": 3, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156089, "dur": 33, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156124, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156126, "dur": 57, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156187, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156238, "dur": 2, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156241, "dur": 31, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156276, "dur": 74, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156354, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156395, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156399, "dur": 34, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156435, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156438, "dur": 70, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156510, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156511, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156547, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156551, "dur": 33, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156586, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156590, "dur": 73, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156676, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156726, "dur": 1, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156728, "dur": 33, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156763, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156767, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156822, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156880, "dur": 32, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156914, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156916, "dur": 37, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156955, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374156958, "dur": 68, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157029, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157068, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157071, "dur": 32, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157105, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157107, "dur": 70, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157183, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157262, "dur": 3, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157267, "dur": 32, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157301, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157304, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157357, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157360, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157404, "dur": 1, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157406, "dur": 31, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157440, "dur": 64, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157509, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157542, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157544, "dur": 33, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157580, "dur": 2, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157583, "dur": 30, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157614, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157617, "dur": 62, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157681, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157684, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157737, "dur": 2, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157741, "dur": 30, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374157774, "dur": 431, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158210, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158284, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158287, "dur": 68, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158358, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158362, "dur": 62, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158427, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158471, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158477, "dur": 49, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158529, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158531, "dur": 74, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158609, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158675, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158677, "dur": 111, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158791, "dur": 1, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158795, "dur": 46, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158844, "dur": 3, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158849, "dur": 32, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158885, "dur": 75, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374158964, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159004, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159006, "dur": 35, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159043, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159044, "dur": 79, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159128, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159164, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159166, "dur": 34, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159203, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159206, "dur": 92, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159301, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159336, "dur": 32, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159371, "dur": 44, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159418, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159419, "dur": 77, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159498, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159501, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159537, "dur": 64, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159606, "dur": 41, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159648, "dur": 1, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159651, "dur": 32, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159685, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159687, "dur": 38, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159727, "dur": 31, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159761, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159762, "dur": 69, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159835, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159870, "dur": 2, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159874, "dur": 29, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159907, "dur": 30, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374159940, "dur": 59, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160004, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160056, "dur": 35, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160093, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160096, "dur": 34, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160133, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160138, "dur": 35, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160174, "dur": 2, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160177, "dur": 27, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160207, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160209, "dur": 33, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160246, "dur": 90, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160339, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160341, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160389, "dur": 1, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160391, "dur": 32, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160425, "dur": 1, "ph": "X", "name": "ProcessMessages 198", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160428, "dur": 37, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160466, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160469, "dur": 40, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160512, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160515, "dur": 28, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160544, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160546, "dur": 32, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160580, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160582, "dur": 72, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160658, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160694, "dur": 264, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160968, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374160975, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161046, "dur": 369, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161420, "dur": 93, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161519, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161522, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161584, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161587, "dur": 51, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161646, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161650, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161712, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161716, "dur": 60, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161781, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161783, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161820, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161823, "dur": 26, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161851, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161852, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161888, "dur": 38, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161932, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161935, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161988, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374161992, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162043, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162047, "dur": 66, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162117, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162121, "dur": 67, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162194, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162197, "dur": 50, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162251, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162254, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162299, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162303, "dur": 55, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162362, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162365, "dur": 50, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162420, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162422, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162480, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162537, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162540, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162597, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162600, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162653, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162658, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162705, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162709, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162769, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162772, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162816, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162819, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162847, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162888, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162891, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162937, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162940, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374162971, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163011, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163014, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163054, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163057, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163098, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163101, "dur": 37, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163142, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163146, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163186, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163188, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163232, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163236, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163273, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163275, "dur": 29, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163307, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163309, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163341, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163343, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163381, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163384, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163422, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163461, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163464, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163500, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163502, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163542, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163544, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163586, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163588, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163632, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163635, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163673, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163714, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163717, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163747, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163792, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163795, "dur": 47, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163845, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163849, "dur": 35, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163887, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163889, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163954, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163959, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163996, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374163999, "dur": 38, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164041, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164043, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164086, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164088, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164131, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164133, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164168, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164172, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164213, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164217, "dur": 39, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164260, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164262, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164306, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164309, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164347, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164351, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164398, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164401, "dur": 37, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164441, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164443, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164478, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164480, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164515, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164543, "dur": 71, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164623, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164629, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164685, "dur": 3, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164691, "dur": 39, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164734, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164737, "dur": 44, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164785, "dur": 3, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164790, "dur": 42, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164837, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164839, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164890, "dur": 3, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164895, "dur": 44, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164945, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374164948, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165000, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165003, "dur": 64, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165070, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165074, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165123, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165128, "dur": 51, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165184, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165187, "dur": 41, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165232, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165235, "dur": 49, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165287, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165290, "dur": 56, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165349, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165351, "dur": 53, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165410, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165414, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165462, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165470, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165517, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165520, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165551, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165555, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165625, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165628, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165687, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165692, "dur": 42, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165737, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165740, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165774, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165776, "dur": 44, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165824, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165826, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165854, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165856, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165888, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374165891, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166101, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166105, "dur": 74, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166183, "dur": 4, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166188, "dur": 65, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166266, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166273, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166355, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166358, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166398, "dur": 87, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166491, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166493, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166551, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166555, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166616, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166622, "dur": 50, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166676, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166679, "dur": 47, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166730, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166733, "dur": 38, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166774, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166778, "dur": 40, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166821, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166823, "dur": 44, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166871, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166875, "dur": 45, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166924, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374166926, "dur": 6987, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374173928, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374173933, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374173989, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374173992, "dur": 919, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374174920, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374174925, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374175018, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374175023, "dur": 298, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374175328, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374175332, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374175369, "dur": 6044, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374181436, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374181443, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374181536, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374181539, "dur": 401, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374181945, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374181948, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374181987, "dur": 606, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374182597, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374182599, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374182633, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374182661, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374182663, "dur": 305, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374182972, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374182975, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183033, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183036, "dur": 26, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183065, "dur": 84, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183154, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183185, "dur": 527, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183716, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183718, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183761, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183763, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183802, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183836, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183838, "dur": 35, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183875, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183878, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183913, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183916, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183954, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183956, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374183986, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184012, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184014, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184146, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184151, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184177, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184209, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184268, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184270, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184310, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184403, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184453, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184455, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184499, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184502, "dur": 104, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184610, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184642, "dur": 181, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184827, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184863, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184865, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184912, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374184913, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185039, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185043, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185080, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185120, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185167, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185169, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185207, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185256, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185283, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185334, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185366, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185370, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185397, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185400, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185446, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185472, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185550, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185603, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185605, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185639, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185641, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185692, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185695, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185727, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185785, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185814, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185862, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185895, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185896, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185918, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185991, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374185996, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186038, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186040, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186075, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186077, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186130, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186132, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186167, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186232, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186234, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186271, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186306, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186309, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186347, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186349, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186401, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186404, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186442, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186444, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186477, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186479, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186508, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186509, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186540, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186542, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186572, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186603, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186635, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186638, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186670, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186722, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186770, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186773, "dur": 62, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186838, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186840, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186881, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186883, "dur": 55, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186943, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186977, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374186980, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187012, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187045, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187079, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187081, "dur": 162, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187246, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187249, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187292, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187295, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187333, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187336, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187386, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187456, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187457, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187498, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187500, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187534, "dur": 40, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187577, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187579, "dur": 53, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187635, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187638, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187679, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187777, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187780, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187824, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187827, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374187867, "dur": 274, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188146, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188192, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188194, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188242, "dur": 120, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188365, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188398, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188441, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188443, "dur": 248, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188696, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188698, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188760, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188764, "dur": 54, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188822, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188825, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188877, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188880, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374188920, "dur": 674, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374189600, "dur": 137, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374189749, "dur": 6, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374189758, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374189804, "dur": 258, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190068, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190071, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190142, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190144, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190198, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190201, "dur": 376, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190583, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190627, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190670, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190721, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190724, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190768, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190770, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190861, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190907, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190910, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190950, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190952, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190988, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374190991, "dur": 418, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374191415, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374191417, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374191481, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374191482, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374191564, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374191598, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374191629, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374191690, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374191693, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374191726, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374191846, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374191851, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374191883, "dur": 571, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374192457, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374192489, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374192512, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374192566, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374192599, "dur": 162, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374192765, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374192825, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374192829, "dur": 638, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374193473, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374193510, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374193603, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374193631, "dur": 306, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374193940, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374193967, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374194079, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374194105, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374194160, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374194162, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374194239, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374194241, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374194270, "dur": 820, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374195098, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374195102, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374195171, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374195177, "dur": 66213, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374261397, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374261401, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374261445, "dur": 2426, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374263882, "dur": 10940, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374274834, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374274838, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374274874, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374274876, "dur": 158, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374275044, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374275048, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374275105, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374275108, "dur": 2108, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374277227, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374277231, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374277323, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374277327, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374277368, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374277409, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374277411, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374277455, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374277458, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374277492, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374277494, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374277544, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374277546, "dur": 805, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374278361, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374278365, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374278408, "dur": 1323, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374279746, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374279751, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374279797, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374279799, "dur": 126, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374279928, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374279931, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374280021, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374280025, "dur": 59, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374280116, "dur": 40, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374280162, "dur": 374, "ph": "X", "name": "ProcessMessages 22", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374280541, "dur": 64, "ph": "X", "name": "ReadAsync 22", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374280612, "dur": 366, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374280984, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374280987, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374281018, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374281020, "dur": 1565, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374282595, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374282599, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374282635, "dur": 273, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374282918, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374282923, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374282985, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374282988, "dur": 146, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374283141, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374283184, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374283187, "dur": 350, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374283540, "dur": 597, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374284152, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374284157, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374284252, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374284255, "dur": 941, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374285205, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374285209, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374285251, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374285253, "dur": 123, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374285381, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374285414, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374285471, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374285474, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374285506, "dur": 344, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374285855, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374285859, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374285897, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374285899, "dur": 384, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286287, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286289, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286338, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286341, "dur": 138, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286482, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286483, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286519, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286522, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286668, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286669, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286723, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286726, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286766, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286805, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286807, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286843, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374286845, "dur": 192, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287046, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287049, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287085, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287188, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287190, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287230, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287233, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287267, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287302, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287304, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287360, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287362, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287408, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287411, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287446, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287447, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287473, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287506, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287544, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287546, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287584, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287586, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287625, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287663, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287666, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287700, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287701, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287732, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287736, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287765, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287766, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287799, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287828, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287882, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287884, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287921, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287952, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287955, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374287976, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288006, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288011, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288053, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288055, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288106, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288108, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288144, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288145, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288180, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288183, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288219, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288221, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288263, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288266, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288301, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288305, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288348, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288350, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288410, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288412, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288451, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288453, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288489, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288491, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288524, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288526, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288561, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288563, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288591, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288623, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288625, "dur": 53, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288680, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288682, "dur": 37, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288724, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288726, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288847, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288892, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288894, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374288923, "dur": 326, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374289255, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374289298, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374289300, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374289330, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374289397, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374289432, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374289467, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374289509, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374289512, "dur": 139337, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374428858, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374428862, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374428897, "dur": 22, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374428920, "dur": 18400, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374447329, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374447333, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374447416, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374447420, "dur": 122268, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374569697, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374569701, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374569813, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374569820, "dur": 86, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374569909, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374569914, "dur": 97207, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374667136, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374667142, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374667197, "dur": 29, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374667227, "dur": 24634, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374691871, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374691876, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374691942, "dur": 27, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374691970, "dur": 8089, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374700069, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374700073, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374700114, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374700116, "dur": 5648, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374705773, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374705777, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374705814, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374705815, "dur": 14591, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374720415, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374720419, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374720495, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374720501, "dur": 1951, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374722461, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374722465, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374722531, "dur": 27, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374722559, "dur": 107543, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374830111, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374830116, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374830186, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374830190, "dur": 1143, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374831348, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374831353, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374831431, "dur": 33, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374831466, "dur": 599, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374832076, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374832081, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374832174, "dur": 642, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751311374832825, "dur": 11454, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 19704, "tid": 2513, "ts": 1751311374863065, "dur": 2131, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 19704, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 19704, "tid": 8589934592, "ts": 1751311374122599, "dur": 110558, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 19704, "tid": 8589934592, "ts": 1751311374233160, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 19704, "tid": 8589934592, "ts": 1751311374233165, "dur": 1063, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 19704, "tid": 2513, "ts": 1751311374865198, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 19704, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 19704, "tid": 4294967296, "ts": 1751311374096692, "dur": 749232, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751311374101496, "dur": 10619, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751311374846019, "dur": 5359, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751311374848916, "dur": 37, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751311374851471, "dur": 20, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 19704, "tid": 2513, "ts": 1751311374865209, "dur": 15, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751311374128074, "dur": 66, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311374128167, "dur": 2480, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311374130665, "dur": 923, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311374131725, "dur": 83, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751311374131808, "dur": 493, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311374133241, "dur": 243, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_526B70D8724FDBD0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751311374134380, "dur": 164, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751311374134638, "dur": 2394, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_715C92D4D50EF494.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751311374137475, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751311374138408, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751311374139808, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751311374141237, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751311374141312, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751311374141744, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll_94F5458D7254E99F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751311374145665, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751311374148298, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751311374150331, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751311374152366, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751311374154461, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751311374156719, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751311374158113, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751311374159428, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751311374132332, "dur": 28241, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311374160590, "dur": 670649, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311374831240, "dur": 302, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311374831787, "dur": 87, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311374831910, "dur": 1894, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751311374133143, "dur": 27604, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374160761, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7B82E1658E753B21.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374161411, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_8DE949524442C9D8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374161596, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_8DE949524442C9D8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374161698, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_33AFB99E7761AA5C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374161775, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374161850, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_308026626194FDB8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374161967, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374162042, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BADDD17E000661FB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374162139, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374162205, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_433711FCB6016F00.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374162509, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_EAAAA0867963BCF7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374162572, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374162665, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_EAAAA0867963BCF7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374162724, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_80843626DE95EA13.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374162827, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_1638DBF3D84589E5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374162932, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_E106F825F731F4BC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374163220, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751311374163283, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374163379, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374163458, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751311374163532, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374163718, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374163874, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374163948, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374164077, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374164416, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374164527, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374164763, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374164898, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374164983, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374165058, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374165176, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751311374165259, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374165373, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751311374165529, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374165792, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751311374165907, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751311374166074, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751311374166275, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374166528, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1177933628359149393.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751311374166580, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374166715, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374167298, "dur": 1642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374168941, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374169282, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374169506, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374169749, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374170300, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374170662, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374170915, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374171196, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374171674, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374172023, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374172711, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374172993, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374173244, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374173561, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374173877, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374174656, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374175039, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374175484, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374175756, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374175995, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374176301, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374176762, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374177035, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374177264, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374177592, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374177884, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374178122, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374179293, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374180112, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374180629, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374181279, "dur": 1331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374182613, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374182781, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374182864, "dur": 727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751311374183592, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374183918, "dur": 583, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374184519, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374184750, "dur": 1036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751311374185792, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374185895, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374186324, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374186701, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374186768, "dur": 1297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751311374188067, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374188366, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374188524, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374188590, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751311374189233, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374189311, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374189399, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374189570, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374189636, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751311374190394, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374190555, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751311374190756, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751311374191342, "dur": 80541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374271886, "dur": 2535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751311374274435, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374274553, "dur": 2441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751311374276995, "dur": 1199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374278215, "dur": 2561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751311374280778, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374280878, "dur": 2541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751311374283420, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374283555, "dur": 2374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751311374285930, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751311374286182, "dur": 3060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751311374289324, "dur": 541903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374132841, "dur": 27767, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374160660, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374160912, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_8325F75A1495950D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374161548, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374161620, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_1DC8148833156840.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374161732, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_595F986B2A245834.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374161838, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_0EEB0D491376C433.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374161954, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_7676AB112B74F121.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374162025, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374162163, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3E1DF6609ACAC61F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374162335, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D7CA9FED890DDAB1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374162511, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374162620, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_48483D327EA17808.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374162739, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3EF742C8CA4C5E1E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374162837, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A587944D9110748A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374163101, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374163230, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374163366, "dur": 11326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374174792, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374174868, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374175144, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374175237, "dur": 5959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374181293, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374181417, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374181771, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374181835, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374182411, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374182627, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374182802, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374182900, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374183698, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374183847, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374184103, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374184967, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374185115, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374185180, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374185233, "dur": 236, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_9A20CFD48BC11F43.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374185478, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374185594, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374185693, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374185827, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374185953, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374186172, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374186234, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374186867, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374186950, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374187460, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374187531, "dur": 829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374188371, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374188536, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374188619, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374189284, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374189448, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374189656, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374190419, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374190588, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374190789, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374191421, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374191572, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751311374191752, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374192410, "dur": 79458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374271873, "dur": 2529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374274405, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374274576, "dur": 2606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374277183, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374277307, "dur": 2615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374279982, "dur": 2695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374282679, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374282815, "dur": 5574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751311374288390, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374288548, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374288766, "dur": 410618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751311374699445, "dur": 6098, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751311374699387, "dur": 6159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751311374705599, "dur": 125635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374133026, "dur": 27660, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374160697, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_5F13DCF03A10B823.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374161405, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_596B249A76101BE6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374161590, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_596B249A76101BE6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374161644, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_B83C6071BB394334.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374161775, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_4E1AB9DAA17812DB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374161840, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374161903, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A619838E516A75AC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374162143, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A619838E516A75AC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374162238, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_D8FCB30ADD921A73.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374162474, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_F21194D2F617C9ED.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374162542, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374162643, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_CBABBF352E91654E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374162771, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_1FF1C2755BAA46D4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374163045, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_BFEF6BF3A445E475.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374163268, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374163421, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374163658, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751311374163745, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374163851, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751311374163942, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751311374164071, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374164334, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374164387, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751311374164542, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374164662, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374164758, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751311374164875, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374165029, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374165180, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751311374165460, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374165512, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751311374165637, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751311374165708, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751311374165762, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374165911, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751311374165963, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374166077, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13909235412005675431.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751311374166294, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374166585, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374166771, "dur": 1710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374168481, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374169609, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374169846, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374170624, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374170877, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374171134, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374171816, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374172081, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374172328, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374172982, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374173228, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374173686, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374174234, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374174501, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374174829, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374175063, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374175634, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374175882, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374176124, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374176447, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374176907, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374177196, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374177534, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374177830, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374178081, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374178708, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374179621, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374180222, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374180660, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374181310, "dur": 1334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374182647, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374182811, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374182897, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311374183508, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374183711, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374183839, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374184010, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374184078, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374184307, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311374184985, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374185077, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374185406, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374185509, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311374186214, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374186528, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311374187179, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374187262, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374187436, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374187495, "dur": 873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374188369, "dur": 2203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374190577, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374190721, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374190844, "dur": 1535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311374192381, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374192498, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374192607, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374192666, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311374193288, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374193384, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374193509, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311374194017, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751311374194147, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311374194895, "dur": 54, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374196090, "dur": 232619, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311374435637, "dur": 11354, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751311374435146, "dur": 11920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751311374447067, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374447151, "dur": 118694, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751311374447148, "dur": 120620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751311374569105, "dur": 199, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751311374570505, "dur": 96463, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751311374699382, "dur": 466, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751311374699372, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751311374699891, "dur": 131377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374133109, "dur": 27620, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374160744, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_CC3B7EF1A2C4EA40.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374161224, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374161310, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_467B09015CCE177C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374161423, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374161562, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_15A0A72BE0744047.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374161694, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9843DD54C5E6159E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374161800, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_67CB883191F4D66D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374161900, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_A164BAE5670FC544.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374162013, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_DBB153F07C192DFF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374162118, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_658DA73782B14E20.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374162237, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_658DA73782B14E20.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374162393, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_7DC1C7578DCAB919.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374162568, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374162650, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_5FDF6992734E0361.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374162736, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374162794, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_5FDF6992734E0361.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374162865, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_228E07120E318326.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374163049, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374163410, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374163628, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751311374163706, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374163798, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374163915, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751311374164215, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374164445, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374164540, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374164677, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751311374164798, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374165095, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374165263, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751311374165421, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751311374165825, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374166056, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751311374166211, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374166731, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374167457, "dur": 1801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374169259, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374169502, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374169735, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374170039, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374170843, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374171155, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374171641, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374171993, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374172238, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374173010, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374173265, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374173630, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374173911, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374174183, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374174432, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374174683, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374174912, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374175577, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374175820, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374176055, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374176348, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374176598, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374176858, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374177202, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374177468, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374177728, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374178018, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374178351, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374179543, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374180258, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374180728, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374181296, "dur": 1330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374182628, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374182800, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374182952, "dur": 710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751311374183663, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374183840, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374184091, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374184155, "dur": 1628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751311374185785, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374186171, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374186426, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374186615, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374186729, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751311374187333, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374187428, "dur": 954, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374188383, "dur": 5641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374194026, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751311374194159, "dur": 77750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374271911, "dur": 2494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751311374274407, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374274557, "dur": 2455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751311374277013, "dur": 2809, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374279834, "dur": 2890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751311374282725, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374282819, "dur": 2449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751311374285270, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374285367, "dur": 3170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751311374288538, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751311374288787, "dur": 542449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374133323, "dur": 27595, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374160920, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F697CEAF687CB7A2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311374161590, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_0E07881F85CDD302.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311374161705, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_51B6A25823037467.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311374161836, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374161908, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_EE062F8861099912.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311374162023, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_64D9AC7F4C4AC8A8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311374162094, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374162199, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_FE92BD4D4925733A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311374162307, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_FE92BD4D4925733A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311374162371, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_77F91A8BF54EFB7C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311374162570, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E689A207C3E7A0C2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311374162666, "dur": 261, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E689A207C3E7A0C2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311374162930, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751311374162989, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374163239, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374163395, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374163662, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374163896, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751311374163953, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751311374164067, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374164124, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751311374164268, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751311374164329, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374164843, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751311374164968, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374165063, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374165397, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751311374165454, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374165560, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374165744, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751311374165969, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374166158, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374166507, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751311374166659, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374166749, "dur": 1460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374168210, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374169481, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374169745, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374170087, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374170872, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374171136, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374172081, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374172711, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374173001, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374173262, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374173508, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374173935, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374174192, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374174431, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374174674, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374175159, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374175528, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374175824, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374176076, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374176355, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374176644, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374176920, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374177224, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374177512, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374177759, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374178094, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374179145, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374179982, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374180630, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374181342, "dur": 1306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374182650, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311374182808, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374182906, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751311374183636, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374183830, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311374184138, "dur": 816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751311374184955, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374185026, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311374185353, "dur": 967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751311374186321, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374186398, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374186524, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311374186703, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374186845, "dur": 788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751311374187634, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374187759, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374188371, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751311374188535, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374188676, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751311374189262, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374189381, "dur": 48220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374237602, "dur": 34295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374271901, "dur": 2917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751311374274819, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374274934, "dur": 2403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751311374277393, "dur": 2649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751311374280043, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374280133, "dur": 2532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751311374282667, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374283008, "dur": 492, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751311374283506, "dur": 2590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751311374286097, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751311374286193, "dur": 3087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751311374289350, "dur": 541929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374132909, "dur": 27733, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374160667, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374160848, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_44462716F1E6187F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311374161499, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0333895C18D609C9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311374161721, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_E5318FF293833B30.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311374162002, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D7031BE9F197A0A6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311374162113, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8B3842F7B08DFF89.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311374162221, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B1D6DCAB2D5A18DA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311374162292, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374162423, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_93B77B82222D1B4A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311374162637, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_584BDBB0AACB340C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311374162692, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374162814, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_1FC72A0013DC4EEB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311374162919, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374162986, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311374163115, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374163234, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374163325, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311374163380, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374163718, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311374163820, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374164107, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751311374164300, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374164410, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311374164569, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374164710, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374164867, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374164923, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751311374165189, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311374165340, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374165477, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311374165606, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374165671, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374165727, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311374165914, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751311374166054, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374166413, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374166585, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374166778, "dur": 1763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374168542, "dur": 1664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374170207, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374170586, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374170968, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374171769, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374172063, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374172321, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374172933, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374173457, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374173705, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374173999, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374174248, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374174500, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374175096, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374175599, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374175861, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374176148, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374176462, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374176725, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374177041, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374177490, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374177722, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374178110, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374179133, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374180031, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374180320, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374180866, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374181283, "dur": 1330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374182615, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311374182789, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374182878, "dur": 1714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751311374184593, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374184796, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311374185018, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374185131, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311374185234, "dur": 1971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751311374187206, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374187362, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374187442, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311374187700, "dur": 1155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751311374188857, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374189003, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751311374189184, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374189285, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751311374189873, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374189968, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374190044, "dur": 81830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374271878, "dur": 2527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751311374274407, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374274568, "dur": 2563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751311374277133, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374277277, "dur": 2267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751311374279546, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374280026, "dur": 4462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751311374284490, "dur": 497, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374285016, "dur": 3439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751311374288456, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374288560, "dur": 780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751311374289360, "dur": 541913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374132998, "dur": 27662, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374160671, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_82F8B03BA9650A81.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311374161387, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374161559, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_B566D0CB2E423B86.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311374161678, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DAD9D37CE1A4BA55.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311374161785, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7E47E9336205E31D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311374161847, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374161920, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BA0CB78B239CCACF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311374162020, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8D9A2E8777AB6882.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311374162160, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_209987431F1A3EDC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311374162235, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374162312, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_209987431F1A3EDC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311374162394, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B6153E13308654D1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311374162582, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374162693, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_815CA55F1B3FC2AD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311374162845, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374162936, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_7AC1C063560F2BC6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311374163102, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374163173, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311374163228, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374163411, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374163605, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374163715, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311374163774, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374163924, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374164120, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751311374164197, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374164290, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374164348, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311374164585, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751311374164745, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374164940, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374165005, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374165173, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374165279, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374165367, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311374165541, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751311374165625, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374165737, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311374165789, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374166048, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751311374166185, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374166746, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374167963, "dur": 1580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374169544, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374169788, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374170556, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374170856, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374171095, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374171963, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374172217, "dur": 582, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\ViewModels\\BlackboardCategoryViewModel.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751311374172217, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374173053, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374173297, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374173721, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374174282, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374174576, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374174887, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374175169, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374175574, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374175819, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374176067, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374176566, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374176819, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374177175, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374177464, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374177706, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374178033, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374178535, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374179668, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374179994, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374180358, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374180615, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374181280, "dur": 1340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374182632, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311374182800, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374182860, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751311374183563, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374183787, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374183860, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311374184042, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374184100, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751311374184835, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374185046, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311374185384, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374185475, "dur": 872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751311374186348, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374186664, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751311374186761, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374186899, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374187430, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374187506, "dur": 856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374188364, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751311374188536, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374188597, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751311374189109, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374189200, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374189283, "dur": 44841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374235874, "dur": 292, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1751311374236167, "dur": 1285, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 7, "ts": 1751311374237453, "dur": 123, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 7, "ts": 1751311374234126, "dur": 3459, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374237586, "dur": 34305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374271893, "dur": 2513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751311374274407, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374274535, "dur": 2518, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751311374277061, "dur": 2496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751311374279558, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374279666, "dur": 3126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751311374282793, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374282867, "dur": 4458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751311374287326, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374287586, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374288246, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374288314, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374288558, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374289309, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751311374289372, "dur": 541873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374133094, "dur": 27609, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374160714, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_256223B421F6FD72.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374161328, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_69FC355D1BD4BB29.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374161433, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_857E61E190401437.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374161591, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_857E61E190401437.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374161681, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_76EB92ADF25C401C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374161845, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_5DFD03B93DA29B4D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374161950, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_60AF5DD29DC33809.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374162091, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E04F87D224606974.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374162164, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374162260, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_BB587A5119495C88.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374162465, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DA7D4247AC2DB3F7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374162590, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_C01CA8838C1CE31C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374162688, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A28CE54978CD971D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374162782, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AFE94C31B12D3213.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374162890, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374163002, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374163160, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7F3203F2669A3DC6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374163323, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374163378, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751311374163490, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_3D651E01942E03F8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374163674, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374163789, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374163859, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374163957, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751311374164125, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374164196, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374164486, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374164728, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751311374164800, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751311374164941, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374165514, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374165576, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751311374165741, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751311374165874, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374166064, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751311374166252, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374166538, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751311374166590, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374166715, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374166790, "dur": 1448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374168239, "dur": 1487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374169727, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374169984, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374170701, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374170985, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374171659, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374171986, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374172923, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374173207, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374173477, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374173757, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374174081, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374174338, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374174614, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374174950, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374175225, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374175510, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374175793, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374176057, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374176340, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374176574, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374176811, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374177281, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374177624, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374178089, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374178939, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374179756, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374180120, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374180495, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374180629, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374181365, "dur": 1270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374182639, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374182801, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374182923, "dur": 688, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374183622, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751311374184452, "dur": 688, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374185141, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751311374185269, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374185359, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374185687, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374185769, "dur": 1044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751311374186814, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374187147, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374187215, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374187449, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374187515, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751311374188089, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374188376, "dur": 2274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374190653, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751311374190798, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374190865, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751311374191342, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374191534, "dur": 82916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374274454, "dur": 2551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751311374277006, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374277166, "dur": 2394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751311374279561, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374279830, "dur": 3003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751311374282834, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374282896, "dur": 596, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751311374283503, "dur": 2810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751311374286315, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374286384, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374286691, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374287742, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374288032, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374288137, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374288222, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374288390, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374288518, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374288585, "dur": 146569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374435186, "dur": 130709, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751311374435157, "dur": 132607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751311374569488, "dur": 195, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751311374570501, "dur": 121204, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751311374719850, "dur": 110024, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751311374719841, "dur": 110035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751311374829927, "dur": 1253, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751311374133236, "dur": 27560, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374160810, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_A1485D01311C863E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374161393, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_1182EE28E68C62C8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374161460, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374161598, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_1182EE28E68C62C8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374161763, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_9D64899DE49D3B06.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374161834, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374161978, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_BFD5601BA05C7CD8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374162120, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374162195, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_472976495F1230C7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374162309, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374162444, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7B8E45163E4907D2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374162608, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_0B2798972D9D68EB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374162702, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FEA05FC664382286.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374162835, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_486DF76931BD0557.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374162900, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374162972, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_715C92D4D50EF494.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374163043, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374163175, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751311374163303, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374163542, "dur": 10123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751311374173822, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374174332, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374174627, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374175018, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374175474, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374175763, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374176026, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374176405, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374176666, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374176952, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374177223, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374177622, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374177907, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374178146, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374179265, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374180206, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374180697, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374181295, "dur": 1335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374182643, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374182805, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374182955, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751311374183556, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374183718, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374183784, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374184347, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751311374185130, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374185238, "dur": 839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751311374186078, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374186329, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374186562, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751311374186682, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751311374187309, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374187384, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1751311374188056, "dur": 127, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374188783, "dur": 72476, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1751311374271885, "dur": 2526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751311374274412, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374274581, "dur": 2409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751311374276992, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374277140, "dur": 2342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751311374279485, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374279612, "dur": 2741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751311374282363, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374282462, "dur": 2509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751311374284974, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374285072, "dur": 3196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751311374288269, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374288555, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751311374289207, "dur": 542043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374133205, "dur": 27561, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374160779, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_8A89D5AFAE49E63E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374161374, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_E681FA607883332E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374161515, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_D32EF602AAA54BE6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374161635, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374161702, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_8EA224AC6679B7C2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374161791, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C8A867A03915DFB5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374161844, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374161917, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0FEAE2B241ACD344.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374161988, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374162093, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_DB48149D0776AF57.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374162209, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1FE2264601546D0E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374162278, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374162433, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_D4344483B74B03A7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374162630, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374162706, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_7E691CD5ECB673D9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374162806, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_20A6B658F2F96E47.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374162961, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_0D77228FB13B7FB6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374163155, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_07FA64190ECA4CC1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374163258, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374163628, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751311374163706, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374164046, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374164197, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374164356, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751311374164421, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374164508, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751311374164658, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374164797, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374164864, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374164989, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374165063, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374165129, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751311374165193, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374165275, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374165457, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374165574, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751311374165693, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374166085, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751311374166305, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374166723, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374167193, "dur": 1506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374168700, "dur": 1417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374170118, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374170664, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374170953, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374171427, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374171855, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374172122, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374172833, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374173104, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374173332, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374173644, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374174042, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374174289, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374174565, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374175367, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374175931, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374176177, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374176497, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374176756, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374177080, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374177354, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374177982, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374178309, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374179348, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374180180, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374180666, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374181294, "dur": 1539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374182835, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374183057, "dur": 1561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751311374184619, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374184730, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374184943, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751311374185718, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374185956, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374186034, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374186274, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374186438, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374186751, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751311374187352, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374187449, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374187684, "dur": 1471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751311374189157, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374189342, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374189507, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374189583, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751311374190478, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374190635, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374190797, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751311374191377, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374191518, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751311374191749, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751311374192315, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374192457, "dur": 79421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374271898, "dur": 2507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751311374274407, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374274543, "dur": 2455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751311374277000, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374277111, "dur": 2443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751311374279555, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374280156, "dur": 2479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751311374282637, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374282779, "dur": 2366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751311374285147, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751311374285285, "dur": 3394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751311374288793, "dur": 542436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374133296, "dur": 27521, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374160832, "dur": 647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_0E82E95F1D4B1B8B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374161480, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374161623, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F27EBD72F4881481.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374161760, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_C047F56604413718.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374161827, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374161924, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_91BF37D1F055EC15.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374162091, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374162156, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F0EAC96F0603CD08.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374162213, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374162292, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_8FE2697BF98A2193.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374162495, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_9870EDF59A107BDF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374162615, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_D1635D1E3A6F959D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374162734, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_922751811D286E63.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374162854, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_12124AFB436F9D5D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374162915, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374163097, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_0854A01FF08BC637.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374163398, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374163508, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374163558, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374163679, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751311374163765, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374163893, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374164125, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751311374164221, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374164300, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751311374164376, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751311374164442, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374164571, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374164674, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751311374164741, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374164920, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751311374165169, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374165251, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374165467, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374165706, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751311374166042, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751311374166168, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374166567, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751311374166671, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374166785, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374168363, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374170192, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374170622, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374170895, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374171175, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374171546, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374172008, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374172256, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374172895, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374173139, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374173400, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374173643, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374173939, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374174181, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374174434, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374174705, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374175092, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374175378, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374175695, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374175963, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374176188, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374176487, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374176750, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374177121, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374177410, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374177650, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374177963, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374178224, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374179359, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374180079, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374180367, "dur": 70, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374180630, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374181278, "dur": 1338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374182626, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374182804, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374182948, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751311374183576, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374183915, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374184096, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374184174, "dur": 814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751311374184989, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374185131, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751311374185197, "dur": 856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751311374186054, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374186252, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374186372, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374186570, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374186627, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751311374187378, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374187511, "dur": 870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374188382, "dur": 5008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374193392, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751311374193530, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751311374193848, "dur": 78046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374271895, "dur": 2510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751311374274407, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374274573, "dur": 2558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751311374277163, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374277329, "dur": 2894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751311374280224, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374280314, "dur": 2499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751311374282863, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751311374283058, "dur": 2598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751311374285658, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751311374285754, "dur": 3339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751311374289166, "dur": 542066, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374133368, "dur": 27486, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374160862, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FECC8B8B813FB02A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374161425, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374161547, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_14A5559FFED55C50.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374161650, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_14A5559FFED55C50.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374161821, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_526B70D8724FDBD0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374161961, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0999ABDFA115F312.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374162108, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_BEB6379C07D3D185.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374162237, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374162355, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_1A2A3B79BD3B4CFD.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374162552, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374162613, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_C1BEC5DDD68DA9AF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374162685, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374162763, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1948650C4D21EE20.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374162900, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374162982, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_570D95476513426D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374163062, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374163262, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374163345, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374163466, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374163600, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374163703, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374163800, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374163852, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751311374163931, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374164146, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374164388, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751311374164519, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751311374164617, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374164727, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374164873, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374164937, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751311374165091, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374165228, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374165450, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374165528, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374165675, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751311374165808, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751311374165883, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374165984, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374166405, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374166718, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374167150, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374168666, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374169994, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374171136, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374171909, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374172200, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374172818, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374173142, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374173479, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374173799, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374174078, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374174350, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374174832, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374175085, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374175516, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Utilities\\Clipboard.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751311374175408, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374176288, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374176541, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374176790, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374177179, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374177548, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374177831, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374178121, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374178945, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374179866, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374180232, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374180689, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374181312, "dur": 1329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374182643, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374182807, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374182882, "dur": 656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751311374183539, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374183623, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374183708, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374184068, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374184275, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374184354, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751311374185163, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374185281, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374185439, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374185512, "dur": 1062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751311374186575, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374186753, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374186946, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374187118, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374187227, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751311374187850, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374188093, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374188370, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751311374188514, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374188585, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751311374189284, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374189404, "dur": 82483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374271890, "dur": 2605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751311374274497, "dur": 404, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374274915, "dur": 2388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751311374277304, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374277398, "dur": 2341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751311374279740, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374279821, "dur": 3530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751311374283353, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374283443, "dur": 3039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751311374286483, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374286731, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374286933, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374287355, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374287551, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374287960, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751311374288135, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374288243, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374288546, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374288785, "dur": 431077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751311374719925, "dur": 286, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751311374719865, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751311374720255, "dur": 2047, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751311374722309, "dur": 108958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751311374841067, "dur": 2544, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 19704, "tid": 2513, "ts": 1751311374865942, "dur": 8601, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 19704, "tid": 2513, "ts": 1751311374874594, "dur": 2831, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 19704, "tid": 2513, "ts": 1751311374859783, "dur": 18925, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}