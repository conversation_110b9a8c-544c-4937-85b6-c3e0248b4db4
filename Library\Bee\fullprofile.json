{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 19704, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 19704, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 19704, "tid": 1767, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 19704, "tid": 1767, "ts": 1751309167203724, "dur": 854, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 19704, "tid": 1767, "ts": 1751309167209558, "dur": 965, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 19704, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 19704, "tid": 1, "ts": 1751309166476875, "dur": 7126, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19704, "tid": 1, "ts": 1751309166484006, "dur": 53113, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19704, "tid": 1, "ts": 1751309166537134, "dur": 45293, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 19704, "tid": 1767, "ts": 1751309167210528, "dur": 14, "ph": "X", "name": "", "args": {}}, {"pid": 19704, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166474288, "dur": 8551, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166482843, "dur": 709823, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166483985, "dur": 2670, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166486664, "dur": 1678, "ph": "X", "name": "ProcessMessages 16929", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166488349, "dur": 372, "ph": "X", "name": "ReadAsync 16929", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166488724, "dur": 12, "ph": "X", "name": "ProcessMessages 20569", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166488738, "dur": 73, "ph": "X", "name": "ReadAsync 20569", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166488812, "dur": 1, "ph": "X", "name": "ProcessMessages 1819", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166488814, "dur": 26, "ph": "X", "name": "ReadAsync 1819", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166488843, "dur": 26, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166488871, "dur": 61, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166488934, "dur": 54, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166488990, "dur": 60, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489052, "dur": 1, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489053, "dur": 34, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489090, "dur": 54, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489147, "dur": 31, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489181, "dur": 89, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489272, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489309, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489310, "dur": 28, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489342, "dur": 61, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489404, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489406, "dur": 31, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489442, "dur": 57, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489502, "dur": 87, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489590, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489592, "dur": 33, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489627, "dur": 31, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489660, "dur": 30, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489693, "dur": 62, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489758, "dur": 37, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489798, "dur": 30, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489830, "dur": 43, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489876, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489877, "dur": 111, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489992, "dur": 1, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166489994, "dur": 72, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490070, "dur": 1, "ph": "X", "name": "ProcessMessages 960", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490073, "dur": 51, "ph": "X", "name": "ReadAsync 960", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490126, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490128, "dur": 60, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490191, "dur": 3, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490196, "dur": 35, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490234, "dur": 47, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490283, "dur": 1, "ph": "X", "name": "ProcessMessages 872", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490285, "dur": 40, "ph": "X", "name": "ReadAsync 872", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490327, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490328, "dur": 68, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490400, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490402, "dur": 82, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490488, "dur": 2, "ph": "X", "name": "ProcessMessages 1330", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490491, "dur": 40, "ph": "X", "name": "ReadAsync 1330", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490534, "dur": 40, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490578, "dur": 34, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490616, "dur": 73, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490691, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490693, "dur": 99, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490794, "dur": 1, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490798, "dur": 40, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490840, "dur": 35, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490878, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490880, "dur": 42, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490925, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490927, "dur": 56, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166490986, "dur": 43, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491031, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491033, "dur": 45, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491080, "dur": 1, "ph": "X", "name": "ProcessMessages 1042", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491081, "dur": 62, "ph": "X", "name": "ReadAsync 1042", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491145, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491147, "dur": 36, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491187, "dur": 41, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491232, "dur": 65, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491300, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491301, "dur": 36, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491340, "dur": 38, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491380, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491382, "dur": 35, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491419, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491420, "dur": 57, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491480, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491483, "dur": 37, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491521, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491522, "dur": 39, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491565, "dur": 53, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491620, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491621, "dur": 45, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491670, "dur": 30, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491702, "dur": 36, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491744, "dur": 32, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491777, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491778, "dur": 66, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491846, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491849, "dur": 44, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491895, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491897, "dur": 63, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491962, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166491964, "dur": 49, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492014, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492016, "dur": 39, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492058, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492060, "dur": 140, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492203, "dur": 34, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492240, "dur": 62, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492305, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492308, "dur": 60, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492371, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492374, "dur": 54, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492430, "dur": 1, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492433, "dur": 51, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492486, "dur": 1, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492488, "dur": 32, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492524, "dur": 60, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492589, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492634, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492636, "dur": 39, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492676, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492677, "dur": 31, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492711, "dur": 47, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492759, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492761, "dur": 163, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492928, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492983, "dur": 1, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166492986, "dur": 35, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493022, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493024, "dur": 51, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493078, "dur": 30, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493111, "dur": 29, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493144, "dur": 34, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493180, "dur": 30, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493212, "dur": 30, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493245, "dur": 27, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493274, "dur": 28, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493304, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493339, "dur": 36, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493378, "dur": 37, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493418, "dur": 201, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493624, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493627, "dur": 316, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493946, "dur": 3, "ph": "X", "name": "ProcessMessages 2006", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166493950, "dur": 65, "ph": "X", "name": "ReadAsync 2006", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494022, "dur": 3, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494027, "dur": 64, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494094, "dur": 1, "ph": "X", "name": "ProcessMessages 1082", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494097, "dur": 82, "ph": "X", "name": "ReadAsync 1082", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494182, "dur": 1, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494184, "dur": 33, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494220, "dur": 36, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494259, "dur": 47, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494309, "dur": 32, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494343, "dur": 28, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494374, "dur": 34, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494411, "dur": 33, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494447, "dur": 35, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494483, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494485, "dur": 23, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494511, "dur": 37, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494551, "dur": 39, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494592, "dur": 42, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494635, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494637, "dur": 43, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494683, "dur": 32, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494718, "dur": 26, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494746, "dur": 48, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494797, "dur": 47, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494846, "dur": 29, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494878, "dur": 39, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494920, "dur": 32, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494955, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166494993, "dur": 49, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495045, "dur": 38, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495086, "dur": 48, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495137, "dur": 53, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495192, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495196, "dur": 51, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495249, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495251, "dur": 96, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495349, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495351, "dur": 33, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495389, "dur": 51, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495443, "dur": 44, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495489, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495492, "dur": 43, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495537, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495538, "dur": 39, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495579, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495581, "dur": 32, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495617, "dur": 43, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495662, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495664, "dur": 42, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495708, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495710, "dur": 38, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495751, "dur": 40, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495793, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495795, "dur": 32, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495831, "dur": 42, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495875, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495877, "dur": 38, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495917, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495920, "dur": 39, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495961, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166495963, "dur": 35, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496000, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496002, "dur": 47, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496051, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496052, "dur": 65, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496119, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496121, "dur": 47, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496171, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496173, "dur": 34, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496209, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496211, "dur": 41, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496254, "dur": 28, "ph": "X", "name": "ReadAsync 1019", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496285, "dur": 35, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496325, "dur": 34, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496360, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496362, "dur": 28, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496392, "dur": 50, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496445, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496447, "dur": 31, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496481, "dur": 38, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496522, "dur": 27, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496552, "dur": 63, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496617, "dur": 35, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496655, "dur": 32, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496689, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496690, "dur": 47, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496741, "dur": 30, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496775, "dur": 29, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496806, "dur": 34, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496842, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496844, "dur": 33, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496879, "dur": 35, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496917, "dur": 34, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496953, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496954, "dur": 35, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166496992, "dur": 36, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497031, "dur": 34, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497068, "dur": 44, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497114, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497116, "dur": 39, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497159, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497192, "dur": 34, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497228, "dur": 46, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497276, "dur": 1, "ph": "X", "name": "ProcessMessages 975", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497277, "dur": 24, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497304, "dur": 27, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497334, "dur": 37, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497374, "dur": 43, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497419, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497420, "dur": 33, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497456, "dur": 32, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497491, "dur": 28, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497521, "dur": 30, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497554, "dur": 29, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497590, "dur": 38, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497630, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497632, "dur": 31, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497666, "dur": 42, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497710, "dur": 23, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497736, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497789, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497790, "dur": 32, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497825, "dur": 61, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497888, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497890, "dur": 48, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497941, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497958, "dur": 33, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497994, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166497996, "dur": 49, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498047, "dur": 71, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498123, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498126, "dur": 33, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498161, "dur": 41, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498205, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498207, "dur": 45, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498254, "dur": 1, "ph": "X", "name": "ProcessMessages 835", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498256, "dur": 31, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498290, "dur": 41, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498334, "dur": 44, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498380, "dur": 2, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498382, "dur": 35, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498420, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498421, "dur": 36, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498460, "dur": 57, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498519, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498520, "dur": 35, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498557, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498559, "dur": 40, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498600, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498602, "dur": 33, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498637, "dur": 55, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498695, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498759, "dur": 1, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498761, "dur": 44, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498807, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498808, "dur": 29, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498839, "dur": 34, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498877, "dur": 49, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498927, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498929, "dur": 33, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166498965, "dur": 75, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499041, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499043, "dur": 32, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499077, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499166, "dur": 1, "ph": "X", "name": "ProcessMessages 1196", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499168, "dur": 43, "ph": "X", "name": "ReadAsync 1196", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499213, "dur": 1, "ph": "X", "name": "ProcessMessages 1175", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499215, "dur": 29, "ph": "X", "name": "ReadAsync 1175", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499246, "dur": 38, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499287, "dur": 32, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499322, "dur": 35, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499359, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499361, "dur": 32, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499394, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499395, "dur": 34, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499433, "dur": 24, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499460, "dur": 48, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499510, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499511, "dur": 39, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499552, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499553, "dur": 34, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499591, "dur": 30, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499623, "dur": 28, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499654, "dur": 31, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499688, "dur": 47, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499737, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499739, "dur": 31, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499772, "dur": 41, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499816, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499817, "dur": 29, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499848, "dur": 32, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499882, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499883, "dur": 32, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499917, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499919, "dur": 33, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499955, "dur": 32, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499989, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166499991, "dur": 58, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500051, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500053, "dur": 32, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500087, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500088, "dur": 29, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500121, "dur": 78, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500202, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500203, "dur": 47, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500253, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500255, "dur": 43, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500300, "dur": 1, "ph": "X", "name": "ProcessMessages 1033", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500302, "dur": 43, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500347, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500348, "dur": 52, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500402, "dur": 2, "ph": "X", "name": "ProcessMessages 1003", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500405, "dur": 36, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500444, "dur": 34, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500480, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500482, "dur": 30, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500515, "dur": 32, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500550, "dur": 47, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500599, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500600, "dur": 34, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500637, "dur": 34, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500673, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500674, "dur": 47, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500723, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500724, "dur": 38, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500764, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500766, "dur": 36, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500804, "dur": 37, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500843, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500845, "dur": 34, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500882, "dur": 28, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500913, "dur": 32, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500947, "dur": 32, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166500982, "dur": 34, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501019, "dur": 74, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501095, "dur": 1, "ph": "X", "name": "ProcessMessages 954", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501096, "dur": 35, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501134, "dur": 33, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501169, "dur": 25, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501197, "dur": 29, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501229, "dur": 31, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501262, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501264, "dur": 35, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501302, "dur": 29, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501333, "dur": 48, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501383, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501384, "dur": 28, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501415, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501447, "dur": 40, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501489, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501557, "dur": 34, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501594, "dur": 26, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501623, "dur": 94, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501721, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501776, "dur": 1, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501778, "dur": 31, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501812, "dur": 59, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501874, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501922, "dur": 30, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501955, "dur": 40, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501997, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166501998, "dur": 79, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502081, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502116, "dur": 46, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502164, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502166, "dur": 70, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502239, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502282, "dur": 32, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502317, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502318, "dur": 72, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502392, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502428, "dur": 32, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502463, "dur": 27, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502493, "dur": 70, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502567, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502603, "dur": 29, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502635, "dur": 27, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502664, "dur": 69, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502737, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502773, "dur": 31, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502806, "dur": 27, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502837, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502899, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502935, "dur": 32, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502970, "dur": 27, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166502999, "dur": 69, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503070, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503117, "dur": 36, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503156, "dur": 26, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503185, "dur": 78, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503265, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503298, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503300, "dur": 33, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503336, "dur": 25, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503364, "dur": 70, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503436, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503490, "dur": 45, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503537, "dur": 29, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503569, "dur": 72, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503644, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503680, "dur": 41, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503724, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503726, "dur": 69, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503796, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503798, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503842, "dur": 31, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503876, "dur": 71, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503950, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166503985, "dur": 36, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504024, "dur": 26, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504053, "dur": 72, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504128, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504163, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504165, "dur": 32, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504200, "dur": 26, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504228, "dur": 66, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504296, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504332, "dur": 29, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504364, "dur": 28, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504395, "dur": 63, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504462, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504497, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504500, "dur": 41, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504543, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504545, "dur": 71, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504619, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504663, "dur": 85, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504753, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504755, "dur": 52, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504813, "dur": 68, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504883, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504885, "dur": 36, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504923, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504924, "dur": 69, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504995, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166504997, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505037, "dur": 54, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505093, "dur": 2, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505095, "dur": 52, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505151, "dur": 35, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505189, "dur": 28, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505220, "dur": 28, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505252, "dur": 43, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505298, "dur": 80, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505382, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505419, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505421, "dur": 45, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505470, "dur": 75, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505550, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505587, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505589, "dur": 51, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505643, "dur": 35, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505681, "dur": 41, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505726, "dur": 46, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505776, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505778, "dur": 36, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505819, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505889, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505933, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505934, "dur": 34, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166505972, "dur": 75, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506049, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506087, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506089, "dur": 38, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506130, "dur": 34, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506166, "dur": 41, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506210, "dur": 41, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506255, "dur": 33, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506290, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506291, "dur": 93, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506387, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506424, "dur": 34, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506461, "dur": 70, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506534, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506570, "dur": 31, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506604, "dur": 28, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506635, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506697, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506699, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506761, "dur": 47, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506810, "dur": 1, "ph": "X", "name": "ProcessMessages 988", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506812, "dur": 37, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506850, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506853, "dur": 29, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506888, "dur": 42, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506933, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506970, "dur": 25, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166506997, "dur": 95, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507095, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507134, "dur": 33, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507171, "dur": 68, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507242, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507285, "dur": 45, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507332, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507334, "dur": 74, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507410, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507447, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507449, "dur": 38, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507490, "dur": 25, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507518, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507582, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507618, "dur": 46, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507666, "dur": 29, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507697, "dur": 79, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507779, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507819, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507820, "dur": 55, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507877, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507879, "dur": 27, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507913, "dur": 63, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166507981, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508015, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508017, "dur": 31, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508053, "dur": 28, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508084, "dur": 64, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508151, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508183, "dur": 40, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508225, "dur": 27, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508254, "dur": 78, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508335, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508423, "dur": 1, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508425, "dur": 28, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508454, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508457, "dur": 78, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508538, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508571, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508573, "dur": 31, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508609, "dur": 26, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508637, "dur": 71, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166508711, "dur": 286, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509003, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509007, "dur": 84, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509094, "dur": 2, "ph": "X", "name": "ProcessMessages 1818", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509098, "dur": 58, "ph": "X", "name": "ReadAsync 1818", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509163, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509236, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509240, "dur": 52, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509294, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509297, "dur": 182, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509488, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509494, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509556, "dur": 2, "ph": "X", "name": "ProcessMessages 1216", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509560, "dur": 56, "ph": "X", "name": "ReadAsync 1216", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509619, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509621, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509675, "dur": 1, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509677, "dur": 33, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509712, "dur": 114, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509831, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509867, "dur": 28, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509897, "dur": 83, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166509983, "dur": 30, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510015, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510059, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510061, "dur": 49, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510112, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510113, "dur": 73, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510189, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510243, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510244, "dur": 32, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510282, "dur": 75, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510360, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510415, "dur": 1, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510417, "dur": 29, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510449, "dur": 69, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510521, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510559, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510561, "dur": 34, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510599, "dur": 28, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510631, "dur": 56, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510691, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510729, "dur": 35, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510769, "dur": 44, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510815, "dur": 81, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510899, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510936, "dur": 33, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510970, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166510972, "dur": 65, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511040, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511074, "dur": 2, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511077, "dur": 33, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511113, "dur": 72, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511188, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511222, "dur": 36, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511260, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511261, "dur": 27, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511291, "dur": 74, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511368, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511403, "dur": 32, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511438, "dur": 69, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511509, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511511, "dur": 59, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511573, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511576, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511622, "dur": 1, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511625, "dur": 33, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511659, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511661, "dur": 68, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511731, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511778, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511779, "dur": 50, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511833, "dur": 97, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511935, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166511968, "dur": 38, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512008, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512010, "dur": 33, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512047, "dur": 73, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512124, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512163, "dur": 30, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512196, "dur": 43, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512241, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512243, "dur": 49, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512293, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512296, "dur": 27, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512326, "dur": 32, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512362, "dur": 80, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512446, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512489, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512491, "dur": 32, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512526, "dur": 34, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512563, "dur": 64, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512629, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512667, "dur": 97, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512768, "dur": 54, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512825, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512827, "dur": 47, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512875, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512877, "dur": 31, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166512911, "dur": 104, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513020, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513071, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513072, "dur": 83, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513158, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513159, "dur": 32, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513194, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513234, "dur": 2, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513237, "dur": 39, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513278, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513281, "dur": 28, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513314, "dur": 82, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513399, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513435, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513437, "dur": 203, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513645, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166513725, "dur": 682, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166514417, "dur": 170, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166514595, "dur": 8, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166514605, "dur": 76, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166514685, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166514690, "dur": 49, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166514743, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166514746, "dur": 45, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166514793, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166514796, "dur": 160, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166514962, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166514964, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515028, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515031, "dur": 50, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515084, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515089, "dur": 46, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515138, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515143, "dur": 45, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515190, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515192, "dur": 46, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515243, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515245, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515293, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515297, "dur": 66, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515366, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515369, "dur": 47, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515421, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515423, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515478, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515481, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515527, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515530, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515584, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515587, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515633, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515635, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515681, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515684, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515725, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515727, "dur": 48, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515777, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515780, "dur": 55, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515840, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515842, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515895, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515897, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515953, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166515955, "dur": 50, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516007, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516010, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516057, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516060, "dur": 41, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516104, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516106, "dur": 36, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516145, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516148, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516184, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516219, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516221, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516266, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516270, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516326, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516328, "dur": 52, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516382, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516384, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516434, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516437, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516492, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516494, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516545, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516548, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516600, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516603, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516652, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516654, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516703, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516705, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516747, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516749, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516792, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516794, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516837, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516839, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516888, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516890, "dur": 51, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516945, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166516948, "dur": 63, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517014, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517020, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517070, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517073, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517123, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517125, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517172, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517174, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517229, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517232, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517276, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517279, "dur": 38, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517320, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517323, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517366, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517369, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517427, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517430, "dur": 38, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517472, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517474, "dur": 41, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517519, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517524, "dur": 48, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517574, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517577, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517628, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517631, "dur": 44, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517677, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517680, "dur": 43, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517725, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517729, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517773, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517778, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517825, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517830, "dur": 44, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517876, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517879, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517923, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517926, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517975, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166517978, "dur": 47, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518028, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518032, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518079, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518086, "dur": 50, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518139, "dur": 3, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518143, "dur": 42, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518188, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518190, "dur": 37, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518230, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518232, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518279, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518283, "dur": 58, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518343, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518347, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518390, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518393, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518440, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518443, "dur": 49, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518494, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518497, "dur": 44, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518544, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518548, "dur": 41, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518592, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518594, "dur": 43, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518640, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518644, "dur": 43, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518690, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518693, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518738, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518741, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518781, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518785, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518842, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518844, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518894, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518896, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518945, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518948, "dur": 41, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518992, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166518993, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519046, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519048, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519099, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519101, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519151, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519157, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519209, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519211, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519261, "dur": 141, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519407, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519466, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519469, "dur": 46, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519517, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519519, "dur": 79, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519601, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519604, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519650, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519652, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519691, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519748, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166519750, "dur": 8068, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166527834, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166527841, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166527914, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166527916, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166527945, "dur": 445, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166528401, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166528407, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166528476, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166528480, "dur": 65, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166528550, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166528591, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166528594, "dur": 382, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166528980, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166528983, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166529028, "dur": 6217, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166535252, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166535257, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166535296, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166535298, "dur": 134, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166535437, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166535474, "dur": 371, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166535849, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166535894, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166535896, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166535963, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166536010, "dur": 209, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166536221, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166536225, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166536267, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166536270, "dur": 53, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166536327, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166536367, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166536369, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166536402, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166536404, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166536427, "dur": 276, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166536708, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166536740, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166536742, "dur": 291, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537038, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537075, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537077, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537132, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537170, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537172, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537206, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537303, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537305, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537345, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537347, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537382, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537384, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537421, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537494, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537525, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537567, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537599, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537601, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537646, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537680, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537710, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537745, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537773, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537809, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537859, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537862, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537907, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537909, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537939, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537972, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166537974, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538009, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538011, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538057, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538090, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538092, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538134, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538166, "dur": 187, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538356, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538388, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538390, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538444, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538482, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538483, "dur": 165, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538652, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538682, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538710, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538829, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538863, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538864, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538900, "dur": 49, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538954, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538956, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538995, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166538997, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539043, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539074, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539212, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539245, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539275, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539306, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539336, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539444, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539481, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539483, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539514, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539544, "dur": 182, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539730, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539789, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539823, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539886, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166539917, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540074, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540102, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540224, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540260, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540291, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540293, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540383, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540414, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540488, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540536, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540538, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540580, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540582, "dur": 263, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540850, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540852, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540898, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540900, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540942, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166540945, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541000, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541002, "dur": 61, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541067, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541100, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541102, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541137, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541138, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541171, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541173, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541222, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541261, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541308, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541345, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541348, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541384, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541386, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541418, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541420, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541503, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541537, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541539, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541570, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541603, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541605, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541638, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541671, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541731, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541761, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541763, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541904, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541939, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541941, "dur": 35, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166541979, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166542023, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166542025, "dur": 312, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166542342, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166542344, "dur": 135, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166542483, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166542529, "dur": 810, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166543350, "dur": 111, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166543466, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166543469, "dur": 78, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166543550, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166543552, "dur": 399, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166543958, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166543961, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544024, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544027, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544075, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544233, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544271, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544323, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544362, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544450, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544454, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544495, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544497, "dur": 115, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544615, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544617, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544660, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544661, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544696, "dur": 186, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544887, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544924, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544926, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166544970, "dur": 228, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166545203, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166545259, "dur": 108, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166545371, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166545418, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166545420, "dur": 190, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166545612, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166545614, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166545655, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166545657, "dur": 580, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546242, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546245, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546306, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546307, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546353, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546355, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546395, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546397, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546438, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546547, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546585, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546624, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546670, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546700, "dur": 195, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546898, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546936, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166546975, "dur": 984, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166547963, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166548005, "dur": 176, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166548184, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166548225, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166548256, "dur": 330, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166548589, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166548620, "dur": 448, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166549072, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166549074, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166549117, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166549121, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166549168, "dur": 184, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166549356, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166549390, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166549442, "dur": 1003, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166550453, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166550458, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166550497, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166550501, "dur": 72447, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166622960, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166622965, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166623036, "dur": 2633, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166625682, "dur": 9855, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166635554, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166635560, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166635634, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166635638, "dur": 47, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166635688, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166635690, "dur": 2410, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166638113, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166638118, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166638169, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166638172, "dur": 404, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166638584, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166638588, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166638664, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166638669, "dur": 1024, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166639705, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166639710, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166639746, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166639748, "dur": 810, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166640572, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166640577, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166640634, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166640636, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166640685, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166640687, "dur": 448, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166641145, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166641149, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166641226, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166641230, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166641474, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166641477, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166641513, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166641517, "dur": 241, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166641765, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166641768, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166641823, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166641825, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166641940, "dur": 48, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166641991, "dur": 363, "ph": "X", "name": "ProcessMessages 6", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166642361, "dur": 502, "ph": "X", "name": "ReadAsync 6", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166642878, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166642883, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166642959, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166642962, "dur": 212, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166643179, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166643182, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166643236, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166643239, "dur": 216, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166643462, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166643464, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166643525, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166643527, "dur": 683, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166644226, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166644232, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166644292, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166644295, "dur": 328, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166644630, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166644635, "dur": 178, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166644823, "dur": 8, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166644833, "dur": 552, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166645397, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166645402, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166645446, "dur": 165, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166645616, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166645618, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166645665, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166645667, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166645730, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166645767, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166645769, "dur": 241, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166646013, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166646016, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166646059, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166646060, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166646266, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166646300, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166646301, "dur": 464, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166646773, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166646776, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166646820, "dur": 359, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166647182, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166647185, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166647231, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166647233, "dur": 513, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166647752, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166647757, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166647790, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166647792, "dur": 320, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166648117, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166648121, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166648164, "dur": 211, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166648379, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166648381, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166648428, "dur": 288, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166648719, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166648721, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166648761, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166648763, "dur": 265, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166649032, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166649068, "dur": 416, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166649488, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166649490, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166649537, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166649539, "dur": 134, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166649676, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166649678, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166649719, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166649721, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166649757, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166649834, "dur": 104, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166649942, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166649945, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650000, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650003, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650052, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650054, "dur": 46, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650104, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650106, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650143, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650145, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650196, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650198, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650239, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650241, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650297, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650397, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650429, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650460, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650462, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650517, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650519, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650585, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650588, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650641, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650644, "dur": 58, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650704, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650706, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650751, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650753, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650790, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650792, "dur": 39, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650832, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650834, "dur": 54, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650894, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650896, "dur": 43, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650942, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650944, "dur": 47, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166650995, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651032, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651034, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651070, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651073, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651108, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651110, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651154, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651156, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651204, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651206, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651248, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651251, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651297, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651299, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651340, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651348, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651406, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651408, "dur": 38, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651449, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651451, "dur": 47, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651501, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651503, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651552, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651554, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651602, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651604, "dur": 64, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651672, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651674, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651715, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651717, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651776, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651778, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651830, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651832, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651882, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651885, "dur": 40, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651927, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166651930, "dur": 61, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166652003, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166652010, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166652057, "dur": 155, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166652216, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166652218, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166652273, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166652277, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166652315, "dur": 251, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166652572, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166652575, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166652632, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166652634, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166652689, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166652691, "dur": 323, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166653026, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166653031, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166653100, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166653102, "dur": 211, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166653322, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166653327, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166653388, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166653390, "dur": 143329, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166796729, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166796735, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166796778, "dur": 29, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166796808, "dur": 19367, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166816184, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166816188, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166816218, "dur": 121989, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166938215, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166938220, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166938324, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166938328, "dur": 71, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166938402, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309166938406, "dur": 100031, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167038449, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167038454, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167038537, "dur": 41, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167038580, "dur": 8576, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167047169, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167047174, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167047249, "dur": 31, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167047281, "dur": 30128, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167077417, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167077422, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167077503, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167077505, "dur": 6827, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167084343, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167084347, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167084428, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167084432, "dur": 2510, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167086952, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167086958, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167087013, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167087043, "dur": 91055, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167178116, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167178120, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167178216, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167178222, "dur": 1071, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167179308, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167179314, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167179415, "dur": 31, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167179447, "dur": 569, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167180030, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167180035, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167180103, "dur": 402, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 19704, "tid": 12884901888, "ts": 1751309167180513, "dur": 11380, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 19704, "tid": 1767, "ts": 1751309167210543, "dur": 2554, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 19704, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 19704, "tid": 8589934592, "ts": 1751309166471207, "dur": 111295, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 19704, "tid": 8589934592, "ts": 1751309166582506, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 19704, "tid": 8589934592, "ts": 1751309166582510, "dur": 1338, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 19704, "tid": 1767, "ts": 1751309167213101, "dur": 13, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 19704, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 19704, "tid": 4294967296, "ts": 1751309166448973, "dur": 744677, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751309166453091, "dur": 10059, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751309167193768, "dur": 6266, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751309167197280, "dur": 57, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 19704, "tid": 4294967296, "ts": 1751309167200146, "dur": 14, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 19704, "tid": 1767, "ts": 1751309167213116, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751309166481045, "dur": 62, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309166481135, "dur": 2719, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309166483872, "dur": 943, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309166484967, "dur": 87, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751309166485054, "dur": 517, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309166486515, "dur": 244, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_67CB883191F4D66D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309166487671, "dur": 1745, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_BFEF6BF3A445E475.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309166494401, "dur": 272, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309166496015, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309166497301, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_9A20CFD48BC11F43.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751309166504172, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751309166505822, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751309166509927, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751309166511490, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751309166513417, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751309166513820, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751309166485628, "dur": 28520, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309166514166, "dur": 665880, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309167180062, "dur": 241, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309167180304, "dur": 127, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309167180614, "dur": 66, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751309167180710, "dur": 1732, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751309166486028, "dur": 28248, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166514291, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_256223B421F6FD72.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166514518, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_256223B421F6FD72.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166514572, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_69FC355D1BD4BB29.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166514671, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_15A0A72BE0744047.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166514724, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166514814, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_33AFB99E7761AA5C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166514878, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166515051, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BA0CB78B239CCACF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166515149, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BADDD17E000661FB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166515414, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_8FE2697BF98A2193.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166515492, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166515702, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_9870EDF59A107BDF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166515818, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_5FDF6992734E0361.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166516018, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_486DF76931BD0557.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166516132, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_7AC1C063560F2BC6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166516182, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166516424, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166516636, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751309166516799, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166516854, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166517062, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751309166517228, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166517339, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166517394, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751309166517479, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166517554, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166517629, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166517782, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166517915, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166518169, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166518348, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166518399, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751309166518631, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166518796, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166518894, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751309166519044, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166519115, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166519201, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751309166519306, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166519387, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751309166519583, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166519663, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751309166519758, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166519861, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751309166519912, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166520183, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166520369, "dur": 1435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166521805, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166523297, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166523851, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166524275, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166524559, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166524819, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166525479, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166525814, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166526304, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\Controls\\TextureArrayControl.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751309166526107, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166526871, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166527113, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166527362, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166527639, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166527937, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166528167, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166528403, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166528839, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166529133, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166529489, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166529875, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166530192, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166530501, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166530762, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166531015, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166531345, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166531623, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166531986, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166532411, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166532655, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166533548, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166534591, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166535067, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166535196, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166535955, "dur": 808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166536764, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166536939, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166537144, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751309166537676, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166537858, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166538166, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166538284, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_407F07063643C512.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166538384, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166538577, "dur": 590, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166539176, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751309166539916, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166540071, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166540209, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166540445, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166541020, "dur": 1196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751309166542217, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166542471, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166542636, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166542766, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751309166543455, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166543575, "dur": 6312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166549890, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751309166550096, "dur": 83656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166633763, "dur": 2354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751309166636119, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166636211, "dur": 2428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751309166638641, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166638785, "dur": 2377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751309166641163, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166641309, "dur": 5638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751309166647006, "dur": 3020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751309166650027, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166650215, "dur": 3712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751309166653929, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751309166654078, "dur": 525988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166485799, "dur": 28384, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166514239, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166514391, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_0E82E95F1D4B1B8B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166514548, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166514627, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_0E82E95F1D4B1B8B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166514696, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_1DC8148833156840.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166514844, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_E5318FF293833B30.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166514903, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166514973, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_0EEB0D491376C433.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166515072, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_BFD5601BA05C7CD8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166515182, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_BEB6379C07D3D185.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166515241, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166515410, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_BB587A5119495C88.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166515673, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166515890, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_7E691CD5ECB673D9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166515975, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166516051, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166516120, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_E106F825F731F4BC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166516182, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166516252, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_0854A01FF08BC637.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166516391, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166516495, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166516645, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166516760, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166516887, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166516951, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166517061, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751309166517196, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751309166517301, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751309166517587, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166517700, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166517877, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751309166518042, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166518133, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751309166518207, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166518431, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751309166518540, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166518737, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751309166518812, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166518954, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751309166519033, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166519189, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166519583, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751309166519680, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166519906, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166520235, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166520321, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166520705, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166522219, "dur": 1414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166523633, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166524339, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166524591, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166524830, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166525295, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166525760, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166526029, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166526313, "dur": 1071, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Data\\Nodes\\Utility\\Logic\\IsInfiniteNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751309166526299, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166527768, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166528007, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166528263, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166528565, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166528815, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166529139, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166529496, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166529921, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166530357, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166530585, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166530809, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166531128, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166531391, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166531672, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166532143, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166532386, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166532657, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166533763, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166534682, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166535194, "dur": 751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166535947, "dur": 779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166536728, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166536959, "dur": 1928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751309166538889, "dur": 758, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166539713, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166539948, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166540236, "dur": 1927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751309166542164, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166542391, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166542644, "dur": 1047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751309166543693, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166543847, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166544028, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166544086, "dur": 940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751309166545027, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166545168, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751309166545375, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751309166545846, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166545962, "dur": 87806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166633781, "dur": 2405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751309166636187, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166636299, "dur": 2473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751309166638773, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166639022, "dur": 275, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751309166639300, "dur": 2740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751309166642042, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166642200, "dur": 4355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751309166646557, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166646746, "dur": 2356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751309166649103, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166649450, "dur": 3758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751309166653210, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309166653314, "dur": 424384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751309167077732, "dur": 307, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751309167077699, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751309167078085, "dur": 102015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166485894, "dur": 28320, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166514239, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166514361, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_8A89D5AFAE49E63E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166514606, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_1182EE28E68C62C8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166514725, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166514897, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_C047F56604413718.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166514993, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_308026626194FDB8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166515090, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D7031BE9F197A0A6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166515188, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8B3842F7B08DFF89.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166515283, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_433711FCB6016F00.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166515380, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_433711FCB6016F00.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166515450, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_7DC1C7578DCAB919.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166515764, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_D1635D1E3A6F959D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166515892, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_80843626DE95EA13.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166516074, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166516210, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166516269, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166516382, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751309166516465, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166516665, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751309166516775, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166516887, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751309166517052, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751309166517231, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166517487, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166517558, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166517618, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166517678, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751309166517743, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166517851, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166517959, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751309166518014, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751309166518111, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166518216, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166518700, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166518846, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166519025, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751309166519100, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166519322, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166519531, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751309166519925, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166520338, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166520984, "dur": 2049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166523034, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166523310, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166523634, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166524510, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166524738, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166525687, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166526470, "dur": 954, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\Views\\Slots\\ColorRGBSlotControlView.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751309166526009, "dur": 1709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166527719, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166527978, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166528219, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166528582, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166528835, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166529076, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166529571, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166529873, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166530475, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166530755, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166531042, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166531320, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166531605, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166532162, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166532453, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166532705, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166534011, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166534621, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166535324, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166535973, "dur": 779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166536786, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166536956, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166537056, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166537125, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751309166538027, "dur": 615, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166538655, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166538732, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166538893, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166539173, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166539436, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751309166540119, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166540229, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751309166540939, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166541303, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166541635, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166541841, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166541910, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166542098, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166542296, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166542467, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751309166542737, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166542838, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751309166543456, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166543614, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166543778, "dur": 40758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166586605, "dur": 362, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 3, "ts": 1751309166586968, "dur": 1328, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 3, "ts": 1751309166588297, "dur": 107, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 3, "ts": 1751309166584538, "dur": 3875, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166588414, "dur": 45299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166633732, "dur": 2384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751309166636118, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166636343, "dur": 9565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751309166645911, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166646081, "dur": 2898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751309166648981, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166649108, "dur": 3750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751309166652859, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166652985, "dur": 1077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751309166654063, "dur": 525975, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166485942, "dur": 28287, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166514240, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_82F8B03BA9650A81.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309166514634, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0333895C18D609C9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309166514730, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166514817, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_8EA224AC6679B7C2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309166514941, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_67CB883191F4D66D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309166515005, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166515067, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0999ABDFA115F312.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309166515328, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166515417, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D7CA9FED890DDAB1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309166515489, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166515759, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_C1BEC5DDD68DA9AF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309166515888, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FEA05FC664382286.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309166516272, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166516511, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166516646, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166517010, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751309166517400, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751309166517541, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166517643, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166517758, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166517888, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166517961, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751309166518013, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166518112, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166518250, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751309166518338, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166518529, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166518647, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166518756, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166518831, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166519244, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166519574, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751309166519715, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166519916, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166520141, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751309166520197, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166520296, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166520364, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166521698, "dur": 1582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166523281, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166523654, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166524509, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166524743, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166525498, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166525873, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166526208, "dur": 609, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\Colors\\ShaderGraphHeatmapValues.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751309166526161, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166527065, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166527323, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166527564, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166527876, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166528117, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166528441, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166528733, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166528972, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166529230, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166529625, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166530117, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166530427, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166530698, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166530934, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166531243, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166531519, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166532121, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166532415, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166532671, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166533450, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166534570, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166534855, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166535312, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166535981, "dur": 765, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166536750, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309166536938, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166537072, "dur": 867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751309166537940, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166538084, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309166538371, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166538625, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751309166539407, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166539666, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166539777, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309166539971, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166540273, "dur": 1740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751309166542014, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166542254, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166542404, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309166542653, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751309166543626, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166543899, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309166544061, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166544170, "dur": 893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751309166545065, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166545218, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309166545418, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751309166546041, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166546140, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751309166546332, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751309166546914, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166547093, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166547165, "dur": 86524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166633714, "dur": 2471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751309166636187, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166636391, "dur": 2644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751309166639036, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166639313, "dur": 3014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751309166642329, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166642514, "dur": 3685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751309166646201, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166646343, "dur": 5197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751309166651541, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166651793, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166652040, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166652176, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166652355, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166652448, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166652687, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309166653356, "dur": 431250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751309167084660, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751309167084608, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751309167084999, "dur": 2646, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751309167087652, "dur": 92398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166486000, "dur": 28242, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166514253, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_5F13DCF03A10B823.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166514529, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_467B09015CCE177C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166514733, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_B83C6071BB394334.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166514856, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_595F986B2A245834.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166514959, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_526B70D8724FDBD0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166515060, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_60AF5DD29DC33809.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166515275, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_FE92BD4D4925733A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166515379, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_D8FCB30ADD921A73.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166515487, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7B8E45163E4907D2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166515727, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166515801, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_584BDBB0AACB340C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166516026, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A587944D9110748A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166516140, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166516212, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A587944D9110748A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166516430, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166516641, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751309166516719, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166516837, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166516911, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166517004, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166517085, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751309166517295, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751309166517380, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166517514, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166517699, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166517848, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166518037, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166518129, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166518229, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166518336, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166518423, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751309166518505, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166518576, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166518723, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166518854, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751309166519037, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751309166519104, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166519171, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166519254, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166519358, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751309166519699, "dur": 412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166520269, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751309166520322, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166520766, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166522210, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166523504, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166524371, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166524601, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166524975, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166525635, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166525918, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166526312, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\Controls\\DielectricSpecularControl.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751309166526154, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166526978, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166527217, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166527475, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166527781, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166528646, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166528877, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166529126, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166529843, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166530166, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166530462, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166530715, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166530952, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166531259, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166531740, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166532191, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166532464, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166532745, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166533584, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166534450, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166534636, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166534941, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166535195, "dur": 780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166535976, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166536740, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166536964, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751309166537692, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166537782, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166537895, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166538376, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166538551, "dur": 990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751309166539542, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166539610, "dur": 944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751309166540555, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166540630, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751309166541533, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166541738, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166541802, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166542241, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166542436, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751309166542641, "dur": 1099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751309166543741, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166543854, "dur": 44571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166588427, "dur": 47718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166636160, "dur": 4085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751309166640247, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166640427, "dur": 3250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751309166643679, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166644187, "dur": 4092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751309166648281, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166648477, "dur": 2832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751309166651311, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166651513, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166651624, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166651717, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751309166651896, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751309166652104, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751309166652319, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166652456, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166652706, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751309166653766, "dur": 526319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166486068, "dur": 28239, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166514321, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_CC3B7EF1A2C4EA40.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166514575, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_E681FA607883332E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166514656, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_D32EF602AAA54BE6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166514798, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_76EB92ADF25C401C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166514856, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166514917, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_4E1AB9DAA17812DB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166515064, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_7676AB112B74F121.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166515168, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_DB48149D0776AF57.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166515267, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_209987431F1A3EDC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166515375, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B1D6DCAB2D5A18DA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166515479, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_93B77B82222D1B4A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166515746, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E689A207C3E7A0C2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166515837, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166515899, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_922751811D286E63.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166516014, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_1638DBF3D84589E5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166516076, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166516240, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_570D95476513426D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166516297, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166516445, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166516642, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166516751, "dur": 11638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751309166528482, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166528738, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166529017, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166529260, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166529980, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166530349, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166530577, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166530823, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166531323, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166531615, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166531958, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166532368, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166532612, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166532956, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166533766, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166534656, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166535062, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166535206, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166535979, "dur": 1098, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166537079, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166537284, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166537445, "dur": 2237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751309166539684, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166539953, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166540115, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166540179, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166540418, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166540552, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751309166541078, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166541253, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166541368, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.Editor.ref.dll_E4491E98C71DB4B4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166541603, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166541714, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166541898, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166541986, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751309166542535, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166542665, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166542933, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166543289, "dur": 1658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751309166544956, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166545116, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166545337, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751309166546137, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166546340, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751309166547111, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751309166547280, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751309166547691, "dur": 85993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166633687, "dur": 2428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751309166636117, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166636376, "dur": 2264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751309166638641, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166638775, "dur": 3454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751309166642230, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166642649, "dur": 2479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751309166645130, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166645370, "dur": 5054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751309166650425, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166650824, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166650978, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166651219, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166651390, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751309166651461, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166651600, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166651794, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166652117, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751309166652226, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166652360, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166652472, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166652646, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166652749, "dur": 1284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751309166654064, "dur": 526016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166486154, "dur": 28187, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166514359, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7B82E1658E753B21.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166514535, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_F8CCEDBE36EB62B5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166514661, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_14A5559FFED55C50.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166514732, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166514794, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DAD9D37CE1A4BA55.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166514901, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_9D64899DE49D3B06.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166515001, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_A164BAE5670FC544.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166515318, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166515385, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_A164BAE5670FC544.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166515464, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B6153E13308654D1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166515755, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_C01CA8838C1CE31C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166515817, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166515873, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_815CA55F1B3FC2AD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166515979, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_1FC72A0013DC4EEB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166516133, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_0D77228FB13B7FB6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166516192, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166516316, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7F3203F2669A3DC6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166516424, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166516670, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7F3203F2669A3DC6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166516941, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166517120, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309166517454, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166517605, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166517814, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166517956, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309166518161, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166518275, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309166518585, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166518669, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166518886, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166519197, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309166519261, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166519566, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309166519688, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166519762, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2400031028012695012.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309166519867, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10719215101466552486.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309166519933, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166520187, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166520283, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751309166520355, "dur": 1978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166522334, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166522637, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166522887, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166523085, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166523283, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166523578, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166524429, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166524721, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166525653, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166526203, "dur": 628, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\Inspector\\WindowDockingLayout.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751309166526031, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166526928, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166527147, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166527376, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166527731, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166527988, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166528224, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166528449, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166528827, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166529134, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166529448, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166529718, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166530071, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166530352, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166530571, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166530962, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166531271, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166531583, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166532266, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166533078, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166534167, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166534561, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166534886, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166535198, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166535948, "dur": 795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166536746, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166536919, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166536980, "dur": 983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751309166537964, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166538131, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166538339, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166538585, "dur": 1270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751309166539856, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166540261, "dur": 798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751309166541060, "dur": 563, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166541682, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166542412, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166542661, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751309166543391, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166543553, "dur": 5194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166548750, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751309166548926, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751309166549332, "dur": 84405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166633749, "dur": 2367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751309166636196, "dur": 2443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751309166638641, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166638834, "dur": 2342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751309166641178, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166641236, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751309166641292, "dur": 5656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751309166646961, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166647019, "dur": 2700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751309166649769, "dur": 3837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751309166653608, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751309166653734, "dur": 526306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166486200, "dur": 28180, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166514399, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_A1485D01311C863E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309166514513, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166514628, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_857E61E190401437.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309166514750, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166514809, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9843DD54C5E6159E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309166514872, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166515027, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_EE062F8861099912.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309166515142, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8D9A2E8777AB6882.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309166515273, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_472976495F1230C7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309166515388, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_472976495F1230C7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309166515485, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_D4344483B74B03A7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309166515706, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166515776, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_48483D327EA17808.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309166515907, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3EF742C8CA4C5E1E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309166515987, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166516072, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166516269, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166516371, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751309166516450, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166516516, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166516761, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751309166516954, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166517054, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751309166517284, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166517348, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751309166517562, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166517679, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751309166517755, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166518130, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751309166518191, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166518427, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166518615, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166518838, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166519039, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751309166519164, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166519371, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751309166519482, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751309166519576, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166519634, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751309166519828, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751309166519969, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166520287, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751309166520457, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166521774, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166523376, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166523679, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166524513, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166524749, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166525709, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166525952, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166526194, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166526980, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166527221, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166527587, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166527882, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166528130, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166528371, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166528739, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166529036, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166529373, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166529631, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166530258, "dur": 593, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\InspectorAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751309166530109, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166530933, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166531646, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166531984, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166532338, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166532597, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166532828, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166533634, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166534575, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166534869, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166535551, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166535951, "dur": 781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166536738, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309166536938, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166537070, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751309166537710, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166537924, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309166538311, "dur": 1072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751309166539384, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166539574, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166539750, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309166540147, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166540220, "dur": 2344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751309166542565, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166542739, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166542799, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309166543017, "dur": 1588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751309166544606, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166544760, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751309166544961, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751309166545527, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166545669, "dur": 88032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166633703, "dur": 2485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751309166636190, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166636273, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751309166636386, "dur": 2357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751309166638745, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166638897, "dur": 395, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751309166639310, "dur": 4117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751309166643429, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166643566, "dur": 3702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751309166647270, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166647501, "dur": 2793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751309166650296, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166650479, "dur": 3449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751309166653929, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751309166654080, "dur": 525963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166486247, "dur": 28158, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166514418, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_44462716F1E6187F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166514612, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_596B249A76101BE6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166514722, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F27EBD72F4881481.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166514825, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_51B6A25823037467.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166514936, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C8A867A03915DFB5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166515056, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_91BF37D1F055EC15.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166515161, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E04F87D224606974.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166515270, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3E1DF6609ACAC61F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166515391, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3E1DF6609ACAC61F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166515493, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DA7D4247AC2DB3F7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166515740, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DA7D4247AC2DB3F7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166515806, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_CBABBF352E91654E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166515895, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166515965, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AFE94C31B12D3213.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166516029, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166516136, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166516212, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751309166516489, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751309166516543, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166516639, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751309166516852, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166517146, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166517229, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166517320, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166517405, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751309166517527, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166517716, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166518268, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166518481, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166518638, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166518742, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166518864, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166519049, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166519273, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166519388, "dur": 573, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751309166519992, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166520270, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14502112590723880366.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751309166520358, "dur": 1508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166521866, "dur": 1486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166523353, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166523747, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166524377, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166524653, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166525503, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166525797, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166526207, "dur": 923, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\Inspector\\PropertyDrawers\\SampleTexture3DNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751309166526066, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166527235, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166527464, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166528505, "dur": 599, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.State\\AnyState.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751309166528279, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166529149, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166529498, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166529792, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166530111, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166530409, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166530641, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166530949, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166531227, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166531545, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166532258, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166532512, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166532758, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166533746, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166534807, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166535316, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166535982, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166536755, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166536938, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166537084, "dur": 852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751309166537937, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166538394, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166538697, "dur": 1337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751309166540035, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166540472, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166540545, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751309166541046, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166541123, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166541875, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166541938, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751309166542695, "dur": 672, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166543385, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166543447, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751309166543695, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166543790, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751309166544436, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166544641, "dur": 91563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166636206, "dur": 3963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751309166640171, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166640385, "dur": 4383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751309166644770, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166644901, "dur": 2952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751309166647854, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166647933, "dur": 2399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751309166650334, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166650418, "dur": 3509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751309166653929, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751309166654105, "dur": 525952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166486295, "dur": 28128, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166514434, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FECC8B8B813FB02A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166514543, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166514667, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_B566D0CB2E423B86.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166514726, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166514929, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7E47E9336205E31D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166515038, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0FEAE2B241ACD344.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166515146, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_64D9AC7F4C4AC8A8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166515243, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_64D9AC7F4C4AC8A8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166515307, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1FE2264601546D0E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166515372, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166515447, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_77F91A8BF54EFB7C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166515658, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166515758, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_0B2798972D9D68EB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166515920, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166515976, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_20A6B658F2F96E47.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166516270, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166516410, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166516518, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166516701, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166516770, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166516940, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166516996, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751309166517218, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166517293, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751309166517480, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166517621, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166517785, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166517906, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751309166517972, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751309166518162, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166518295, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166518406, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751309166518469, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166518549, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166518663, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166518804, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166519026, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166519223, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166519324, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166519889, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166520159, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166520294, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751309166520375, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166521649, "dur": 1985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166523634, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166524547, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166524809, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166525707, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166526001, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166526293, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166527067, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166527321, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166527739, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166527970, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166528264, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166528635, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166528874, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166529130, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166529440, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166529871, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166530214, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166530533, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166530786, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166531053, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Graphs\\IGraphParentElement.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751309166531036, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166531881, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166532212, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166532561, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166532822, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166533724, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166534856, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166535205, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166535983, "dur": 983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166536986, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166537413, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166537473, "dur": 853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751309166538327, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166538428, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166538582, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166538783, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166538879, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166539076, "dur": 618, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166539704, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166539949, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166540031, "dur": 856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751309166540888, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166541014, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751309166541878, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166542125, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166542682, "dur": 2495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166545179, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166545338, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166545428, "dur": 1818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751309166547247, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166547415, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166547640, "dur": 941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751309166548582, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166548743, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166548924, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166548981, "dur": 733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751309166549723, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166549816, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166549882, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751309166550129, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751309166551019, "dur": 81, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166552422, "dur": 244992, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751309166804115, "dur": 12578, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751309166803670, "dur": 13096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751309166816767, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166816876, "dur": 118587, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751309166816873, "dur": 120230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751309166938829, "dur": 204, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751309166939963, "dur": 99149, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751309167077682, "dur": 352, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1751309167077671, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1751309167078079, "dur": 102003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166486343, "dur": 28092, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166514447, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_8325F75A1495950D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166514546, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166514615, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_8DE949524442C9D8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166514728, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166514977, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_5DFD03B93DA29B4D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166515194, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_658DA73782B14E20.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166515253, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166515686, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_F21194D2F617C9ED.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166515937, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_1FF1C2755BAA46D4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166516036, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_228E07120E318326.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166516088, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166516313, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_07FA64190ECA4CC1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166516485, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166516597, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166516667, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751309166516800, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_3D651E01942E03F8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166516932, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166517039, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751309166517142, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166517222, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166517322, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166517437, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166517702, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166517837, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166517907, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751309166517995, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751309166518105, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166518173, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166518273, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166518379, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751309166518514, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166518688, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166518816, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166518934, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751309166519255, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166519380, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751309166519508, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166519570, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751309166519913, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166520160, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166520336, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166520741, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166522033, "dur": 1687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166523721, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166524092, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166524454, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166524697, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166525683, "dur": 551, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@89399b10acbb\\Editor\\AssetPostProcessors\\AutodeskInteractiveMaterialImport.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751309166526314, "dur": 508, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@89399b10acbb\\Editor\\2D\\ShapeEditor\\View\\IEditablePathView.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751309166525530, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166526901, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166527163, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166527401, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166528021, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166529276, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166529677, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166530044, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166530553, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166530799, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166531310, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166531604, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166532167, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166532451, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166532675, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166533633, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166534432, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166534794, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166535349, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166535967, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166536730, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166536938, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166537129, "dur": 1250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751309166538380, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166538506, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166538734, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166538803, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166538942, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166539098, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166539392, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751309166540259, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166540521, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751309166541111, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166541235, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166541319, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166541509, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Editor.ref.dll_F96E2A343514CFAE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166541649, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166541710, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166541914, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166542309, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751309166543152, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166543212, "dur": 2935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166546149, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751309166546315, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166546376, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751309166547032, "dur": 86751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166633794, "dur": 2391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751309166636187, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166636303, "dur": 5247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751309166641594, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166641853, "dur": 3400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751309166645265, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166645382, "dur": 3889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751309166649272, "dur": 1277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166650568, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166650776, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166650843, "dur": 375, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166651256, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166651348, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166651642, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751309166651810, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166651987, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166652215, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166652361, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166652464, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166652564, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166652709, "dur": 1317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751309166654077, "dur": 525999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166486413, "dur": 28047, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166514463, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F697CEAF687CB7A2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166514682, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_0E07881F85CDD302.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166514860, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166515005, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A619838E516A75AC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166515111, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_DBB153F07C192DFF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166515198, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F0EAC96F0603CD08.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166515262, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166515426, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_1A2A3B79BD3B4CFD.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166515487, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166515719, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_EAAAA0867963BCF7.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166515823, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A28CE54978CD971D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166516034, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_12124AFB436F9D5D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166516208, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166516370, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166516502, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166516683, "dur": 12350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751309166529120, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166529212, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166529358, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166529690, "dur": 6162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751309166536009, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166536165, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751309166536594, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166536729, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166536955, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751309166537754, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166537870, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_E8EFDB1B8D5C174E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166537982, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166538057, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166538326, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166538440, "dur": 1026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751309166539467, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166539687, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751309166539921, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166540012, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751309166540532, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166540826, "dur": 1017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1751309166541882, "dur": 431, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166543299, "dur": 80347, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1751309166633694, "dur": 2421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751309166636117, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166636275, "dur": 2367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751309166638643, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166638780, "dur": 2361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751309166641143, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166641375, "dur": 2342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751309166643719, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166643893, "dur": 2336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751309166646231, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166646471, "dur": 2281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751309166648753, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166648845, "dur": 2584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751309166651430, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166651631, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166651769, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751309166651974, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166652126, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751309166652345, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166652443, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166652684, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166653000, "dur": 150675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166803752, "dur": 131749, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751309166803680, "dur": 133364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751309166938432, "dur": 350, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751309166939673, "dur": 108166, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751309167084595, "dur": 94112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751309167084584, "dur": 94124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751309167178756, "dur": 1237, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751309167189663, "dur": 2359, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 19704, "tid": 1767, "ts": 1751309167214173, "dur": 5965, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 19704, "tid": 1767, "ts": 1751309167220209, "dur": 3578, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 19704, "tid": 1767, "ts": 1751309167207464, "dur": 17666, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}