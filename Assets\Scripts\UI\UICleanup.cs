using UnityEngine;

namespace ZombieGame.UI
{
    /// <summary>
    /// Utility script to clean up duplicate UI systems
    /// </summary>
    public class UICleanup : MonoBehaviour
    {
        [ContextMenu("Clean Up Duplicate UIs")]
        public void CleanUpDuplicateUIs()
        {
            Debug.Log("Starting UI cleanup...");
            
            // Find all canvases
            var allCanvases = FindObjectsByType<Canvas>(FindObjectsSortMode.None);
            Debug.Log($"Found {allCanvases.Length} canvases in scene");
            
            // Find all SimpleInventoryUI instances
            var simpleUIs = FindObjectsByType<SimpleInventoryUI>(FindObjectsSortMode.None);
            Debug.Log($"Found {simpleUIs.Length} SimpleInventoryUI instances");
            
            // Find all InventoryUI instances
            var inventoryUIs = FindObjectsByType<InventoryUI>(FindObjectsSortMode.None);
            Debug.Log($"Found {inventoryUIs.Length} InventoryUI instances");
            
            // Find all InventoryUISetup instances
            var setupScripts = FindObjectsByType<InventoryUISetup>(FindObjectsSortMode.None);
            Debug.Log($"Found {setupScripts.Length} InventoryUISetup instances");
            
            // Remove duplicate canvases named "InventoryCanvas"
            int canvasesRemoved = 0;
            foreach (var canvas in allCanvases)
            {
                if (canvas.name == "InventoryCanvas")
                {
                    Debug.Log($"Removing duplicate InventoryCanvas: {canvas.name}");
                    DestroyImmediate(canvas.gameObject);
                    canvasesRemoved++;
                }
            }
            
            // Remove all SimpleInventoryUI instances
            foreach (var ui in simpleUIs)
            {
                Debug.Log($"Removing SimpleInventoryUI: {ui.name}");
                DestroyImmediate(ui.gameObject);
            }
            
            // Disable all InventoryUI instances
            foreach (var ui in inventoryUIs)
            {
                Debug.Log($"Disabling InventoryUI: {ui.name}");
                ui.enabled = false;
            }
            
            // Disable all InventoryUISetup instances
            foreach (var setup in setupScripts)
            {
                Debug.Log($"Disabling InventoryUISetup: {setup.name}");
                setup.enabled = false;
            }
            
            Debug.Log($"UI cleanup complete! Removed {canvasesRemoved} duplicate canvases, {simpleUIs.Length} SimpleInventoryUIs");
            Debug.Log("You can now run the game and the PlayerInventoryIntegration will create a clean UI system.");
        }
        
        [ContextMenu("List All UI Components")]
        public void ListAllUIComponents()
        {
            Debug.Log("=== UI COMPONENT INVENTORY ===");
            
            var allCanvases = FindObjectsByType<Canvas>(FindObjectsSortMode.None);
            Debug.Log($"Canvases ({allCanvases.Length}):");
            foreach (var canvas in allCanvases)
            {
                Debug.Log($"  - {canvas.name} (Active: {canvas.gameObject.activeInHierarchy})");
            }
            
            var simpleUIs = FindObjectsByType<SimpleInventoryUI>(FindObjectsSortMode.None);
            Debug.Log($"SimpleInventoryUI ({simpleUIs.Length}):");
            foreach (var ui in simpleUIs)
            {
                Debug.Log($"  - {ui.name} (Enabled: {ui.enabled})");
            }
            
            var inventoryUIs = FindObjectsByType<InventoryUI>(FindObjectsSortMode.None);
            Debug.Log($"InventoryUI ({inventoryUIs.Length}):");
            foreach (var ui in inventoryUIs)
            {
                Debug.Log($"  - {ui.name} (Enabled: {ui.enabled})");
            }
            
            var setupScripts = FindObjectsByType<InventoryUISetup>(FindObjectsSortMode.None);
            Debug.Log($"InventoryUISetup ({setupScripts.Length}):");
            foreach (var setup in setupScripts)
            {
                Debug.Log($"  - {setup.name} (Enabled: {setup.enabled})");
            }
            
            Debug.Log("=== END INVENTORY ===");
        }
    }
}
