using UnityEngine;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Data for consumable items like food, medicine, drinks
    /// </summary>
    [CreateAssetMenu(fileName = "New Consumable", menuName = "Inventory/Consumable Item")]
    public class ConsumableItemData : ItemData
    {
        [Header("Consumable Properties")]
        [Range(0f, 100f)]
        public float healthRestore = 0f;
        
        [Range(0f, 100f)]
        public float hungerRestore = 0f;
        
        [Range(0f, 100f)]
        public float thirstRestore = 0f;
        
        [Range(0f, 100f)]
        public float staminaRestore = 0f;
        
        [Header("Effects")]
        public float consumeTime = 2f; // Time to consume in seconds
        public bool canConsumeWhileMoving = false;
        
        [Header("Status Effects")]
        public StatusEffect[] statusEffects;
        
        private void Awake()
        {
            itemType = ItemType.Consumable;
        }
    }
    
    [System.Serializable]
    public class StatusEffect
    {
        public StatusEffectType effectType;
        public float duration;
        public float intensity;
        
        [TextArea(2, 3)]
        public string description;
    }
    
    public enum StatusEffectType
    {
        HealthRegeneration,
        HealthDegeneration,
        StaminaBoost,
        SpeedBoost,
        Poisoned,
        Infected,
        Immunity,
        NightVision,
        Painkiller
    }
}
