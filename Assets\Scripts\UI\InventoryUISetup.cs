using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace ZombieGame.UI
{
    /// <summary>
    /// Helper script to automatically create inventory UI elements
    /// </summary>
    public class InventoryUISetup : MonoBehaviour
    {
        [Header("Auto Setup")]
        [SerializeField] private bool setupOnStart = true;
        [SerializeField] private bool createCanvas = true;
        
        [Header("UI Settings")]
        [SerializeField] private Vector2 inventoryPanelSize = new Vector2(800, 600);
        [SerializeField] private Vector2 hotbarSize = new Vector2(400, 60);
        [SerializeField] private int slotsPerRow = 5;
        [SerializeField] private float slotSize = 60f;
        [SerializeField] private float slotSpacing = 5f;
        
        [Header("Colors")]
        [SerializeField] private Color panelColor = new Color(0.1f, 0.1f, 0.1f, 0.9f);
        [SerializeField] private Color slotColor = new Color(0.3f, 0.3f, 0.3f, 0.8f);
        [SerializeField] private Color hotbarColor = new Color(0.2f, 0.2f, 0.2f, 0.8f);
        
        private Canvas canvas;
        private GameObject inventoryPanel;
        private GameObject hotbarPanel;
        
        private void Start()
        {
            if (setupOnStart)
            {
                SetupInventoryUI();
            }
        }
        
        [ContextMenu("Setup Inventory UI")]
        public void SetupInventoryUI()
        {
            Debug.Log("Setting up inventory UI...");
            
            // Create or find canvas
            SetupCanvas();
            
            // Create inventory panel
            CreateInventoryPanel();
            
            // Create hotbar panel
            CreateHotbarPanel();
            
            // Add InventoryUI component
            AddInventoryUIComponent();
            
            Debug.Log("Inventory UI setup complete!");
        }
        
        private void SetupCanvas()
        {
            canvas = FindFirstObjectByType<Canvas>();
            
            if (canvas == null && createCanvas)
            {
                // Create new canvas
                GameObject canvasObj = new GameObject("InventoryCanvas");
                canvas = canvasObj.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvas.sortingOrder = 100; // High sorting order for UI
                
                // Add CanvasScaler
                CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
                scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
                scaler.referenceResolution = new Vector2(1920, 1080);
                scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
                scaler.matchWidthOrHeight = 0.5f;
                
                // Add GraphicRaycaster
                canvasObj.AddComponent<GraphicRaycaster>();
                
                Debug.Log("Created new canvas for inventory UI");
            }
            
            if (canvas == null)
            {
                Debug.LogError("No canvas found and createCanvas is disabled!");
                return;
            }
        }
        
        private void CreateInventoryPanel()
        {
            // Create main inventory panel
            GameObject panelObj = new GameObject("InventoryPanel");
            panelObj.transform.SetParent(canvas.transform, false);
            
            // Setup RectTransform
            RectTransform panelRect = panelObj.AddComponent<RectTransform>();
            panelRect.anchorMin = new Vector2(0.5f, 0.5f);
            panelRect.anchorMax = new Vector2(0.5f, 0.5f);
            panelRect.sizeDelta = inventoryPanelSize;
            panelRect.anchoredPosition = Vector2.zero;
            
            // Add background image
            Image panelImage = panelObj.AddComponent<Image>();
            panelImage.color = panelColor;
            
            // Create backpack section
            CreateBackpackSection(panelObj);
            
            // Create equipment section
            CreateEquipmentSection(panelObj);
            
            // Create player info section
            CreatePlayerInfoSection(panelObj);
            
            inventoryPanel = panelObj;
            inventoryPanel.SetActive(false); // Start hidden
        }
        
        private void CreateBackpackSection(GameObject parent)
        {
            // Create backpack container
            GameObject backpackObj = new GameObject("BackpackContainer");
            backpackObj.transform.SetParent(parent.transform, false);
            
            RectTransform backpackRect = backpackObj.AddComponent<RectTransform>();
            backpackRect.anchorMin = new Vector2(0.5f, 0.1f);
            backpackRect.anchorMax = new Vector2(0.9f, 0.8f);
            backpackRect.offsetMin = Vector2.zero;
            backpackRect.offsetMax = Vector2.zero;
            
            // Add GridLayoutGroup for automatic slot arrangement
            GridLayoutGroup gridLayout = backpackObj.AddComponent<GridLayoutGroup>();
            gridLayout.cellSize = new Vector2(slotSize, slotSize);
            gridLayout.spacing = new Vector2(slotSpacing, slotSpacing);
            gridLayout.constraint = GridLayoutGroup.Constraint.FixedColumnCount;
            gridLayout.constraintCount = slotsPerRow;
            gridLayout.startCorner = GridLayoutGroup.Corner.UpperLeft;
            gridLayout.startAxis = GridLayoutGroup.Axis.Horizontal;
            gridLayout.childAlignment = TextAnchor.UpperLeft;
            
            // Add title
            CreateSectionTitle(parent, "Backpack", new Vector2(0.5f, 0.85f));
        }
        
        private void CreateEquipmentSection(GameObject parent)
        {
            // Create equipment container
            GameObject equipmentObj = new GameObject("EquipmentContainer");
            equipmentObj.transform.SetParent(parent.transform, false);
            
            RectTransform equipmentRect = equipmentObj.AddComponent<RectTransform>();
            equipmentRect.anchorMin = new Vector2(0.05f, 0.1f);
            equipmentRect.anchorMax = new Vector2(0.45f, 0.8f);
            equipmentRect.offsetMin = Vector2.zero;
            equipmentRect.offsetMax = Vector2.zero;
            
            // Add VerticalLayoutGroup for equipment slots
            VerticalLayoutGroup verticalLayout = equipmentObj.AddComponent<VerticalLayoutGroup>();
            verticalLayout.spacing = slotSpacing;
            verticalLayout.childAlignment = TextAnchor.UpperCenter;
            verticalLayout.childControlHeight = false;
            verticalLayout.childControlWidth = false;
            verticalLayout.childForceExpandHeight = false;
            verticalLayout.childForceExpandWidth = false;
            
            // Add title
            CreateSectionTitle(parent, "Equipment", new Vector2(0.25f, 0.85f));
        }
        
        private void CreatePlayerInfoSection(GameObject parent)
        {
            // Create player info panel
            GameObject infoObj = new GameObject("PlayerInfoPanel");
            infoObj.transform.SetParent(parent.transform, false);
            
            RectTransform infoRect = infoObj.AddComponent<RectTransform>();
            infoRect.anchorMin = new Vector2(0.05f, 0.85f);
            infoRect.anchorMax = new Vector2(0.95f, 0.95f);
            infoRect.offsetMin = Vector2.zero;
            infoRect.offsetMax = Vector2.zero;
            
            // Add background
            Image infoImage = infoObj.AddComponent<Image>();
            infoImage.color = new Color(0.2f, 0.2f, 0.2f, 0.5f);
            
            // Create health bar
            CreateStatusBar(infoObj, "HealthBar", new Vector2(0.1f, 0.7f), new Vector2(0.35f, 0.9f), Color.red);
            
            // Create stamina bar
            CreateStatusBar(infoObj, "StaminaBar", new Vector2(0.4f, 0.7f), new Vector2(0.65f, 0.9f), Color.blue);
            
            // Create weight text
            CreateInfoText(infoObj, "WeightText", new Vector2(0.7f, 0.5f), "Weight: 0/50");
        }
        
        private void CreateHotbarPanel()
        {
            // Create hotbar panel
            GameObject hotbarObj = new GameObject("HotbarPanel");
            hotbarObj.transform.SetParent(canvas.transform, false);
            
            // Setup RectTransform
            RectTransform hotbarRect = hotbarObj.AddComponent<RectTransform>();
            hotbarRect.anchorMin = new Vector2(0.5f, 0f);
            hotbarRect.anchorMax = new Vector2(0.5f, 0f);
            hotbarRect.sizeDelta = hotbarSize;
            hotbarRect.anchoredPosition = new Vector2(0, 50);
            
            // Add background image
            Image hotbarImage = hotbarObj.AddComponent<Image>();
            hotbarImage.color = hotbarColor;
            
            // Create hotbar container
            GameObject containerObj = new GameObject("HotbarContainer");
            containerObj.transform.SetParent(hotbarObj.transform, false);
            
            RectTransform containerRect = containerObj.AddComponent<RectTransform>();
            containerRect.anchorMin = Vector2.zero;
            containerRect.anchorMax = Vector2.one;
            containerRect.offsetMin = new Vector2(5, 5);
            containerRect.offsetMax = new Vector2(-5, -5);
            
            // Add HorizontalLayoutGroup
            HorizontalLayoutGroup horizontalLayout = containerObj.AddComponent<HorizontalLayoutGroup>();
            horizontalLayout.spacing = slotSpacing;
            horizontalLayout.childAlignment = TextAnchor.MiddleCenter;
            horizontalLayout.childControlHeight = true;
            horizontalLayout.childControlWidth = true;
            horizontalLayout.childForceExpandHeight = false;
            horizontalLayout.childForceExpandWidth = false;
            
            hotbarPanel = hotbarObj;
        }
        
        private void CreateSectionTitle(GameObject parent, string title, Vector2 anchorPosition)
        {
            GameObject titleObj = new GameObject($"{title}Title");
            titleObj.transform.SetParent(parent.transform, false);
            
            RectTransform titleRect = titleObj.AddComponent<RectTransform>();
            titleRect.anchorMin = anchorPosition;
            titleRect.anchorMax = anchorPosition;
            titleRect.sizeDelta = new Vector2(200, 30);
            titleRect.anchoredPosition = Vector2.zero;
            
            TextMeshProUGUI titleText = titleObj.AddComponent<TextMeshProUGUI>();
            titleText.text = title;
            titleText.fontSize = 18;
            titleText.color = Color.white;
            titleText.alignment = TextAlignmentOptions.Center;
            titleText.fontStyle = FontStyles.Bold;
        }
        
        private void CreateStatusBar(GameObject parent, string name, Vector2 anchorMin, Vector2 anchorMax, Color barColor)
        {
            GameObject barObj = new GameObject(name);
            barObj.transform.SetParent(parent.transform, false);
            
            RectTransform barRect = barObj.AddComponent<RectTransform>();
            barRect.anchorMin = anchorMin;
            barRect.anchorMax = anchorMax;
            barRect.offsetMin = Vector2.zero;
            barRect.offsetMax = Vector2.zero;
            
            Slider slider = barObj.AddComponent<Slider>();
            slider.value = 1f;
            
            // Create background
            GameObject bgObj = new GameObject("Background");
            bgObj.transform.SetParent(barObj.transform, false);
            Image bgImage = bgObj.AddComponent<Image>();
            bgImage.color = Color.gray;
            
            RectTransform bgRect = bgObj.GetComponent<RectTransform>();
            bgRect.anchorMin = Vector2.zero;
            bgRect.anchorMax = Vector2.one;
            bgRect.offsetMin = Vector2.zero;
            bgRect.offsetMax = Vector2.zero;
            
            // Create fill area
            GameObject fillAreaObj = new GameObject("Fill Area");
            fillAreaObj.transform.SetParent(barObj.transform, false);
            
            RectTransform fillAreaRect = fillAreaObj.GetComponent<RectTransform>();
            fillAreaRect.anchorMin = Vector2.zero;
            fillAreaRect.anchorMax = Vector2.one;
            fillAreaRect.offsetMin = Vector2.zero;
            fillAreaRect.offsetMax = Vector2.zero;
            
            // Create fill
            GameObject fillObj = new GameObject("Fill");
            fillObj.transform.SetParent(fillAreaObj.transform, false);
            Image fillImage = fillObj.AddComponent<Image>();
            fillImage.color = barColor;
            
            RectTransform fillRect = fillObj.GetComponent<RectTransform>();
            fillRect.anchorMin = Vector2.zero;
            fillRect.anchorMax = Vector2.one;
            fillRect.offsetMin = Vector2.zero;
            fillRect.offsetMax = Vector2.zero;
            
            // Setup slider references
            slider.targetGraphic = fillImage;
            slider.fillRect = fillRect;
        }
        
        private void CreateInfoText(GameObject parent, string name, Vector2 anchorPosition, string text)
        {
            GameObject textObj = new GameObject(name);
            textObj.transform.SetParent(parent.transform, false);
            
            RectTransform textRect = textObj.AddComponent<RectTransform>();
            textRect.anchorMin = anchorPosition;
            textRect.anchorMax = anchorPosition;
            textRect.sizeDelta = new Vector2(150, 30);
            textRect.anchoredPosition = Vector2.zero;
            
            TextMeshProUGUI textComponent = textObj.AddComponent<TextMeshProUGUI>();
            textComponent.text = text;
            textComponent.fontSize = 14;
            textComponent.color = Color.white;
            textComponent.alignment = TextAlignmentOptions.Center;
        }
        
        private void AddInventoryUIComponent()
        {
            // Add InventoryUI component to canvas
            InventoryUI inventoryUI = canvas.GetComponent<InventoryUI>();
            if (inventoryUI == null)
            {
                inventoryUI = canvas.gameObject.AddComponent<InventoryUI>();
            }
            
            // TODO: Set up references in InventoryUI component
            Debug.Log("Added InventoryUI component - you'll need to assign the UI references manually");
        }
        
        [ContextMenu("Clear Inventory UI")]
        public void ClearInventoryUI()
        {
            if (inventoryPanel != null)
            {
                DestroyImmediate(inventoryPanel);
            }
            
            if (hotbarPanel != null)
            {
                DestroyImmediate(hotbarPanel);
            }
            
            Debug.Log("Cleared inventory UI");
        }
    }
}
