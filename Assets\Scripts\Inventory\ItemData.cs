using UnityEngine;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Base ScriptableObject for all item data. This holds the static information about items.
    /// </summary>
    [CreateAssetMenu(fileName = "New Item", menuName = "Inventory/Item Data")]
    public class ItemData : ScriptableObject
    {
        [Header("Basic Info")]
        public string itemName = "New Item";
        public string itemID = ""; // Unique identifier for networking/saving
        [TextArea(3, 5)]
        public string description = "";
        public Sprite icon;
        
        [Header("Stack Settings")]
        public bool isStackable = true;
        public int maxStackSize = 64;
        
        [Header("Item Properties")]
        public ItemType itemType = ItemType.Consumable;
        public ItemRarity rarity = ItemRarity.Common;
        public float weight = 1f;
        
        [Header("World Representation")]
        public GameObject worldPrefab; // Prefab to spawn when dropped in world
        
        [Header("Audio")]
        public AudioClip pickupSound;
        public AudioClip useSound;
        
        private void OnValidate()
        {
            // Auto-generate ID if empty
            if (string.IsNullOrEmpty(itemID))
            {
                itemID = name.ToLower().Replace(" ", "_");
            }
        }
    }
    
    public enum ItemType
    {
        Consumable,     // Food, medicine, etc.
        Weapon,         // Melee and ranged weapons
        Ammunition,     // Bullets, arrows, etc.
        Material,       // Crafting materials, scrap
        Tool,           // Flashlight, radio, etc.
        Equipment,      // Armor, clothing
        Key,            // Keys, keycards
        Quest           // Quest items
    }
    
    public enum ItemRarity
    {
        Common,
        Uncommon,
        Rare,
        Epic,
        Legendary
    }
}
