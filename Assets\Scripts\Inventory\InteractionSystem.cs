using UnityEngine;
using UnityEngine.InputSystem;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Handles player interactions with world objects, including item pickups
    /// </summary>
    public class InteractionSystem : MonoBehaviour
    {
        [Header("Interaction Settings")]
        [SerializeField] private float interactionRange = 3f;
        [SerializeField] private LayerMask interactableLayer = -1;
        [SerializeField] private KeyCode interactionKey = KeyCode.E;
        
        [Header("UI")]
        [SerializeField] private GameObject interactionPrompt;
        [SerializeField] private TMPro.TextMeshProUGUI promptText;
        
        [Header("Debug")]
        [SerializeField] private bool showDebugRay = true;
        [SerializeField] private bool enableDebugLogs = true;
        
        // Components
        private Camera playerCamera;
        private PlayerInventory playerInventory;
        private InputAction interactAction;
        
        // State
        private IInteractable currentInteractable;
        private WorldItem currentWorldItem;
        
        private void Awake()
        {
            SetupComponents();
            SetupInput();
        }
        
        private void SetupComponents()
        {
            // Get player camera
            playerCamera = Camera.main;
            if (playerCamera == null)
            {
                playerCamera = FindFirstObjectByType<Camera>();
            }
            
            // Get player inventory
            playerInventory = GetComponent<PlayerInventory>();
            if (playerInventory == null)
            {
                Debug.LogWarning("InteractionSystem: No PlayerInventory found on this GameObject!");
            }
            
            // Setup interaction prompt
            if (interactionPrompt != null)
            {
                interactionPrompt.SetActive(false);
            }
        }
        
        private void SetupInput()
        {
            // Create input action for interaction
            interactAction = new InputAction(binding: $"<Keyboard>/{interactionKey.ToString().ToLower()}");
            interactAction.AddBinding("<Gamepad>/buttonWest"); // X button on Xbox controller
            
            interactAction.performed += OnInteract;
            interactAction.Enable();
        }
        
        private void Update()
        {
            CheckForInteractables();
        }
        
        private void CheckForInteractables()
        {
            if (playerCamera == null) return;
            
            // Cast ray from camera center
            Ray ray = playerCamera.ScreenPointToRay(new Vector3(Screen.width / 2, Screen.height / 2, 0));
            
            if (showDebugRay)
            {
                Debug.DrawRay(ray.origin, ray.direction * interactionRange, Color.yellow);
            }
            
            RaycastHit hit;
            bool foundInteractable = false;
            
            if (Physics.Raycast(ray, out hit, interactionRange, interactableLayer))
            {
                if (enableDebugLogs)
                {
                    Debug.Log($"Raycast hit: {hit.collider.name} at distance {hit.distance}");
                }

                // Check for WorldItem
                WorldItem worldItem = hit.collider.GetComponent<WorldItem>();
                if (worldItem != null && worldItem.CanBePickedUp)
                {
                    if (enableDebugLogs)
                    {
                        Debug.Log($"Found WorldItem: {worldItem.Item?.ItemData?.itemName}");
                    }
                    SetCurrentWorldItem(worldItem);
                    foundInteractable = true;
                }
                else
                {
                    // Check for other interactables
                    IInteractable interactable = hit.collider.GetComponent<IInteractable>();
                    if (interactable != null && interactable.CanInteract())
                    {
                        SetCurrentInteractable(interactable);
                        foundInteractable = true;
                    }
                    else if (enableDebugLogs)
                    {
                        Debug.Log($"Hit object {hit.collider.name} but no WorldItem or IInteractable found");
                    }
                }
            }
            else if (enableDebugLogs && Time.frameCount % 60 == 0) // Log every 60 frames to avoid spam
            {
                Debug.Log("No raycast hits detected");
            }
            
            // Clear current interactable if nothing found
            if (!foundInteractable)
            {
                ClearCurrentInteractable();
            }
        }
        
        private void SetCurrentWorldItem(WorldItem worldItem)
        {
            if (currentWorldItem != worldItem)
            {
                currentWorldItem = worldItem;
                currentInteractable = null;
                
                UpdateInteractionPrompt($"Pick up {worldItem.Item.ItemData.itemName}");
            }
        }
        
        private void SetCurrentInteractable(IInteractable interactable)
        {
            if (currentInteractable != interactable)
            {
                currentInteractable = interactable;
                currentWorldItem = null;
                
                UpdateInteractionPrompt(interactable.GetInteractionText());
            }
        }
        
        private void ClearCurrentInteractable()
        {
            if (currentInteractable != null || currentWorldItem != null)
            {
                currentInteractable = null;
                currentWorldItem = null;
                
                HideInteractionPrompt();
            }
        }
        
        private void UpdateInteractionPrompt(string text)
        {
            if (interactionPrompt != null)
            {
                interactionPrompt.SetActive(true);
                
                if (promptText != null)
                {
                    promptText.text = $"[{interactionKey}] {text}";
                }
            }
        }
        
        private void HideInteractionPrompt()
        {
            if (interactionPrompt != null)
            {
                interactionPrompt.SetActive(false);
            }
        }
        
        private void OnInteract(InputAction.CallbackContext context)
        {
            if (currentWorldItem != null)
            {
                // Try to pick up the world item
                if (playerInventory != null)
                {
                    bool success = currentWorldItem.TryPickup();
                    if (success)
                    {
                        ClearCurrentInteractable();
                    }
                }
            }
            else if (currentInteractable != null)
            {
                // Interact with the object
                currentInteractable.Interact(gameObject);
            }
        }
        
        private void OnEnable()
        {
            if (interactAction != null)
            {
                interactAction.Enable();
            }
        }
        
        private void OnDisable()
        {
            if (interactAction != null)
            {
                interactAction.Disable();
            }
        }
        
        private void OnDestroy()
        {
            if (interactAction != null)
            {
                interactAction.Dispose();
            }
        }
        
        // Public methods for external use
        public bool HasCurrentInteractable => currentInteractable != null || currentWorldItem != null;
        public string GetCurrentInteractionText()
        {
            if (currentWorldItem != null)
                return $"Pick up {currentWorldItem.Item.ItemData.itemName}";
            if (currentInteractable != null)
                return currentInteractable.GetInteractionText();
            return "";
        }
    }
    
    /// <summary>
    /// Interface for objects that can be interacted with
    /// </summary>
    public interface IInteractable
    {
        bool CanInteract();
        void Interact(GameObject interactor);
        string GetInteractionText();
    }
}
