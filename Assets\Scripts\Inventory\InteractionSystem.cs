using UnityEngine;
using UnityEngine.InputSystem;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Handles player interactions with world objects, including item pickups
    /// </summary>
    public class InteractionSystem : MonoBehaviour
    {
        [Header("Interaction Settings")]
        [SerializeField] private float interactionRange = 3f;
        [SerializeField] private LayerMask interactableLayer = -1;
        [SerializeField] private LayerMask ignoreLayer = 0; // Layers to ignore (like player)
        [SerializeField] private KeyCode interactionKey = KeyCode.E;
        
        [Header("UI")]
        [SerializeField] private GameObject interactionPrompt;
        [SerializeField] private TMPro.TextMeshProUGUI promptText;
        [SerializeField] private bool createDynamicUI = true; // Auto-create UI if not assigned
        
        [Header("Debug")]
        [SerializeField] private bool showDebugRay = true;
        [SerializeField] private bool enableDebugLogs = true;
        
        // Components
        private Camera playerCamera;
        private PlayerInventory playerInventory;
        private InputAction interactAction;
        
        // State
        private IInteractable currentInteractable;
        private WorldItem currentWorldItem;
        
        private void Awake()
        {
            SetupComponents();
            SetupInput();
        }
        
        private void SetupComponents()
        {
            // Get player camera
            playerCamera = Camera.main;
            if (playerCamera == null)
            {
                playerCamera = FindFirstObjectByType<Camera>();
            }
            
            // Get player inventory
            playerInventory = GetComponent<PlayerInventory>();
            if (playerInventory == null)
            {
                Debug.LogWarning("InteractionSystem: No PlayerInventory found on this GameObject!");
            }
            
            // Setup interaction prompt
            if (interactionPrompt != null)
            {
                interactionPrompt.SetActive(false);
            }
            else if (createDynamicUI)
            {
                CreateDynamicInteractionUI();
            }
        }
        
        private void SetupInput()
        {
            // Create input action for interaction
            interactAction = new InputAction(binding: $"<Keyboard>/{interactionKey.ToString().ToLower()}");
            interactAction.AddBinding("<Gamepad>/buttonWest"); // X button on Xbox controller
            
            interactAction.performed += OnInteract;
            interactAction.Enable();
        }
        
        private void Update()
        {
            CheckForInteractables();
        }
        
        private void CheckForInteractables()
        {
            if (playerCamera == null) return;

            // Cast ray from camera center, but start it a bit forward to avoid hitting the player
            Ray ray = playerCamera.ScreenPointToRay(new Vector3(Screen.width / 2, Screen.height / 2, 0));
            Vector3 rayStart = ray.origin + ray.direction * 0.5f; // Start 0.5 units forward

            if (showDebugRay)
            {
                Debug.DrawRay(rayStart, ray.direction * interactionRange, Color.yellow);
            }
            
            RaycastHit hit;
            bool foundInteractable = false;
            
            // Create a layer mask that includes interactableLayer but excludes ignoreLayer
            LayerMask finalLayerMask = interactableLayer & ~ignoreLayer;

            if (Physics.Raycast(rayStart, ray.direction, out hit, interactionRange, finalLayerMask))
            {
                if (enableDebugLogs)
                {
                    Debug.Log($"Raycast hit: {hit.collider.name} at distance {hit.distance}");
                }

                // Check for WorldItem
                WorldItem worldItem = hit.collider.GetComponent<WorldItem>();
                if (worldItem != null && worldItem.CanBePickedUp && worldItem.gameObject.activeInHierarchy)
                {
                    if (enableDebugLogs)
                    {
                        Debug.Log($"Found WorldItem: {worldItem.Item?.ItemData?.itemName}");
                    }
                    SetCurrentWorldItem(worldItem);
                    foundInteractable = true;
                }
                else
                {
                    // Check for other interactables
                    IInteractable interactable = hit.collider.GetComponent<IInteractable>();
                    if (interactable != null && interactable.CanInteract())
                    {
                        SetCurrentInteractable(interactable);
                        foundInteractable = true;
                    }
                    else if (enableDebugLogs)
                    {
                        Debug.Log($"Hit object {hit.collider.name} but no WorldItem or IInteractable found");
                    }
                }
            }
            else if (enableDebugLogs && Time.frameCount % 60 == 0) // Log every 60 frames to avoid spam
            {
                Debug.Log("No raycast hits detected");
            }
            
            // Clear current interactable if nothing found
            if (!foundInteractable)
            {
                ClearCurrentInteractable();
            }
        }
        
        private void SetCurrentWorldItem(WorldItem worldItem)
        {
            if (currentWorldItem != worldItem)
            {
                currentWorldItem = worldItem;
                currentInteractable = null;
                
                if (enableDebugLogs)
                {
                    Debug.Log($"Set current WorldItem: {worldItem.Item?.ItemData?.itemName}. Press [{interactionKey}] to pick up!");
                }
                
                UpdateInteractionPrompt($"Pick up {worldItem.Item.ItemData.itemName}");
            }
        }
        
        private void SetCurrentInteractable(IInteractable interactable)
        {
            if (currentInteractable != interactable)
            {
                currentInteractable = interactable;
                currentWorldItem = null;
                
                UpdateInteractionPrompt(interactable.GetInteractionText());
            }
        }
        
        private void ClearCurrentInteractable()
        {
            if (currentInteractable != null || currentWorldItem != null)
            {
                currentInteractable = null;
                currentWorldItem = null;
                
                HideInteractionPrompt();
            }
        }
        
        private void UpdateInteractionPrompt(string text)
        {
            if (interactionPrompt != null)
            {
                interactionPrompt.SetActive(true);
                
                if (promptText != null)
                {
                    promptText.text = $"[{interactionKey}] {text}";
                }
            }
            else if (enableDebugLogs)
            {
                // Show in console if UI is not set up
                Debug.Log($"INTERACTION PROMPT: [{interactionKey}] {text}");
            }
        }
        
        private void HideInteractionPrompt()
        {
            if (interactionPrompt != null)
            {
                interactionPrompt.SetActive(false);
            }
        }
        
        private void OnInteract(InputAction.CallbackContext context)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"Interaction key pressed! Current WorldItem: {currentWorldItem?.name}, Current Interactable: {currentInteractable}");
            }
            
            if (currentWorldItem != null)
            {
                if (enableDebugLogs)
                {
                    Debug.Log($"Attempting to pickup WorldItem: {currentWorldItem.Item?.ItemData?.itemName}");
                }
                
                // Check if we have PlayerInventory
                if (playerInventory == null)
                {
                    Debug.LogError("InteractionSystem: PlayerInventory is null! Cannot pickup items.");
                    
                    // Try to find PlayerInventory on this GameObject again
                    playerInventory = GetComponent<PlayerInventory>();
                    if (playerInventory == null)
                    {
                        Debug.LogError("Still no PlayerInventory found! Make sure PlayerInventory component is attached to the same GameObject as InteractionSystem.");
                        return;
                    }
                    else
                    {
                        Debug.Log("Found PlayerInventory component!");
                    }
                }
                
                // Debug the item and inventory state
                if (enableDebugLogs)
                {
                    Debug.Log($"PlayerInventory found: {playerInventory != null}");
                    Debug.Log($"WorldItem.Item: {currentWorldItem.Item != null}");
                    Debug.Log($"WorldItem.CanBePickedUp: {currentWorldItem.CanBePickedUp}");
                    if (currentWorldItem.Item?.ItemData != null)
                    {
                        Debug.Log($"Item details - Name: {currentWorldItem.Item.ItemData.itemName}, ID: {currentWorldItem.Item.ItemData.itemID}, Quantity: {currentWorldItem.Item.Quantity}");
                    }
                    
                    // Check inventory space
                    Debug.Log($"Backpack slots: {playerInventory.Backpack?.SlotCount}, Hotbar slots: {playerInventory.Hotbar?.SlotCount}");
                }
                
                // Try to pick up the world item
                bool success = currentWorldItem.TryPickup(playerInventory);
                if (enableDebugLogs)
                {
                    Debug.Log($"WorldItem pickup result: {success}");
                }
                
                if (success)
                {
                    Debug.Log($"SUCCESS! Item '{currentWorldItem.Item?.ItemData?.itemName}' was added to inventory!");
                    
                    // Force clear the interaction immediately since item was picked up
                    WorldItem pickedUpItem = currentWorldItem;
                    currentWorldItem = null;
                    currentInteractable = null;
                    HideInteractionPrompt();
                    
                    // Mark the WorldItem as picked up to prevent Update from re-detecting it
                    if (pickedUpItem != null)
                    {
                        pickedUpItem.gameObject.SetActive(false);
                    }
                    
                    // Refresh inventory UI if it exists
                    RefreshInventoryUI();
                }
                else
                {
                    Debug.LogWarning($"FAILED to pickup WorldItem '{currentWorldItem.Item?.ItemData?.itemName}' - Possible reasons:");
                    Debug.LogWarning("1. Inventory is full");
                    Debug.LogWarning("2. Item data is invalid");
                    Debug.LogWarning("3. PlayerInventory.PickupItem() returned false");
                    
                    // Additional debugging
                    if (playerInventory != null && currentWorldItem.Item != null)
                    {
                        Debug.LogWarning($"Trying to manually add item to see what happens...");
                        bool manualResult = playerInventory.PickupItem(currentWorldItem.Item);
                        Debug.LogWarning($"Manual pickup result: {manualResult}");
                    }
                }
            }
            else if (currentInteractable != null)
            {
                // Interact with the object
                currentInteractable.Interact(gameObject);
            }
            else if (enableDebugLogs)
            {
                Debug.Log("Interaction key pressed but no interactable object found");
            }
        }
        
        private void OnEnable()
        {
            if (interactAction != null)
            {
                interactAction.Enable();
            }
        }
        
        private void OnDisable()
        {
            if (interactAction != null)
            {
                interactAction.Disable();
            }
        }
        
        private void OnDestroy()
        {
            if (interactAction != null)
            {
                interactAction.Dispose();
            }
        }
        
        private void CreateDynamicInteractionUI()
        {
            // Find or create canvas
            Canvas canvas = FindFirstObjectByType<Canvas>();
            if (canvas == null)
            {
                GameObject canvasObj = new GameObject("InteractionCanvas");
                canvas = canvasObj.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvas.sortingOrder = 999; // High priority
                
                UnityEngine.UI.CanvasScaler scaler = canvasObj.AddComponent<UnityEngine.UI.CanvasScaler>();
                scaler.uiScaleMode = UnityEngine.UI.CanvasScaler.ScaleMode.ScaleWithScreenSize;
                scaler.referenceResolution = new Vector2(1920, 1080);
                
                canvasObj.AddComponent<UnityEngine.UI.GraphicRaycaster>();
            }
            
            // Create interaction prompt panel
            interactionPrompt = new GameObject("InteractionPrompt");
            interactionPrompt.transform.SetParent(canvas.transform, false);
            
            RectTransform promptRect = interactionPrompt.AddComponent<RectTransform>();
            promptRect.anchorMin = new Vector2(0.5f, 0.3f);
            promptRect.anchorMax = new Vector2(0.5f, 0.3f);
            promptRect.sizeDelta = new Vector2(400, 80);
            promptRect.anchoredPosition = Vector2.zero;
            
            // Add background panel
            UnityEngine.UI.Image bgImage = interactionPrompt.AddComponent<UnityEngine.UI.Image>();
            bgImage.color = new Color(0f, 0f, 0f, 0.8f); // Semi-transparent black
            
            // Create text element
            GameObject textObj = new GameObject("PromptText");
            textObj.transform.SetParent(interactionPrompt.transform, false);
            
            RectTransform textRect = textObj.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = new Vector2(10, 10);
            textRect.offsetMax = new Vector2(-10, -10);
            
            promptText = textObj.AddComponent<TMPro.TextMeshProUGUI>();
            promptText.text = "[E] Interact";
            promptText.fontSize = 24;
            promptText.color = Color.white;
            promptText.alignment = TMPro.TextAlignmentOptions.Center;
            promptText.fontStyle = TMPro.FontStyles.Bold;
            
            // Start hidden
            interactionPrompt.SetActive(false);
            
            if (enableDebugLogs)
            {
                Debug.Log("Created dynamic interaction UI");
            }
        }
        
        /// <summary>
        /// Refreshes the inventory UI to show newly picked up items
        /// </summary>
        private void RefreshInventoryUI()
        {
            // Find and setup inventory UI if it's not already set up
            var simpleInventoryUI = FindFirstObjectByType<ZombieGame.UI.SimpleInventoryUI>();
            if (simpleInventoryUI != null)
            {
                simpleInventoryUI.SetupUI();
                Debug.Log("Refreshed SimpleInventoryUI after item pickup");
            }
            
            Debug.Log("Inventory UI refresh completed");
        }

        // Public methods for external use
        public bool HasCurrentInteractable => currentInteractable != null || currentWorldItem != null;
        public string GetCurrentInteractionText()
        {
            if (currentWorldItem != null)
                return $"Pick up {currentWorldItem.Item.ItemData.itemName}";
            if (currentInteractable != null)
                return currentInteractable.GetInteractionText();
            return "";
        }
    }
    
    /// <summary>
    /// Interface for objects that can be interacted with
    /// </summary>
    public interface IInteractable
    {
        bool CanInteract();
        void Interact(GameObject interactor);
        string GetInteractionText();
    }
}
