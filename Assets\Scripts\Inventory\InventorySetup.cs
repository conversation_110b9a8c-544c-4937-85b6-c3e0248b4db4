using UnityEngine;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Helper script to automatically set up the inventory system on a player GameObject
    /// </summary>
    [System.Serializable]
    public class InventorySetup : MonoBehaviour
    {
        [Header("Auto Setup")]
        [SerializeField] private bool autoSetupOnStart = true;
        [SerializeField] private bool createSampleItems = true;
        
        [Head<PERSON>("Components to Add")]
        [SerializeField] private bool addPlayerInventory = true;
        [SerializeField] private bool addInputController = true;
        [SerializeField] private bool addInteractionSystem = true;
        
        [Header("Sample Items")]
        [SerializeField] private string[] sampleItemIDs = {
            "bandage", "water_bottle", "canned_food", "flashlight", "knife"
        };
        
        private void Start()
        {
            if (autoSetupOnStart)
            {
                SetupInventorySystem();
            }
        }
        
        [ContextMenu("Setup Inventory System")]
        public void SetupInventorySystem()
        {
            Debug.Log("Setting up inventory system...");
            
            // Add PlayerInventory component
            if (addPlayerInventory)
            {
                PlayerInventory playerInventory = GetComponent<PlayerInventory>();
                if (playerInventory == null)
                {
                    playerInventory = gameObject.AddComponent<PlayerInventory>();
                    Debug.Log("Added PlayerInventory component");
                }
            }
            
            // Add InventoryInputController component
            if (addInputController)
            {
                InventoryInputController inputController = GetComponent<InventoryInputController>();
                if (inputController == null)
                {
                    inputController = gameObject.AddComponent<InventoryInputController>();
                    Debug.Log("Added InventoryInputController component");
                }
            }
            
            // Add InteractionSystem component
            if (addInteractionSystem)
            {
                InteractionSystem interactionSystem = GetComponent<InteractionSystem>();
                if (interactionSystem == null)
                {
                    interactionSystem = gameObject.AddComponent<InteractionSystem>();
                    Debug.Log("Added InteractionSystem component");
                }
            }
            
            // Create sample items if requested
            if (createSampleItems)
            {
                CreateSampleItems();
            }
            
            Debug.Log("Inventory system setup complete!");
        }
        
        private void CreateSampleItems()
        {
            PlayerInventory playerInventory = GetComponent<PlayerInventory>();
            if (playerInventory == null)
            {
                Debug.LogWarning("Cannot create sample items: No PlayerInventory component found");
                return;
            }
            
            Debug.Log("Creating sample items...");
            
            foreach (string itemID in sampleItemIDs)
            {
                var item = ItemDatabase.CreateItem(itemID, 1);
                if (item != null)
                {
                    bool success = playerInventory.PickupItem(item);
                    if (success)
                    {
                        Debug.Log($"Added sample item: {item.ItemData.itemName}");
                    }
                    else
                    {
                        Debug.LogWarning($"Failed to add sample item: {itemID}");
                    }
                }
                else
                {
                    Debug.LogWarning($"Sample item not found in database: {itemID}");
                }
            }
        }
        
        [ContextMenu("Create Sample World Items")]
        public void CreateSampleWorldItems()
        {
            Vector3 playerPosition = transform.position;
            
            for (int i = 0; i < sampleItemIDs.Length; i++)
            {
                var item = ItemDatabase.CreateItem(sampleItemIDs[i], Random.Range(1, 5));
                if (item != null)
                {
                    Vector3 spawnPosition = playerPosition + new Vector3(
                        Random.Range(-5f, 5f),
                        1f,
                        Random.Range(-5f, 5f)
                    );
                    
                    WorldItem.SpawnWorldItem(item, spawnPosition);
                    Debug.Log($"Spawned world item: {item.ItemData.itemName} at {spawnPosition}");
                }
            }
        }
        
        [ContextMenu("Remove Inventory System")]
        public void RemoveInventorySystem()
        {
            // Remove components
            PlayerInventory playerInventory = GetComponent<PlayerInventory>();
            if (playerInventory != null)
            {
                DestroyImmediate(playerInventory);
                Debug.Log("Removed PlayerInventory component");
            }
            
            InventoryInputController inputController = GetComponent<InventoryInputController>();
            if (inputController != null)
            {
                DestroyImmediate(inputController);
                Debug.Log("Removed InventoryInputController component");
            }
            
            InteractionSystem interactionSystem = GetComponent<InteractionSystem>();
            if (interactionSystem != null)
            {
                DestroyImmediate(interactionSystem);
                Debug.Log("Removed InteractionSystem component");
            }
            
            Debug.Log("Inventory system removed!");
        }
        
        /// <summary>
        /// Creates the ItemDatabase asset if it doesn't exist
        /// </summary>
        [ContextMenu("Create Item Database")]
        public void CreateItemDatabase()
        {
            #if UNITY_EDITOR
            // Check if database already exists
            ItemDatabase existingDB = Resources.Load<ItemDatabase>("ItemDatabase");
            if (existingDB != null)
            {
                Debug.Log("ItemDatabase already exists in Resources folder");
                return;
            }
            
            // Create Resources folder if it doesn't exist
            string resourcesPath = "Assets/Resources";
            if (!UnityEditor.AssetDatabase.IsValidFolder(resourcesPath))
            {
                UnityEditor.AssetDatabase.CreateFolder("Assets", "Resources");
            }
            
            // Create the database
            ItemDatabase database = ScriptableObject.CreateInstance<ItemDatabase>();
            UnityEditor.AssetDatabase.CreateAsset(database, "Assets/Resources/ItemDatabase.asset");
            UnityEditor.AssetDatabase.SaveAssets();
            
            Debug.Log("Created ItemDatabase at Assets/Resources/ItemDatabase.asset");
            Debug.Log("You can now create ItemData assets and add them to the database!");
            #else
            Debug.LogWarning("CreateItemDatabase can only be used in the Unity Editor");
            #endif
        }
        
        /// <summary>
        /// Creates sample item data assets
        /// </summary>
        [ContextMenu("Create Sample Item Data")]
        public void CreateSampleItemData()
        {
            #if UNITY_EDITOR
            string itemsPath = "Assets/Databases/Items";
            
            // Create directories if they don't exist
            if (!UnityEditor.AssetDatabase.IsValidFolder("Assets/Databases"))
            {
                UnityEditor.AssetDatabase.CreateFolder("Assets", "Databases");
            }
            if (!UnityEditor.AssetDatabase.IsValidFolder(itemsPath))
            {
                UnityEditor.AssetDatabase.CreateFolder("Assets/Databases", "Items");
            }
            
            // Create sample consumable
            ConsumableItemData bandage = ScriptableObject.CreateInstance<ConsumableItemData>();
            bandage.itemName = "Bandage";
            bandage.itemID = "bandage";
            bandage.description = "A medical bandage that restores health.";
            bandage.healthRestore = 25f;
            bandage.maxStackSize = 10;
            UnityEditor.AssetDatabase.CreateAsset(bandage, $"{itemsPath}/Bandage.asset");
            
            // Create sample material
            MaterialItemData scrap = ScriptableObject.CreateInstance<MaterialItemData>();
            scrap.itemName = "Metal Scrap";
            scrap.itemID = "metal_scrap";
            scrap.description = "Useful metal scraps for crafting.";
            scrap.category = MaterialCategory.Scrap;
            scrap.maxStackSize = 50;
            UnityEditor.AssetDatabase.CreateAsset(scrap, $"{itemsPath}/MetalScrap.asset");
            
            // Create sample weapon
            WeaponItemData knife = ScriptableObject.CreateInstance<WeaponItemData>();
            knife.itemName = "Combat Knife";
            knife.itemID = "knife";
            knife.description = "A sharp combat knife for close combat.";
            knife.weaponType = WeaponType.Melee;
            knife.damage = 35f;
            knife.attackSpeed = 1.5f;
            knife.range = 1.5f;
            UnityEditor.AssetDatabase.CreateAsset(knife, $"{itemsPath}/CombatKnife.asset");
            
            UnityEditor.AssetDatabase.SaveAssets();
            Debug.Log("Created sample item data assets in Assets/Databases/Items/");
            Debug.Log("Remember to add these items to your ItemDatabase!");
            #else
            Debug.LogWarning("CreateSampleItemData can only be used in the Unity Editor");
            #endif
        }
    }
}
