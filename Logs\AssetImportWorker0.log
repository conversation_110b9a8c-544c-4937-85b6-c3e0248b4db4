Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.1f1 (7197418f847b) revision 7444289'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 32555 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-06-30T17:35:59Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.1f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
F:/Prototype Running Game
-logFile
Logs/AssetImportWorker0.log
-srvPort
58502
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: F:/Prototype Running Game
F:/Prototype Running Game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [25832]  Target information:

Player connection [25832]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 414970 [EditorId] 414970 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-RJGOOJ7) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [25832] Host joined multi-casting on [***********:54997]...
Player connection [25832] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 3.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.1f1 (7197418f847b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Prototype Running Game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce GTX 1660 SUPER (ID=0x21c4)
    Vendor:          NVIDIA
    VRAM:            5966 MB
    App VRAM Budget: 5198 MB
    Driver:          32.0.15.7680
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56924
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002668 seconds.
- Loaded All Assemblies, in  0.510 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.432 seconds
Domain Reload Profiling: 940ms
	BeginReloadAssembly (177ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (64ms)
	LoadAllAssembliesAndSetupDomain (204ms)
		LoadAssemblies (174ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (200ms)
			TypeCache.Refresh (198ms)
				TypeCache.ScanAssembly (184ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (432ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (371ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (59ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (166ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.993 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.72 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
System.InvalidOperationException: The operation is not possible when moved past all properties (Next returned false)
  at UnityEditor.SerializedProperty.get_stringValue () [0x00013] in <a4c13c6b048543258b1eafe39cc26dc5>:0 
  at DevionGames.WriteInputManager.AxisDefined (System.String axisName) [0x0003c] in F:\Prototype Running Game\Assets\Devion Games\Third Person Controller\Scripts\Editor\WriteInputManager.cs:84 
  at DevionGames.WriteInputManager..cctor () [0x00000] in F:\Prototype Running Game\Assets\Devion Games\Third Person Controller\Scripts\Editor\WriteInputManager.cs:13 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.133 seconds
Domain Reload Profiling: 2120ms
	BeginReloadAssembly (192ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (688ms)
		LoadAssemblies (478ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (321ms)
			TypeCache.Refresh (242ms)
				TypeCache.ScanAssembly (223ms)
			BuildScriptInfoCaches (59ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1133ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (882ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (144ms)
			ProcessInitializeOnLoadAttributes (654ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 1.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 229 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6829 unused Assets / (5.2 MB). Loaded Objects now: 7549.
Memory consumption went from 175.7 MB to 170.5 MB.
Total: 13.164900 ms (FindLiveObjects: 1.162500 ms CreateObjectMapping: 1.655000 ms MarkObjects: 7.045700 ms  DeleteObjects: 3.299700 ms)

========================================================================
Received Import Request.
  Time since last request: 31522.832483 seconds.
  path: Assets/Devion Games
  artifactKey: Guid(26548b3aabeb2384184f0f03a83a776e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games using Guid(26548b3aabeb2384184f0f03a83a776e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c470d569ad2edf1f5708fd932eb57790') in 0.008735 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.816707 seconds.
  path: Assets/Devion Games/Flat GUI
  artifactKey: Guid(850a2705c4621b94085d608465f64498) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games/Flat GUI using Guid(850a2705c4621b94085d608465f64498) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bfcce2c4d3ccc7fb8edffbcdf009d0b8') in 0.0005782 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 94.233539 seconds.
  path: Assets/Databases
  artifactKey: Guid(cee95b0c90bf5684196494d8107777cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Databases using Guid(cee95b0c90bf5684196494d8107777cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '185c155eb9a80e135f3cc3a362f10c99') in 0.000886 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 115.342668 seconds.
  path: Assets/Devion Games/Inventory System
  artifactKey: Guid(2da9b7373564cc0448355df8fc246dbd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games/Inventory System using Guid(2da9b7373564cc0448355df8fc246dbd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '089b749f07b269a1cc88ca3307cb9b30') in 0.000638 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 28.439675 seconds.
  path: Assets/Devion Games/UI Widgets
  artifactKey: Guid(17c57564f60ca4b43b5320aacda9c6d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games/UI Widgets using Guid(17c57564f60ca4b43b5320aacda9c6d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a39e0305234600520a653b4ef63bd344') in 0.0006625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.616696 seconds.
  path: Assets/Devion Games/UI Widgets/Example
  artifactKey: Guid(27c1951820ee2b44aaa0efde50aaea3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games/UI Widgets/Example using Guid(27c1951820ee2b44aaa0efde50aaea3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '40b9875a41aa0829859a908580459f14') in 0.0006355 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.198287 seconds.
  path: Assets/Devion Games/UI Widgets/Example/Prefabs
  artifactKey: Guid(b08a45dbf439d864cae149e80cfd3da3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games/UI Widgets/Example/Prefabs using Guid(b08a45dbf439d864cae149e80cfd3da3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2a5e56b86db90c6fc6bafc1b5ff1b20a') in 0.0007685 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 15.791407 seconds.
  path: Assets/Devion Games/Inventory System/com.deviongames.inventory.json
  artifactKey: Guid(2a6341fed626aaa4584bd8c086182e30) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games/Inventory System/com.deviongames.inventory.json using Guid(2a6341fed626aaa4584bd8c086182e30) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '764b11495a160321bbf403c4158e5271') in 0.0223969 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 10.127653 seconds.
  path: Assets/Devion Games/Module Manager
  artifactKey: Guid(03a2efc88fc3d494c964f5ecc4363ead) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games/Module Manager using Guid(03a2efc88fc3d494c964f5ecc4363ead) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8045d307b844a0a6831965e575e8d264') in 0.000797 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2.996138 seconds.
  path: Assets/Devion Games/Inventory System/Examples
  artifactKey: Guid(f7f55d308aa627148bc084db854c86b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games/Inventory System/Examples using Guid(f7f55d308aa627148bc084db854c86b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a11dc12d1eddebe79b4ae919134fa882') in 0.0007669 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 5.043054 seconds.
  path: Assets/Devion Games/Inventory System/Examples/UI
  artifactKey: Guid(00acfe37b8f5f1b42b55e8bfe401a842) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games/Inventory System/Examples/UI using Guid(00acfe37b8f5f1b42b55e8bfe401a842) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e215f3d2d063994fc96022edd5076444') in 0.0006246 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 72.725945 seconds.
  path: Assets/Devion Games/Inventory System/Scripts
  artifactKey: Guid(ef9cef0db4190f549906d0ab0310a4d0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games/Inventory System/Scripts using Guid(ef9cef0db4190f549906d0ab0310a4d0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cddaf6f6a057882583e7b370251d43bd') in 0.0009046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 11.159066 seconds.
  path: Assets/Devion Games/Inventory System/Examples/Presets
  artifactKey: Guid(7601a7f5c347b274fa71c6da442e74ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games/Inventory System/Examples/Presets using Guid(7601a7f5c347b274fa71c6da442e74ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4dfe0c4e2bf9f18622a2a85389053c8c') in 0.000763 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.737867 seconds.
  path: Assets/Devion Games/Inventory System/Examples/Prefabs
  artifactKey: Guid(241bebaa28ae72a4dad31ef76a6d2415) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games/Inventory System/Examples/Prefabs using Guid(241bebaa28ae72a4dad31ef76a6d2415) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa5152371805ff85748c67c0f8e300f5') in 0.0008137 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 4.517337 seconds.
  path: Assets/Devion Games/Inventory System/Examples/Prefabs/UI
  artifactKey: Guid(3746b094930ad7444b5fe79149b0f158) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games/Inventory System/Examples/Prefabs/UI using Guid(3746b094930ad7444b5fe79149b0f158) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eac32ab7c4758bb887fda1a24398082e') in 0.0005445 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2.310640 seconds.
  path: Assets/Devion Games/Inventory System/Examples/Prefabs/UI/Genaral UI.prefab
  artifactKey: Guid(7711700c499322241ad0a6161fccb38f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games/Inventory System/Examples/Prefabs/UI/Genaral UI.prefab using Guid(7711700c499322241ad0a6161fccb38f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3582e0425390575cb2b5bfd5ee1282cd') in 0.0506052 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 382

========================================================================
Received Import Request.
  Time since last request: 1.399634 seconds.
  path: Assets/Devion Games/Inventory System/Examples/Prefabs/UI/Inventory.prefab
  artifactKey: Guid(73000691bb97ca545a8d09fd0c6f5865) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Devion Games/Inventory System/Examples/Prefabs/UI/Inventory.prefab using Guid(73000691bb97ca545a8d09fd0c6f5865) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8cd19929b885eccbfccbee2a8eee9769') in 0.0670138 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 603

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.841 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
System.InvalidOperationException: The operation is not possible when moved past all properties (Next returned false)
  at UnityEditor.SerializedProperty.get_stringValue () [0x00013] in <a4c13c6b048543258b1eafe39cc26dc5>:0 
  at DevionGames.WriteInputManager.AxisDefined (System.String axisName) [0x0003c] in F:\Prototype Running Game\Assets\Devion Games\Third Person Controller\Scripts\Editor\WriteInputManager.cs:84 
  at DevionGames.WriteInputManager..cctor () [0x00000] in F:\Prototype Running Game\Assets\Devion Games\Third Person Controller\Scripts\Editor\WriteInputManager.cs:13 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.023 seconds
Domain Reload Profiling: 1866ms
	BeginReloadAssembly (219ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (535ms)
		LoadAssemblies (432ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (207ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1024ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (787ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (133ms)
			ProcessInitializeOnLoadAttributes (575ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 1.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6828 unused Assets / (5.0 MB). Loaded Objects now: 7569.
Memory consumption went from 149.5 MB to 144.5 MB.
Total: 13.135000 ms (FindLiveObjects: 1.074900 ms CreateObjectMapping: 1.459100 ms MarkObjects: 6.839200 ms  DeleteObjects: 3.760100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.50 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6366 unused Assets / (4.8 MB). Loaded Objects now: 7117.
Memory consumption went from 149.0 MB to 144.2 MB.
Total: 11.812300 ms (FindLiveObjects: 0.885300 ms CreateObjectMapping: 1.146000 ms MarkObjects: 6.492700 ms  DeleteObjects: 3.285600 ms)

Prepare: number of updated asset objects reloaded= 6
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.860 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.079 seconds
Domain Reload Profiling: 1941ms
	BeginReloadAssembly (228ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (541ms)
		LoadAssemblies (434ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (217ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (185ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1080ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (851ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (156ms)
			ProcessInitializeOnLoadAttributes (611ms)
			ProcessInitializeOnLoadMethodAttributes (66ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6373 unused Assets / (4.6 MB). Loaded Objects now: 7113.
Memory consumption went from 150.0 MB to 145.4 MB.
Total: 10.324200 ms (FindLiveObjects: 0.891900 ms CreateObjectMapping: 0.741300 ms MarkObjects: 5.920200 ms  DeleteObjects: 2.769700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 304.179371 seconds.
  path: Assets/Scripts/Player/Inventory
  artifactKey: Guid(4ac2946dfd1894f4fbd02073984b3ff0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Player/Inventory using Guid(4ac2946dfd1894f4fbd02073984b3ff0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '23dc188561edbc28659ffffa0a32a6e4') in 0.0022301 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

