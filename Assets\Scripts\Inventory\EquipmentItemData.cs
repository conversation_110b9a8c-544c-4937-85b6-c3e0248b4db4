using UnityEngine;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Data for equipment items like armor, accessories, and wearable gear
    /// </summary>
    [CreateAssetMenu(fileName = "New Equipment", menuName = "Inventory/Equipment Item")]
    public class EquipmentItemData : ItemData
    {
        [Header("Equipment Properties")]
        public EquipmentSlot equipmentSlot = EquipmentSlot.Chest;
        
        [Header("Protection")]
        [Range(0f, 100f)]
        public float armorValue = 0f;
        
        [Range(0f, 1f)]
        public float damageReduction = 0f; // 0-1 percentage
        
        [Header("Stat Modifiers")]
        public StatModifier[] statModifiers;
        
        [Header("Durability")]
        public float maxDurability = 100f;
        public float durabilityLossPerHit = 1f;
        
        [Header("Visual Representation")]
        public GameObject equipmentPrefab; // 3D model when equipped
        public RuntimeAnimatorController animatorOverride; // Animation overrides
        
        [Header("Special Properties")]
        public bool providesNightVision = false;
        public bool providesRadiation = false;
        public bool providesWarmth = false;
        public bool makesStealth = false;
        
        [Header("Audio")]
        public AudioClip equipSound;
        public AudioClip unequipSound;
        
        private void Awake()
        {
            itemType = ItemType.Equipment;
            isStackable = false; // Equipment doesn't stack
            maxStackSize = 1;
        }
        
        /// <summary>
        /// Gets the total stat bonus for a specific stat type
        /// </summary>
        public float GetStatBonus(StatType statType)
        {
            float total = 0f;
            if (statModifiers != null)
            {
                foreach (var modifier in statModifiers)
                {
                    if (modifier.statType == statType)
                    {
                        total += modifier.value;
                    }
                }
            }
            return total;
        }
        
        /// <summary>
        /// Checks if this equipment provides a specific special property
        /// </summary>
        public bool HasSpecialProperty(SpecialProperty property)
        {
            switch (property)
            {
                case SpecialProperty.NightVision: return providesNightVision;
                case SpecialProperty.RadiationProtection: return providesRadiation;
                case SpecialProperty.Warmth: return providesWarmth;
                case SpecialProperty.Stealth: return makesStealth;
                default: return false;
            }
        }
    }
    
    [System.Serializable]
    public class StatModifier
    {
        public StatType statType;
        public float value;
        public ModifierType modifierType = ModifierType.Additive;
        
        [TextArea(1, 2)]
        public string description;
    }
    
    public enum EquipmentSlot
    {
        Head,           // Helmet, hat, mask
        Chest,          // Armor, vest, jacket
        Legs,           // Pants, leg armor
        Feet,           // Boots, shoes
        Hands,          // Gloves
        Back,           // Backpack, cape
        MainHand,       // Primary weapon
        OffHand,        // Secondary weapon, shield
        Ring,           // Ring, small accessories
        Necklace        // Necklace, dog tags
    }
    
    public enum StatType
    {
        Health,
        Stamina,
        Speed,
        Strength,
        Defense,
        Stealth,
        CarryWeight,
        Accuracy,
        CriticalChance,
        AttackSpeed,
        MeleeResistance,
        RangedResistance,
        FireResistance,
        ColdResistance,
        RadiationResistance,
        InfectionResistance
    }
    
    public enum ModifierType
    {
        Additive,       // +10 Health
        Multiplicative, // +10% Health
        Override        // Set Health to 100
    }
    
    public enum SpecialProperty
    {
        NightVision,
        RadiationProtection,
        Warmth,
        Stealth
    }
}
