using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Container that holds multiple inventory slots
    /// </summary>
    [Serializable]
    public class InventoryContainer
    {
        [SerializeField] private string containerName;
        [SerializeField] private InventorySlot[] slots;
        [SerializeField] private float maxWeight = -1f; // -1 = unlimited weight
        [SerializeField] private ContainerType containerType;
        
        public string ContainerName => containerName;
        public int SlotCount => slots?.Length ?? 0;
        public float MaxWeight => maxWeight;
        public ContainerType ContainerType => containerType;
        public InventorySlot[] Slots => slots;
        
        // Events
        public event Action<InventoryContainer, int> OnItemAdded;
        public event Action<InventoryContainer, int> OnItemRemoved;
        public event Action<InventoryContainer> OnContainerChanged;
        
        public InventoryContainer(string name, int slotCount, ContainerType type = ContainerType.General, float maxWeight = -1f)
        {
            this.containerName = name;
            this.containerType = type;
            this.maxWeight = maxWeight;
            
            InitializeSlots(slotCount);
        }
        
        private void InitializeSlots(int slotCount)
        {
            slots = new InventorySlot[slotCount];
            for (int i = 0; i < slotCount; i++)
            {
                slots[i] = new InventorySlot();
                slots[i].OnSlotChanged += OnSlotChanged;
            }
        }
        
        private void OnSlotChanged(InventorySlot slot)
        {
            OnContainerChanged?.Invoke(this);
        }
        
        /// <summary>
        /// Attempts to add an item to the container
        /// </summary>
        /// <param name="item">Item to add</param>
        /// <returns>Remaining item that couldn't be added (null if all was added)</returns>
        public InventoryItem AddItem(InventoryItem item)
        {
            if (item == null) return null;
            
            // Check weight limit
            if (maxWeight > 0 && GetCurrentWeight() + (item.ItemData.weight * item.Quantity) > maxWeight)
            {
                return item; // Can't add due to weight limit
            }
            
            var remainingItem = item;
            
            // First, try to stack with existing items
            if (item.ItemData.isStackable)
            {
                for (int i = 0; i < slots.Length && remainingItem != null; i++)
                {
                    if (!slots[i].IsEmpty && slots[i].Item.CanStackWith(remainingItem))
                    {
                        remainingItem = slots[i].AddItem(remainingItem);
                        if (remainingItem == null)
                        {
                            OnItemAdded?.Invoke(this, i);
                            return null; // All items were added
                        }
                    }
                }
            }
            
            // Then, try to place in empty slots
            for (int i = 0; i < slots.Length && remainingItem != null; i++)
            {
                if (slots[i].IsEmpty)
                {
                    remainingItem = slots[i].AddItem(remainingItem);
                    if (remainingItem == null)
                    {
                        OnItemAdded?.Invoke(this, i);
                        return null; // All items were added
                    }
                }
            }
            
            return remainingItem; // Return whatever couldn't be added
        }
        
        /// <summary>
        /// Removes an item from a specific slot
        /// </summary>
        public InventoryItem RemoveItem(int slotIndex, int quantity = -1)
        {
            if (slotIndex < 0 || slotIndex >= slots.Length) return null;
            
            var removedItem = slots[slotIndex].RemoveItem(quantity);
            if (removedItem != null)
            {
                OnItemRemoved?.Invoke(this, slotIndex);
            }
            
            return removedItem;
        }
        
        /// <summary>
        /// Removes a specific quantity of an item by item ID
        /// </summary>
        public int RemoveItemByID(string itemID, int quantity)
        {
            int totalRemoved = 0;
            
            for (int i = 0; i < slots.Length && totalRemoved < quantity; i++)
            {
                if (!slots[i].IsEmpty && slots[i].Item.ItemID == itemID)
                {
                    int toRemove = Mathf.Min(quantity - totalRemoved, slots[i].Item.Quantity);
                    var removedItem = slots[i].RemoveItem(toRemove);
                    if (removedItem != null)
                    {
                        totalRemoved += removedItem.Quantity;
                        OnItemRemoved?.Invoke(this, i);
                    }
                }
            }
            
            return totalRemoved;
        }
        
        /// <summary>
        /// Checks if the container has a specific item
        /// </summary>
        public bool HasItem(string itemID, int quantity = 1)
        {
            return GetItemCount(itemID) >= quantity;
        }
        
        /// <summary>
        /// Gets the total count of a specific item
        /// </summary>
        public int GetItemCount(string itemID)
        {
            int count = 0;
            foreach (var slot in slots)
            {
                if (!slot.IsEmpty && slot.Item.ItemID == itemID)
                {
                    count += slot.Item.Quantity;
                }
            }
            return count;
        }
        
        /// <summary>
        /// Gets all items in the container
        /// </summary>
        public List<InventoryItem> GetAllItems()
        {
            var items = new List<InventoryItem>();
            foreach (var slot in slots)
            {
                if (!slot.IsEmpty)
                {
                    items.Add(slot.Item);
                }
            }
            return items;
        }
        
        /// <summary>
        /// Gets the current total weight of all items
        /// </summary>
        public float GetCurrentWeight()
        {
            float totalWeight = 0f;
            foreach (var slot in slots)
            {
                totalWeight += slot.GetWeight();
            }
            return totalWeight;
        }
        
        /// <summary>
        /// Gets the number of empty slots
        /// </summary>
        public int GetEmptySlotCount()
        {
            return slots.Count(slot => slot.IsEmpty);
        }
        
        /// <summary>
        /// Checks if the container is full
        /// </summary>
        public bool IsFull()
        {
            return GetEmptySlotCount() == 0;
        }
        
        /// <summary>
        /// Swaps items between two slots
        /// </summary>
        public bool SwapSlots(int slotA, int slotB)
        {
            if (slotA < 0 || slotA >= slots.Length || slotB < 0 || slotB >= slots.Length)
                return false;
            
            return slots[slotA].SwapWith(slots[slotB]);
        }
        
        /// <summary>
        /// Clears all items from the container
        /// </summary>
        public void Clear()
        {
            for (int i = 0; i < slots.Length; i++)
            {
                slots[i].Clear();
            }
        }
        
        /// <summary>
        /// Gets a specific slot
        /// </summary>
        public InventorySlot GetSlot(int index)
        {
            if (index < 0 || index >= slots.Length) return null;
            return slots[index];
        }
    }
    
    public enum ContainerType
    {
        General,        // Standard inventory
        Hotbar,         // Quick access bar
        Equipment,      // Equipped items
        Storage,        // Chest, locker, etc.
        Vendor          // Shop inventory
    }
}
