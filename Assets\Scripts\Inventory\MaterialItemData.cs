using UnityEngine;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Data for crafting materials and resources
    /// </summary>
    [CreateAssetMenu(fileName = "New Material", menuName = "Inventory/Material Item")]
    public class MaterialItemData : ItemData
    {
        [Header("Material Properties")]
        public MaterialCategory category = MaterialCategory.Scrap;
        
        [Header("Crafting")]
        public bool isCraftingMaterial = true;
        public int craftingValue = 1; // How much this material is worth in recipes
        
        [Header("Fuel Properties")]
        public bool canBeFuel = false;
        [Range(0f, 100f)]
        public float fuelValue = 0f; // How long it burns/powers something
        
        [Header("Building")]
        public bool isBuildingMaterial = false;
        public int structuralIntegrity = 0; // For building/fortification
        
        private void Awake()
        {
            itemType = ItemType.Material;
            isStackable = true;
        }
    }
    
    public enum MaterialCategory
    {
        Scrap,          // Metal scraps, broken electronics
        Wood,           // Planks, logs, sticks
        Fabric,         // Cloth, leather, rope
        Electronics,    // Circuits, batteries, wires
        Chemical,       // Chemicals, fuel, oil
        Food,           // Raw food materials
        Medical,        // Medical supplies, bandages
        Ammunition,     // Bullet casings, gunpowder
        Rare            // Rare crafting materials
    }
}
