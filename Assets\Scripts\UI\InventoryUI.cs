using UnityEngine;
using UnityEngine.UI;
using TMPro;
using ZombieGame.Inventory;

namespace ZombieGame.UI
{
    /// <summary>
    /// Main inventory UI controller
    /// </summary>
    public class InventoryUI : MonoBehaviour
    {
        [Header("UI Panels")]
        [SerializeField] private GameObject inventoryPanel;
        [SerializeField] private GameObject backpackPanel;
        [SerializeField] private GameObject equipmentPanel;
        [SerializeField] private GameObject hotbarPanel;
        
        [Header("Slot Prefabs")]
        [SerializeField] private GameObject inventorySlotPrefab;
        [SerializeField] private GameObject equipmentSlotPrefab;
        [SerializeField] private GameObject hotbarSlotPrefab;
        
        [Header("Containers")]
        [SerializeField] private Transform backpackContainer;
        [SerializeField] private Transform equipmentContainer;
        [SerializeField] private Transform hotbarContainer;
        
        [Header("Player Info")]
        [SerializeField] private TextMeshProUGUI playerNameText;
        [SerializeField] private Slider healthBar;
        [SerializeField] private Slider staminaBar;
        [SerializeField] private TextMeshProUGUI weightText;
        
        [Head<PERSON>("Setting<PERSON>")]
        [SerializeField] private bool hideOnStart = true;
        [SerializeField] private KeyCode toggleKey = KeyCode.Tab;
        
        // Components
        private PlayerInventory playerInventory;
        private EquipmentManager equipmentManager;
        private ZombieGame.Player.PlayerStats playerStats;
        
        // UI Elements
        private InventorySlotUI[] backpackSlots;
        private EquipmentSlotUI[] equipmentSlots;
        private HotbarSlotUI[] hotbarSlots;
        
        // State
        private bool isVisible = false;
        
        private void Awake()
        {
            SetupComponents();
            
            if (hideOnStart)
            {
                SetVisible(false);
            }
        }
        
        private void Start()
        {
            SetupUI();
            SubscribeToEvents();
            UpdateUI();
        }
        
        private void Update()
        {
            HandleInput();
            UpdatePlayerInfo();
        }
        
        private void SetupComponents()
        {
            // Find player components
            var player = FindFirstObjectByType<PlayerInventory>();
            if (player != null)
            {
                playerInventory = player;
                equipmentManager = player.GetComponent<EquipmentManager>();
                playerStats = player.GetComponent<ZombieGame.Player.PlayerStats>();
            }
            
            if (playerInventory == null)
            {
                Debug.LogError("InventoryUI: No PlayerInventory found in scene!");
            }
        }
        
        private void SetupUI()
        {
            if (playerInventory == null) return;
            
            // Setup backpack slots
            SetupBackpackSlots();
            
            // Setup equipment slots
            SetupEquipmentSlots();
            
            // Setup hotbar slots
            SetupHotbarSlots();
        }
        
        private void SetupBackpackSlots()
        {
            if (backpackContainer == null || inventorySlotPrefab == null) return;
            
            int slotCount = playerInventory.Backpack.SlotCount;
            backpackSlots = new InventorySlotUI[slotCount];
            
            for (int i = 0; i < slotCount; i++)
            {
                GameObject slotObj = Instantiate(inventorySlotPrefab, backpackContainer);
                InventorySlotUI slotUI = slotObj.GetComponent<InventorySlotUI>();
                
                if (slotUI == null)
                {
                    slotUI = slotObj.AddComponent<InventorySlotUI>();
                }
                
                slotUI.Initialize(playerInventory.Backpack, i);
                backpackSlots[i] = slotUI;
            }
        }
        
        private void SetupEquipmentSlots()
        {
            if (equipmentContainer == null || equipmentSlotPrefab == null) return;
            
            // Create equipment slots for each equipment type
            var equipmentTypes = System.Enum.GetValues(typeof(EquipmentSlot));
            equipmentSlots = new EquipmentSlotUI[equipmentTypes.Length];
            
            for (int i = 0; i < equipmentTypes.Length; i++)
            {
                GameObject slotObj = Instantiate(equipmentSlotPrefab, equipmentContainer);
                EquipmentSlotUI slotUI = slotObj.GetComponent<EquipmentSlotUI>();
                
                if (slotUI == null)
                {
                    slotUI = slotObj.AddComponent<EquipmentSlotUI>();
                }
                
                EquipmentSlot slotType = (EquipmentSlot)equipmentTypes.GetValue(i);
                slotUI.Initialize(equipmentManager, slotType);
                equipmentSlots[i] = slotUI;
            }
        }
        
        private void SetupHotbarSlots()
        {
            if (hotbarContainer == null || hotbarSlotPrefab == null) return;
            
            int slotCount = playerInventory.Hotbar.SlotCount;
            hotbarSlots = new HotbarSlotUI[slotCount];
            
            for (int i = 0; i < slotCount; i++)
            {
                GameObject slotObj = Instantiate(hotbarSlotPrefab, hotbarContainer);
                HotbarSlotUI slotUI = slotObj.GetComponent<HotbarSlotUI>();
                
                if (slotUI == null)
                {
                    slotUI = slotObj.AddComponent<HotbarSlotUI>();
                }
                
                slotUI.Initialize(playerInventory.Hotbar, i, i + 1); // Display 1-8 instead of 0-7
                hotbarSlots[i] = slotUI;
            }
        }
        
        private void SubscribeToEvents()
        {
            if (playerInventory != null)
            {
                playerInventory.OnInventoryToggled += OnInventoryToggled;
                playerInventory.OnInventoryChanged += OnInventoryChanged;
                playerInventory.OnHotbarSelectionChanged += OnHotbarSelectionChanged;
            }
            
            if (playerStats != null)
            {
                playerStats.OnHealthChanged += OnHealthChanged;
                playerStats.OnStaminaChanged += OnStaminaChanged;
            }
        }
        
        private void HandleInput()
        {
            if (Input.GetKeyDown(toggleKey))
            {
                ToggleInventory();
            }
        }
        
        private void UpdatePlayerInfo()
        {
            if (playerStats != null)
            {
                // Update health bar
                if (healthBar != null)
                {
                    healthBar.value = playerStats.HealthPercentage;
                }
                
                // Update stamina bar
                if (staminaBar != null)
                {
                    staminaBar.value = playerStats.StaminaPercentage;
                }
            }
            
            if (playerInventory != null && weightText != null)
            {
                float currentWeight = playerInventory.GetCurrentWeight();
                float maxWeight = playerStats?.CarryWeight ?? 50f;
                weightText.text = $"Weight: {currentWeight:F1}/{maxWeight:F1}";
                
                // Change color based on weight
                if (currentWeight > maxWeight * 0.9f)
                {
                    weightText.color = Color.red;
                }
                else if (currentWeight > maxWeight * 0.7f)
                {
                    weightText.color = Color.yellow;
                }
                else
                {
                    weightText.color = Color.white;
                }
            }
        }
        
        private void UpdateUI()
        {
            // Update all slot UIs
            if (backpackSlots != null)
            {
                foreach (var slot in backpackSlots)
                {
                    slot?.UpdateDisplay();
                }
            }
            
            if (equipmentSlots != null)
            {
                foreach (var slot in equipmentSlots)
                {
                    slot?.UpdateDisplay();
                }
            }
            
            if (hotbarSlots != null)
            {
                foreach (var slot in hotbarSlots)
                {
                    slot?.UpdateDisplay();
                }
            }
        }
        
        public void ToggleInventory()
        {
            SetVisible(!isVisible);
        }
        
        public void SetVisible(bool visible)
        {
            isVisible = visible;
            
            if (inventoryPanel != null)
            {
                inventoryPanel.SetActive(visible);
            }
            
            // Always show hotbar
            if (hotbarPanel != null)
            {
                hotbarPanel.SetActive(true);
            }
            
            // Handle cursor state
            Cursor.lockState = visible ? CursorLockMode.None : CursorLockMode.Locked;
            Cursor.visible = visible;
            
            // Notify player inventory
            if (playerInventory != null && playerInventory.IsInventoryOpen != visible)
            {
                playerInventory.ToggleInventory();
            }
        }
        
        // Event handlers
        private void OnInventoryToggled(bool isOpen)
        {
            if (isVisible != isOpen)
            {
                SetVisible(isOpen);
            }
        }
        
        private void OnInventoryChanged(PlayerInventory inventory)
        {
            UpdateUI();
        }
        
        private void OnHotbarSelectionChanged(int slotIndex)
        {
            if (hotbarSlots != null)
            {
                for (int i = 0; i < hotbarSlots.Length; i++)
                {
                    hotbarSlots[i]?.SetSelected(i == slotIndex);
                }
            }
        }
        
        private void OnHealthChanged(float currentHealth, float maxHealth)
        {
            if (healthBar != null)
            {
                healthBar.value = maxHealth > 0 ? currentHealth / maxHealth : 0f;
            }
        }
        
        private void OnStaminaChanged(float currentStamina, float maxStamina)
        {
            if (staminaBar != null)
            {
                staminaBar.value = maxStamina > 0 ? currentStamina / maxStamina : 0f;
            }
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (playerInventory != null)
            {
                playerInventory.OnInventoryToggled -= OnInventoryToggled;
                playerInventory.OnInventoryChanged -= OnInventoryChanged;
                playerInventory.OnHotbarSelectionChanged -= OnHotbarSelectionChanged;
            }
            
            if (playerStats != null)
            {
                playerStats.OnHealthChanged -= OnHealthChanged;
                playerStats.OnStaminaChanged -= OnStaminaChanged;
            }
        }
        
        // Public properties
        public bool IsVisible => isVisible;
        public PlayerInventory PlayerInventory => playerInventory;
    }
}
