using System;
using UnityEngine;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Runtime instance of an item in the inventory. Contains the data reference and current state.
    /// </summary>
    [Serializable]
    public class InventoryItem
    {
        [SerializeField] private string itemID;
        [SerializeField] private int quantity;
        [SerializeField] private float durability;
        [SerializeField] private ItemCondition condition;
        
        // Runtime reference to the item data (not serialized)
        private ItemData _itemData;
        
        public ItemData ItemData 
        { 
            get 
            { 
                if (_itemData == null && !string.IsNullOrEmpty(itemID))
                {
                    _itemData = ItemDatabase.GetItemData(itemID);
                }
                return _itemData; 
            } 
        }
        
        public string ItemID => itemID;
        public int Quantity => quantity;
        public float Durability => durability;
        public ItemCondition Condition => condition;
        
        // Constructor for new items
        public InventoryItem(ItemData itemData, int quantity = 1)
        {
            this.itemID = itemData.itemID;
            this._itemData = itemData;
            this.quantity = Mathf.Clamp(quantity, 1, itemData.maxStackSize);
            this.durability = 100f; // Start at full durability
            this.condition = ItemCondition.Perfect;
        }
        
        // Constructor for loading from save data
        public InventoryItem(string itemID, int quantity, float durability, ItemCondition condition)
        {
            this.itemID = itemID;
            this.quantity = quantity;
            this.durability = durability;
            this.condition = condition;
        }
        
        /// <summary>
        /// Attempts to add quantity to this item stack
        /// </summary>
        /// <param name="amount">Amount to add</param>
        /// <returns>Amount that couldn't be added (overflow)</returns>
        public int AddQuantity(int amount)
        {
            if (ItemData == null || !ItemData.isStackable) return amount;
            
            int maxCanAdd = ItemData.maxStackSize - quantity;
            int actualAdd = Mathf.Min(amount, maxCanAdd);
            
            quantity += actualAdd;
            return amount - actualAdd; // Return overflow
        }
        
        /// <summary>
        /// Removes quantity from this item stack
        /// </summary>
        /// <param name="amount">Amount to remove</param>
        /// <returns>Amount actually removed</returns>
        public int RemoveQuantity(int amount)
        {
            int actualRemove = Mathf.Min(amount, quantity);
            quantity -= actualRemove;
            return actualRemove;
        }
        
        /// <summary>
        /// Reduces durability and updates condition
        /// </summary>
        public void ReduceDurability(float amount)
        {
            durability = Mathf.Clamp(durability - amount, 0f, 100f);
            UpdateCondition();
        }
        
        /// <summary>
        /// Repairs the item
        /// </summary>
        public void Repair(float amount)
        {
            durability = Mathf.Clamp(durability + amount, 0f, 100f);
            UpdateCondition();
        }
        
        private void UpdateCondition()
        {
            if (durability >= 80f) condition = ItemCondition.Perfect;
            else if (durability >= 60f) condition = ItemCondition.Good;
            else if (durability >= 40f) condition = ItemCondition.Worn;
            else if (durability >= 20f) condition = ItemCondition.Damaged;
            else if (durability > 0f) condition = ItemCondition.Broken;
            else condition = ItemCondition.Destroyed;
        }
        
        /// <summary>
        /// Checks if this item can stack with another
        /// </summary>
        public bool CanStackWith(InventoryItem other)
        {
            if (other == null || ItemData == null || other.ItemData == null) return false;
            if (!ItemData.isStackable) return false;
            
            return itemID == other.itemID && 
                   condition == other.condition &&
                   Mathf.Approximately(durability, other.durability);
        }
        
        /// <summary>
        /// Creates a copy of this item with specified quantity
        /// </summary>
        public InventoryItem Split(int splitQuantity)
        {
            if (splitQuantity >= quantity) return null;
            
            quantity -= splitQuantity;
            return new InventoryItem(itemID, splitQuantity, durability, condition);
        }
        
        public bool IsEmpty => quantity <= 0;
        public bool IsFull => ItemData != null && quantity >= ItemData.maxStackSize;
    }
    
    public enum ItemCondition
    {
        Perfect,
        Good,
        Worn,
        Damaged,
        Broken,
        Destroyed
    }
}
