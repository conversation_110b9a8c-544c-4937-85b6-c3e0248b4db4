using System;
using System.Collections.Generic;
using UnityEngine;
using ZombieGame.Inventory;

namespace ZombieGame.Player
{
    /// <summary>
    /// Manages player stats that can be modified by equipment and other factors
    /// </summary>
    public class PlayerStats : MonoBehaviour
    {
        [Header("Base Stats")]
        [SerializeField] private float baseHealth = 100f;
        [SerializeField] private float baseStamina = 100f;
        [SerializeField] private float baseSpeed = 5f;
        [SerializeField] private float baseStrength = 10f;
        [SerializeField] private float baseDefense = 0f;
        [SerializeField] private float baseStealth = 0f;
        [SerializeField] private float baseCarryWeight = 50f;
        [SerializeField] private float baseAccuracy = 0.8f;
        
        [Header("Current Stats")]
        [SerializeField] private float currentHealth;
        [SerializeField] private float currentStamina;
        
        [Header("Resistances")]
        [SerializeField] private float meleeResistance = 0f;
        [SerializeField] private float rangedResistance = 0f;
        [SerializeField] private float fireResistance = 0f;
        [SerializeField] private float coldResistance = 0f;
        [SerializeField] private float radiationResistance = 0f;
        [SerializeField] private float infectionResistance = 0f;
        
        // Calculated stats (base + modifiers)
        private Dictionary<StatType, float> calculatedStats = new Dictionary<StatType, float>();
        private List<StatModifier> activeModifiers = new List<StatModifier>();
        
        // Events
        public event Action<StatType, float, float> OnStatChanged; // statType, oldValue, newValue
        public event Action<float, float> OnHealthChanged; // currentHealth, maxHealth
        public event Action<float, float> OnStaminaChanged; // currentStamina, maxStamina
        public event Action OnStatsRecalculated;
        
        // Properties
        public float MaxHealth => GetStat(StatType.Health);
        public float MaxStamina => GetStat(StatType.Stamina);
        public float CurrentHealth => currentHealth;
        public float CurrentStamina => currentStamina;
        public float Speed => GetStat(StatType.Speed);
        public float Strength => GetStat(StatType.Strength);
        public float Defense => GetStat(StatType.Defense);
        public float CarryWeight => GetStat(StatType.CarryWeight);
        public float Accuracy => GetStat(StatType.Accuracy);
        
        private void Awake()
        {
            InitializeStats();
        }
        
        private void Start()
        {
            // Set current health and stamina to max
            currentHealth = MaxHealth;
            currentStamina = MaxStamina;
        }
        
        private void InitializeStats()
        {
            // Initialize base stats
            calculatedStats[StatType.Health] = baseHealth;
            calculatedStats[StatType.Stamina] = baseStamina;
            calculatedStats[StatType.Speed] = baseSpeed;
            calculatedStats[StatType.Strength] = baseStrength;
            calculatedStats[StatType.Defense] = baseDefense;
            calculatedStats[StatType.Stealth] = baseStealth;
            calculatedStats[StatType.CarryWeight] = baseCarryWeight;
            calculatedStats[StatType.Accuracy] = baseAccuracy;
            calculatedStats[StatType.MeleeResistance] = meleeResistance;
            calculatedStats[StatType.RangedResistance] = rangedResistance;
            calculatedStats[StatType.FireResistance] = fireResistance;
            calculatedStats[StatType.ColdResistance] = coldResistance;
            calculatedStats[StatType.RadiationResistance] = radiationResistance;
            calculatedStats[StatType.InfectionResistance] = infectionResistance;
        }
        
        /// <summary>
        /// Gets the current value of a stat (base + modifiers)
        /// </summary>
        public float GetStat(StatType statType)
        {
            calculatedStats.TryGetValue(statType, out float value);
            return value;
        }
        
        /// <summary>
        /// Gets the base value of a stat (without modifiers)
        /// </summary>
        public float GetBaseStat(StatType statType)
        {
            switch (statType)
            {
                case StatType.Health: return baseHealth;
                case StatType.Stamina: return baseStamina;
                case StatType.Speed: return baseSpeed;
                case StatType.Strength: return baseStrength;
                case StatType.Defense: return baseDefense;
                case StatType.Stealth: return baseStealth;
                case StatType.CarryWeight: return baseCarryWeight;
                case StatType.Accuracy: return baseAccuracy;
                case StatType.MeleeResistance: return meleeResistance;
                case StatType.RangedResistance: return rangedResistance;
                case StatType.FireResistance: return fireResistance;
                case StatType.ColdResistance: return coldResistance;
                case StatType.RadiationResistance: return radiationResistance;
                case StatType.InfectionResistance: return infectionResistance;
                default: return 0f;
            }
        }
        
        /// <summary>
        /// Adds a stat modifier (from equipment, buffs, etc.)
        /// </summary>
        public void AddModifier(StatModifier modifier)
        {
            activeModifiers.Add(modifier);
            RecalculateStats();
        }
        
        /// <summary>
        /// Removes a stat modifier
        /// </summary>
        public void RemoveModifier(StatModifier modifier)
        {
            activeModifiers.Remove(modifier);
            RecalculateStats();
        }
        
        /// <summary>
        /// Removes all modifiers from a specific source (e.g., when unequipping)
        /// </summary>
        public void RemoveModifiersFromSource(object source)
        {
            activeModifiers.RemoveAll(m => m.Equals(source));
            RecalculateStats();
        }
        
        /// <summary>
        /// Recalculates all stats based on base values and active modifiers
        /// </summary>
        public void RecalculateStats()
        {
            var oldStats = new Dictionary<StatType, float>(calculatedStats);
            
            // Reset to base values
            InitializeStats();
            
            // Apply all modifiers
            foreach (var modifier in activeModifiers)
            {
                ApplyModifier(modifier);
            }
            
            // Check for stat changes and fire events
            foreach (var kvp in calculatedStats)
            {
                if (oldStats.TryGetValue(kvp.Key, out float oldValue) && !Mathf.Approximately(oldValue, kvp.Value))
                {
                    OnStatChanged?.Invoke(kvp.Key, oldValue, kvp.Value);
                }
            }
            
            // Update current health/stamina if max values changed
            float maxHealth = GetStat(StatType.Health);
            float maxStamina = GetStat(StatType.Stamina);
            
            if (oldStats.TryGetValue(StatType.Health, out float oldMaxHealth) && !Mathf.Approximately(oldMaxHealth, maxHealth))
            {
                // Adjust current health proportionally
                float healthRatio = currentHealth / oldMaxHealth;
                currentHealth = Mathf.Min(currentHealth, maxHealth); // Don't exceed new max
                OnHealthChanged?.Invoke(currentHealth, maxHealth);
            }
            
            if (oldStats.TryGetValue(StatType.Stamina, out float oldMaxStamina) && !Mathf.Approximately(oldMaxStamina, maxStamina))
            {
                // Adjust current stamina proportionally
                float staminaRatio = currentStamina / oldMaxStamina;
                currentStamina = Mathf.Min(currentStamina, maxStamina); // Don't exceed new max
                OnStaminaChanged?.Invoke(currentStamina, maxStamina);
            }
            
            OnStatsRecalculated?.Invoke();
        }
        
        private void ApplyModifier(StatModifier modifier)
        {
            if (!calculatedStats.ContainsKey(modifier.statType)) return;
            
            float currentValue = calculatedStats[modifier.statType];
            float baseValue = GetBaseStat(modifier.statType);
            
            switch (modifier.modifierType)
            {
                case ModifierType.Additive:
                    calculatedStats[modifier.statType] = currentValue + modifier.value;
                    break;
                    
                case ModifierType.Multiplicative:
                    calculatedStats[modifier.statType] = baseValue * (1f + modifier.value);
                    break;
                    
                case ModifierType.Override:
                    calculatedStats[modifier.statType] = modifier.value;
                    break;
            }
            
            // Ensure stats don't go below 0 (except for some stats that can be negative)
            if (modifier.statType != StatType.Speed && modifier.statType != StatType.Stealth)
            {
                calculatedStats[modifier.statType] = Mathf.Max(0f, calculatedStats[modifier.statType]);
            }
        }
        
        /// <summary>
        /// Modifies current health
        /// </summary>
        public void ModifyHealth(float amount)
        {
            float oldHealth = currentHealth;
            currentHealth = Mathf.Clamp(currentHealth + amount, 0f, MaxHealth);
            
            if (!Mathf.Approximately(oldHealth, currentHealth))
            {
                OnHealthChanged?.Invoke(currentHealth, MaxHealth);
            }
        }
        
        /// <summary>
        /// Modifies current stamina
        /// </summary>
        public void ModifyStamina(float amount)
        {
            float oldStamina = currentStamina;
            currentStamina = Mathf.Clamp(currentStamina + amount, 0f, MaxStamina);
            
            if (!Mathf.Approximately(oldStamina, currentStamina))
            {
                OnStaminaChanged?.Invoke(currentStamina, MaxStamina);
            }
        }
        
        /// <summary>
        /// Gets all active modifiers
        /// </summary>
        public List<StatModifier> GetActiveModifiers()
        {
            return new List<StatModifier>(activeModifiers);
        }
        
        /// <summary>
        /// Checks if the player is alive
        /// </summary>
        public bool IsAlive => currentHealth > 0f;
        
        /// <summary>
        /// Gets health as a percentage (0-1)
        /// </summary>
        public float HealthPercentage => MaxHealth > 0 ? currentHealth / MaxHealth : 0f;
        
        /// <summary>
        /// Gets stamina as a percentage (0-1)
        /// </summary>
        public float StaminaPercentage => MaxStamina > 0 ? currentStamina / MaxStamina : 0f;
    }
}
