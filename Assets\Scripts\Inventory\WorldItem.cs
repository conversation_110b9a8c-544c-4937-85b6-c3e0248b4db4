using UnityEngine;
using <PERSON><PERSON><PERSON>;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Represents an item that exists in the world and can be picked up
    /// </summary>
    public class WorldItem : MonoBehaviour
    {
        [Header("Item Data")]
        [SerializeField] private InventoryItem item;
        
        [Header("Visual Settings")]
        [SerializeField] private float bobHeight = 0.2f;
        [SerializeField] private float bobSpeed = 2f;
        [SerializeField] private float rotationSpeed = 50f;
        [SerializeField] private bool enableBobbing = true;
        [SerializeField] private bool enableRotation = true;
        
        [Header("Pickup Settings")]
        [SerializeField] private float pickupRange = 2f;
        [SerializeField] private LayerMask playerLayer = 1;
        [SerializeField] private bool autoPickup = false;
        [SerializeField] private float autoPickupDelay = 1f;
        
        [Header("UI")]
        [SerializeField] private Canvas worldCanvas;
        [SerializeField] private TextMeshProUGUI itemNameText;
        [SerializeField] private TextMeshProUGUI quantityText;
        [SerializeField] private GameObject pickupPrompt;
        
        [Header("Audio")]
        [SerializeField] private AudioSource audioSource;
        [SerializeField] private AudioClip spawnSound;
        
        // Private variables
        private Vector3 startPosition;
        private bool playerInRange = false;
        private PlayerInventory nearbyPlayer;
        private float spawnTime;
        private Collider itemCollider;
        private Rigidbody itemRigidbody;
        
        // Properties
        public InventoryItem Item => item;
        public bool CanBePickedUp => item != null && !item.IsEmpty;
        
        private void Awake()
        {
            SetupComponents();
        }
        
        private void Start()
        {
            startPosition = transform.position;
            spawnTime = Time.time;
            
            UpdateUI();
            PlaySpawnSound();
            
            // Setup auto-pickup if enabled
            if (autoPickup)
            {
                Invoke(nameof(TryAutoPickup), autoPickupDelay);
            }
        }
        
        private void SetupComponents()
        {
            // Ensure we have a collider for pickup detection
            itemCollider = GetComponent<Collider>();
            if (itemCollider == null)
            {
                itemCollider = gameObject.AddComponent<SphereCollider>();
                itemCollider.isTrigger = true;
                ((SphereCollider)itemCollider).radius = pickupRange;
            }
            
            // Setup rigidbody for physics
            itemRigidbody = GetComponent<Rigidbody>();
            if (itemRigidbody == null)
            {
                itemRigidbody = gameObject.AddComponent<Rigidbody>();
            }
            
            // Setup audio source
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
                audioSource.spatialBlend = 1f; // 3D sound
            }
            
            // Setup world canvas if not assigned
            if (worldCanvas == null)
            {
                SetupWorldCanvas();
            }
        }
        
        private void SetupWorldCanvas()
        {
            // Create world canvas for item info
            GameObject canvasObj = new GameObject("WorldCanvas");
            canvasObj.transform.SetParent(transform);
            canvasObj.transform.localPosition = Vector3.up * 1.5f;
            
            worldCanvas = canvasObj.AddComponent<Canvas>();
            worldCanvas.renderMode = RenderMode.WorldSpace;
            worldCanvas.worldCamera = Camera.main;
            
            var canvasScaler = canvasObj.AddComponent<UnityEngine.UI.CanvasScaler>();
            canvasScaler.dynamicPixelsPerUnit = 10f;
            
            // Create item name text
            GameObject textObj = new GameObject("ItemText");
            textObj.transform.SetParent(canvasObj.transform);
            textObj.transform.localPosition = Vector3.zero;
            textObj.transform.localScale = Vector3.one * 0.01f;
            
            itemNameText = textObj.AddComponent<TextMeshProUGUI>();
            itemNameText.text = "Item";
            itemNameText.fontSize = 24;
            itemNameText.alignment = TextAlignmentOptions.Center;
            itemNameText.color = Color.white;
            
            // Create quantity text
            GameObject quantityObj = new GameObject("QuantityText");
            quantityObj.transform.SetParent(canvasObj.transform);
            quantityObj.transform.localPosition = Vector3.down * 30f;
            quantityObj.transform.localScale = Vector3.one * 0.01f;
            
            quantityText = quantityObj.AddComponent<TextMeshProUGUI>();
            quantityText.fontSize = 18;
            quantityText.alignment = TextAlignmentOptions.Center;
            quantityText.color = Color.yellow;
            
            // Initially hide the canvas
            worldCanvas.gameObject.SetActive(false);
        }
        
        private void Update()
        {
            HandleVisualEffects();
            HandlePlayerInteraction();
        }
        
        private void HandleVisualEffects()
        {
            if (enableBobbing)
            {
                float newY = startPosition.y + Mathf.Sin(Time.time * bobSpeed) * bobHeight;
                transform.position = new Vector3(transform.position.x, newY, transform.position.z);
            }
            
            if (enableRotation)
            {
                transform.Rotate(Vector3.up, rotationSpeed * Time.deltaTime);
            }
        }
        
        private void HandlePlayerInteraction()
        {
            // Input handling is now done by the InteractionSystem component
            // This method is kept for potential future use but doesn't handle input directly
        }
        
        private void OnTriggerEnter(Collider other)
        {
            if (IsPlayer(other))
            {
                PlayerInventory playerInventory = other.GetComponent<PlayerInventory>();
                if (playerInventory != null)
                {
                    playerInRange = true;
                    nearbyPlayer = playerInventory;
                    ShowUI(true);
                }
            }
        }
        
        private void OnTriggerExit(Collider other)
        {
            if (IsPlayer(other))
            {
                playerInRange = false;
                nearbyPlayer = null;
                ShowUI(false);
            }
        }
        
        private bool IsPlayer(Collider other)
        {
            return ((1 << other.gameObject.layer) & playerLayer) != 0;
        }
        
        private void ShowUI(bool show)
        {
            if (worldCanvas != null)
            {
                worldCanvas.gameObject.SetActive(show);
            }
            
            if (pickupPrompt != null)
            {
                pickupPrompt.SetActive(show);
            }
        }
        
        private void UpdateUI()
        {
            if (item?.ItemData != null)
            {
                if (itemNameText != null)
                {
                    itemNameText.text = item.ItemData.itemName;
                    
                    // Color based on rarity
                    switch (item.ItemData.rarity)
                    {
                        case ItemRarity.Common: itemNameText.color = Color.white; break;
                        case ItemRarity.Uncommon: itemNameText.color = Color.green; break;
                        case ItemRarity.Rare: itemNameText.color = Color.blue; break;
                        case ItemRarity.Epic: itemNameText.color = Color.magenta; break;
                        case ItemRarity.Legendary: itemNameText.color = Color.yellow; break;
                    }
                }
                
                if (quantityText != null)
                {
                    if (item.Quantity > 1)
                    {
                        quantityText.text = $"x{item.Quantity}";
                        quantityText.gameObject.SetActive(true);
                    }
                    else
                    {
                        quantityText.gameObject.SetActive(false);
                    }
                }
            }
        }
        
        /// <summary>
        /// Attempts to pick up this item
        /// </summary>
        public bool TryPickup()
        {
            if (!CanBePickedUp || nearbyPlayer == null) return false;
            
            bool success = nearbyPlayer.PickupItem(item);
            if (success)
            {
                // Play pickup sound from item data
                if (item.ItemData.pickupSound != null)
                {
                    audioSource.PlayOneShot(item.ItemData.pickupSound);
                }
                
                // Destroy the world item
                Destroy(gameObject, 0.1f); // Small delay to let sound play
                return true;
            }
            
            return false;
        }
        
        private void TryAutoPickup()
        {
            if (autoPickup && playerInRange)
            {
                TryPickup();
            }
        }
        
        /// <summary>
        /// Initializes this world item with specific item data
        /// </summary>
        public void Initialize(InventoryItem inventoryItem)
        {
            item = inventoryItem;
            UpdateUI();
            
            // Set the visual model if available
            if (item?.ItemData?.worldPrefab != null)
            {
                // TODO: Instantiate the world prefab as a child
                GameObject model = Instantiate(item.ItemData.worldPrefab, transform);
                model.transform.localPosition = Vector3.zero;
            }
        }
        
        private void PlaySpawnSound()
        {
            if (spawnSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(spawnSound);
            }
        }
        
        /// <summary>
        /// Creates a world item at a specific position
        /// </summary>
        public static WorldItem SpawnWorldItem(InventoryItem item, Vector3 position, Vector3? velocity = null)
        {
            // Create basic world item GameObject
            GameObject worldItemObj = new GameObject($"WorldItem_{item.ItemData.itemName}");
            worldItemObj.transform.position = position;
            
            WorldItem worldItem = worldItemObj.AddComponent<WorldItem>();
            worldItem.Initialize(item);
            
            // Apply velocity if specified
            if (velocity.HasValue && worldItem.itemRigidbody != null)
            {
                worldItem.itemRigidbody.linearVelocity = velocity.Value;
            }
            
            return worldItem;
        }
    }
}
