using UnityEngine;
using UnityEngine.UI;
using UnityEngine.InputSystem;
using TMPro;
using ZombieGame.Inventory;

namespace ZombieGame.UI
{
    /// <summary>
    /// Simple inventory UI that creates slots directly without prefabs
    /// </summary>
    public class SimpleInventoryUI : MonoBehaviour
    {
        [Header("UI Settings")]
        [SerializeField] private bool setupOnStart = true;
        [SerializeField] private KeyCode toggleKey = KeyCode.Tab;
        [SerializeField] private Vector2 slotSize = new Vector2(60, 60);
        [SerializeField] private float slotSpacing = 5f;
        [SerializeField] private int slotsPerRow = 5;
        
        [Header("Colors")]
        [SerializeField] private Color panelColor = new Color(0.1f, 0.1f, 0.1f, 0.9f);
        [SerializeField] private Color slotColor = new Color(0.3f, 0.3f, 0.3f, 0.8f);
        [SerializeField] private Color selectedSlotColor = new Color(0.5f, 0.8f, 0.5f, 0.8f);
        [SerializeField] private Color hotbarColor = new Color(0.2f, 0.2f, 0.2f, 0.8f);
        
        // UI Elements
        private Canvas canvas;
        private GameObject inventoryPanel;
        private GameObject hotbarPanel;
        private GameObject[] backpackSlots;
        private GameObject[] hotbarSlots;
        private GameObject[] equipmentSlots;
        
        // Components
        private PlayerInventory playerInventory;
        private EquipmentManager equipmentManager;
        private ZombieGame.Player.PlayerStats playerStats;
        
        // State
        private bool isVisible = false;

        // Input Actions
        private InputAction toggleInventoryAction;
        
        private void Start()
        {
            if (setupOnStart)
            {
                SetupUI();
            }

            SetupInput();
        }
        
        private void Update()
        {
            HandleInput();
            UpdateSlots();
        }
        
        private void SetupUI()
        {
            Debug.Log("Setting up simple inventory UI...");
            
            // Find player components
            FindPlayerComponents();
            
            // Create or find canvas
            SetupCanvas();
            
            // Create UI panels
            CreateInventoryPanel();
            CreateHotbarPanel();
            
            // Create slots
            CreateBackpackSlots();
            CreateEquipmentSlots();
            CreateHotbarSlots();
            
            // Start hidden
            SetVisible(false);
            
            Debug.Log("Simple inventory UI setup complete!");

            // Debug: Check if we have items
            if (playerInventory != null)
            {
                Debug.Log($"Backpack has {playerInventory.Backpack.SlotCount} slots");
                Debug.Log($"Hotbar has {playerInventory.Hotbar.SlotCount} slots");

                // Check for items in backpack
                for (int i = 0; i < playerInventory.Backpack.SlotCount; i++)
                {
                    var slot = playerInventory.Backpack.GetSlot(i);
                    if (slot?.Item != null)
                    {
                        Debug.Log($"Backpack slot {i}: {slot.Item.ItemData.itemName} x{slot.Item.Quantity}");
                    }
                }
            }
        }
        
        private void FindPlayerComponents()
        {
            var player = FindFirstObjectByType<PlayerInventory>();
            if (player != null)
            {
                playerInventory = player;
                equipmentManager = player.GetComponent<EquipmentManager>();
                playerStats = player.GetComponent<ZombieGame.Player.PlayerStats>();
            }
            
            if (playerInventory == null)
            {
                Debug.LogError("SimpleInventoryUI: No PlayerInventory found in scene!");
            }
        }
        
        private void SetupCanvas()
        {
            canvas = FindFirstObjectByType<Canvas>();
            
            if (canvas == null)
            {
                // Create new canvas
                GameObject canvasObj = new GameObject("InventoryCanvas");
                canvas = canvasObj.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvas.sortingOrder = 100;
                
                CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
                scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
                scaler.referenceResolution = new Vector2(1920, 1080);
                
                canvasObj.AddComponent<GraphicRaycaster>();
            }
        }
        
        private void CreateInventoryPanel()
        {
            inventoryPanel = new GameObject("InventoryPanel");
            inventoryPanel.transform.SetParent(canvas.transform, false);
            
            RectTransform rect = inventoryPanel.AddComponent<RectTransform>();
            rect.anchorMin = new Vector2(0.5f, 0.5f);
            rect.anchorMax = new Vector2(0.5f, 0.5f);
            rect.sizeDelta = new Vector2(800, 600);
            rect.anchoredPosition = Vector2.zero;
            
            Image image = inventoryPanel.AddComponent<Image>();
            image.color = panelColor;
            
            // Add title
            CreateTitle(inventoryPanel, "Inventory", new Vector2(0, 280));
        }
        
        private void CreateHotbarPanel()
        {
            hotbarPanel = new GameObject("HotbarPanel");
            hotbarPanel.transform.SetParent(canvas.transform, false);
            
            RectTransform rect = hotbarPanel.AddComponent<RectTransform>();
            rect.anchorMin = new Vector2(0.5f, 0f);
            rect.anchorMax = new Vector2(0.5f, 0f);
            rect.sizeDelta = new Vector2(400, 70);
            rect.anchoredPosition = new Vector2(0, 50);
            
            Image image = hotbarPanel.AddComponent<Image>();
            image.color = hotbarColor;
        }
        
        private void CreateBackpackSlots()
        {
            if (playerInventory?.Backpack == null) return;
            
            int slotCount = playerInventory.Backpack.SlotCount;
            backpackSlots = new GameObject[slotCount];
            
            // Create container for backpack slots
            GameObject container = new GameObject("BackpackContainer");
            container.transform.SetParent(inventoryPanel.transform, false);
            
            RectTransform containerRect = container.AddComponent<RectTransform>();
            containerRect.anchorMin = new Vector2(0.5f, 0.1f);
            containerRect.anchorMax = new Vector2(0.9f, 0.8f);
            containerRect.offsetMin = Vector2.zero;
            containerRect.offsetMax = Vector2.zero;
            
            // Create slots in grid
            for (int i = 0; i < slotCount; i++)
            {
                backpackSlots[i] = CreateSlot(container, $"BackpackSlot_{i}", i, slotCount, true);
            }
            
            CreateTitle(inventoryPanel, "Backpack", new Vector2(200, 200));
        }
        
        private void CreateEquipmentSlots()
        {
            if (equipmentManager == null) return;
            
            var equipmentTypes = System.Enum.GetValues(typeof(EquipmentSlot));
            equipmentSlots = new GameObject[equipmentTypes.Length];
            
            // Create container for equipment slots
            GameObject container = new GameObject("EquipmentContainer");
            container.transform.SetParent(inventoryPanel.transform, false);
            
            RectTransform containerRect = container.AddComponent<RectTransform>();
            containerRect.anchorMin = new Vector2(0.05f, 0.1f);
            containerRect.anchorMax = new Vector2(0.45f, 0.8f);
            containerRect.offsetMin = Vector2.zero;
            containerRect.offsetMax = Vector2.zero;
            
            // Create equipment slots vertically
            for (int i = 0; i < equipmentTypes.Length; i++)
            {
                EquipmentSlot slotType = (EquipmentSlot)equipmentTypes.GetValue(i);
                equipmentSlots[i] = CreateEquipmentSlot(container, slotType, i);
            }
            
            CreateTitle(inventoryPanel, "Equipment", new Vector2(-200, 200));
        }
        
        private void CreateHotbarSlots()
        {
            if (playerInventory?.Hotbar == null) return;
            
            int slotCount = playerInventory.Hotbar.SlotCount;
            hotbarSlots = new GameObject[slotCount];
            
            for (int i = 0; i < slotCount; i++)
            {
                hotbarSlots[i] = CreateHotbarSlot(hotbarPanel, i);
            }
        }
        
        private GameObject CreateSlot(GameObject parent, string name, int index, int totalSlots, bool showGrid)
        {
            GameObject slot = new GameObject(name);
            slot.transform.SetParent(parent.transform, false);
            
            RectTransform rect = slot.AddComponent<RectTransform>();
            
            if (showGrid)
            {
                // Calculate grid position
                int row = index / slotsPerRow;
                int col = index % slotsPerRow;
                
                float x = col * (slotSize.x + slotSpacing) - (slotsPerRow - 1) * (slotSize.x + slotSpacing) * 0.5f;
                float y = -row * (slotSize.y + slotSpacing);
                
                rect.anchoredPosition = new Vector2(x, y);
            }
            
            rect.sizeDelta = slotSize;
            
            // Add background
            Image bg = slot.AddComponent<Image>();
            bg.color = slotColor;
            
            // Add item icon
            GameObject iconObj = new GameObject("Icon");
            iconObj.transform.SetParent(slot.transform, false);
            
            RectTransform iconRect = iconObj.AddComponent<RectTransform>();
            iconRect.anchorMin = Vector2.zero;
            iconRect.anchorMax = Vector2.one;
            iconRect.offsetMin = Vector2.one * 5;
            iconRect.offsetMax = Vector2.one * -5;
            
            Image icon = iconObj.AddComponent<Image>();
            icon.color = Color.clear; // Start transparent
            
            // Add quantity text
            GameObject textObj = new GameObject("Quantity");
            textObj.transform.SetParent(slot.transform, false);
            
            RectTransform textRect = textObj.AddComponent<RectTransform>();
            textRect.anchorMin = new Vector2(0.6f, 0f);
            textRect.anchorMax = new Vector2(1f, 0.4f);
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;
            
            TextMeshProUGUI text = textObj.AddComponent<TextMeshProUGUI>();
            text.text = "";
            text.fontSize = 12;
            text.color = Color.white;
            text.alignment = TextAlignmentOptions.BottomRight;
            
            return slot;
        }
        
        private GameObject CreateEquipmentSlot(GameObject parent, EquipmentSlot slotType, int index)
        {
            GameObject slot = CreateSlot(parent, $"Equipment_{slotType}", index, 10, false);
            
            RectTransform rect = slot.GetComponent<RectTransform>();
            rect.anchoredPosition = new Vector2(0, -index * (slotSize.y + slotSpacing));
            
            // Add label
            GameObject labelObj = new GameObject("Label");
            labelObj.transform.SetParent(slot.transform, false);
            
            RectTransform labelRect = labelObj.AddComponent<RectTransform>();
            labelRect.anchorMin = new Vector2(1.1f, 0f);
            labelRect.anchorMax = new Vector2(2f, 1f);
            labelRect.offsetMin = Vector2.zero;
            labelRect.offsetMax = Vector2.zero;
            
            TextMeshProUGUI label = labelObj.AddComponent<TextMeshProUGUI>();
            label.text = slotType.ToString();
            label.fontSize = 10;
            label.color = Color.white;
            label.alignment = TextAlignmentOptions.MidlineLeft;
            
            return slot;
        }
        
        private GameObject CreateHotbarSlot(GameObject parent, int index)
        {
            GameObject slot = CreateSlot(parent, $"HotbarSlot_{index}", index, 8, false);
            
            RectTransform rect = slot.GetComponent<RectTransform>();
            float x = (index - 3.5f) * (slotSize.x + slotSpacing);
            rect.anchoredPosition = new Vector2(x, 0);
            
            // Add key number
            GameObject keyObj = new GameObject("KeyNumber");
            keyObj.transform.SetParent(slot.transform, false);
            
            RectTransform keyRect = keyObj.AddComponent<RectTransform>();
            keyRect.anchorMin = new Vector2(0f, 0.8f);
            keyRect.anchorMax = new Vector2(0.4f, 1f);
            keyRect.offsetMin = Vector2.zero;
            keyRect.offsetMax = Vector2.zero;
            
            TextMeshProUGUI keyText = keyObj.AddComponent<TextMeshProUGUI>();
            keyText.text = (index + 1).ToString();
            keyText.fontSize = 10;
            keyText.color = Color.yellow;
            keyText.alignment = TextAlignmentOptions.TopLeft;
            
            return slot;
        }
        
        private void CreateTitle(GameObject parent, string title, Vector2 position)
        {
            GameObject titleObj = new GameObject($"{title}Title");
            titleObj.transform.SetParent(parent.transform, false);
            
            RectTransform rect = titleObj.AddComponent<RectTransform>();
            rect.anchoredPosition = position;
            rect.sizeDelta = new Vector2(200, 30);
            
            TextMeshProUGUI text = titleObj.AddComponent<TextMeshProUGUI>();
            text.text = title;
            text.fontSize = 18;
            text.color = Color.white;
            text.alignment = TextAlignmentOptions.Center;
            text.fontStyle = FontStyles.Bold;
        }
        
        private void UpdateSlots()
        {
            if (playerInventory == null) return;
            
            // Update backpack slots
            UpdateBackpackSlots();
            
            // Update equipment slots
            UpdateEquipmentSlots();
            
            // Update hotbar slots
            UpdateHotbarSlots();
        }
        
        private void UpdateBackpackSlots()
        {
            if (backpackSlots == null || playerInventory?.Backpack == null) return;

            for (int i = 0; i < backpackSlots.Length; i++)
            {
                var slot = playerInventory.Backpack.GetSlot(i);
                UpdateSlotDisplay(backpackSlots[i], slot?.Item);
            }
        }
        
        private void UpdateEquipmentSlots()
        {
            if (equipmentSlots == null || equipmentManager == null) return;
            
            var equipmentTypes = System.Enum.GetValues(typeof(EquipmentSlot));
            for (int i = 0; i < equipmentSlots.Length && i < equipmentTypes.Length; i++)
            {
                EquipmentSlot slotType = (EquipmentSlot)equipmentTypes.GetValue(i);
                var item = equipmentManager.GetEquippedItem(slotType);
                UpdateSlotDisplay(equipmentSlots[i], item);
            }
        }
        
        private void UpdateHotbarSlots()
        {
            if (hotbarSlots == null || playerInventory.Hotbar == null) return;
            
            for (int i = 0; i < hotbarSlots.Length; i++)
            {
                var slot = playerInventory.Hotbar.GetSlot(i);
                UpdateSlotDisplay(hotbarSlots[i], slot?.Item);
                
                // Highlight selected slot
                Image bg = hotbarSlots[i].GetComponent<Image>();
                bg.color = (i == playerInventory.SelectedHotbarSlot) ? selectedSlotColor : slotColor;
            }
        }
        
        private void UpdateSlotDisplay(GameObject slotObj, InventoryItem item)
        {
            if (slotObj == null) return;
            
            Image icon = slotObj.transform.Find("Icon")?.GetComponent<Image>();
            TextMeshProUGUI quantityText = slotObj.transform.Find("Quantity")?.GetComponent<TextMeshProUGUI>();
            
            if (item?.ItemData != null)
            {
                // Show item
                if (icon != null)
                {
                    icon.sprite = item.ItemData.icon;
                    icon.color = item.ItemData.icon != null ? Color.white : Color.clear;
                }
                
                if (quantityText != null)
                {
                    quantityText.text = item.Quantity > 1 ? item.Quantity.ToString() : "";
                }
            }
            else
            {
                // Empty slot
                if (icon != null)
                {
                    icon.sprite = null;
                    icon.color = Color.clear;
                }
                
                if (quantityText != null)
                {
                    quantityText.text = "";
                }
            }
        }
        
        private void SetupInput()
        {
            // Create input action for inventory toggle
            toggleInventoryAction = new InputAction(binding: $"<Keyboard>/{toggleKey.ToString().ToLower()}");
            toggleInventoryAction.performed += OnToggleInventory;
            toggleInventoryAction.Enable();
        }

        private void OnToggleInventory(InputAction.CallbackContext context)
        {
            ToggleInventory();
        }

        private void HandleInput()
        {
            // Input is now handled by InputAction
        }
        
        public void ToggleInventory()
        {
            SetVisible(!isVisible);
        }
        
        public void SetVisible(bool visible)
        {
            isVisible = visible;
            
            if (inventoryPanel != null)
            {
                inventoryPanel.SetActive(visible);
            }
            
            // Always show hotbar
            if (hotbarPanel != null)
            {
                hotbarPanel.SetActive(true);
            }
            
            // Handle cursor
            Cursor.lockState = visible ? CursorLockMode.None : CursorLockMode.Locked;
            Cursor.visible = visible;
        }

        [ContextMenu("Create Test Items")]
        public void CreateTestItems()
        {
            if (playerInventory == null)
            {
                Debug.LogError("No PlayerInventory found!");
                return;
            }

            Debug.Log("Creating test items manually...");

            // Create test consumable
            ConsumableItemData bandage = ScriptableObject.CreateInstance<ConsumableItemData>();
            bandage.itemName = "Test Bandage";
            bandage.itemID = "test_bandage";
            bandage.description = "A test bandage";
            bandage.healthRestore = 25f;
            bandage.maxStackSize = 10;

            InventoryItem testItem = new InventoryItem(bandage, 3);
            bool success = playerInventory.PickupItem(testItem);

            Debug.Log($"Test item creation: {(success ? "SUCCESS" : "FAILED")}");

            if (success)
            {
                Debug.Log("Test item added to inventory!");
            }
        }

        private void OnDestroy()
        {
            if (toggleInventoryAction != null)
            {
                toggleInventoryAction.Dispose();
            }
        }
    }
}
