using UnityEngine;
using UnityEngine.UI;
using UnityEngine.InputSystem;
using TMPro;
using ZombieGame.Inventory;

namespace ZombieGame.UI
{
    /// <summary>
    /// Simple inventory UI that creates slots directly without prefabs
    /// </summary>
    public class SimpleInventoryUI : MonoBehaviour
    {
        [Header("UI Settings")]
        [SerializeField] private bool setupOnStart = false; // Disabled by default to prevent auto-setup
        [SerializeField] private KeyCode toggleKey = KeyCode.Tab;
        [SerializeField] private Vector2 slotSize = new Vector2(60, 60);
        [SerializeField] private float slotSpacing = 5f;
        [SerializeField] private int slotsPerRow = 6; // More slots per row for better space usage
        
        [Header("Colors")]
        [SerializeField] private Color panelColor = new Color(0.1f, 0.1f, 0.1f, 0.9f);
        [SerializeField] private Color slotColor = new Color(0.3f, 0.3f, 0.3f, 0.8f);
        [SerializeField] private Color selectedSlotColor = new Color(0.5f, 0.8f, 0.5f, 0.8f);
        [SerializeField] private Color hotbarColor = new Color(0.2f, 0.2f, 0.2f, 0.8f);
        
        // UI Elements
        private Canvas canvas;
        private GameObject inventoryPanel;
        private GameObject hotbarPanel;
        private GameObject[] backpackSlots;
        private GameObject[] hotbarSlots;
        private GameObject[] equipmentSlots;
        
        // Components
        private PlayerInventory playerInventory;
        private EquipmentManager equipmentManager;
        private ZombieGame.Player.PlayerStats playerStats;
        
        // State
        private bool isVisible = false;

        // Input Actions
        private InputAction toggleInventoryAction;
        
        private void Start()
        {
            if (setupOnStart)
            {
                SetupUI();
            }

            SetupInput();
        }
        
        private void Update()
        {
            HandleInput();
            UpdateSlots();
        }
        
        public void SetupUI()
        {
            Debug.Log("Setting up simple inventory UI...");
            
            // Find player components
            FindPlayerComponents();
            
            // Create or find canvas
            SetupCanvas();
            
            // Create UI panels
            CreateInventoryPanel();
            CreateHotbarPanel();
            
            // Create slots
            CreateBackpackSlots();
            CreateEquipmentSlots();
            CreateHotbarSlots();
            
            // Start hidden
            SetVisible(false);

            // Make sure we're the only UI system
            EnsureSingleUISystem();
            
            // Subscribe to inventory events for UI updates
            SubscribeToInventoryEvents();

            Debug.Log("Simple inventory UI setup complete!");

            // Debug: Check if we have items
            if (playerInventory != null)
            {
                Debug.Log($"Backpack has {playerInventory.Backpack.SlotCount} slots");
                Debug.Log($"Hotbar has {playerInventory.Hotbar.SlotCount} slots");

                // Check for items in backpack
                for (int i = 0; i < playerInventory.Backpack.SlotCount; i++)
                {
                    var slot = playerInventory.Backpack.GetSlot(i);
                    if (slot?.Item != null)
                    {
                        Debug.Log($"Backpack slot {i}: {slot.Item.ItemData.itemName} x{slot.Item.Quantity}");
                    }
                }
            }
        }
        
        private void FindPlayerComponents()
        {
            var player = FindFirstObjectByType<PlayerInventory>();
            if (player != null)
            {
                playerInventory = player;
                equipmentManager = player.GetComponent<EquipmentManager>();
                playerStats = player.GetComponent<ZombieGame.Player.PlayerStats>();
            }
            
            if (playerInventory == null)
            {
                Debug.LogError("SimpleInventoryUI: No PlayerInventory found in scene!");
            }
        }
        
        private void SetupCanvas()
        {
            canvas = FindFirstObjectByType<Canvas>();
            
            if (canvas == null)
            {
                // Create new canvas
                GameObject canvasObj = new GameObject("InventoryCanvas");
                canvas = canvasObj.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvas.sortingOrder = 100;
                
                CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
                scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
                scaler.referenceResolution = new Vector2(1920, 1080);
                
                canvasObj.AddComponent<GraphicRaycaster>();
            }
        }
        
        private void CreateInventoryPanel()
        {
            inventoryPanel = new GameObject("InventoryPanel");
            inventoryPanel.transform.SetParent(canvas.transform, false);

            RectTransform rect = inventoryPanel.AddComponent<RectTransform>();
            rect.anchorMin = new Vector2(0.5f, 0.5f);
            rect.anchorMax = new Vector2(0.5f, 0.5f);
            rect.sizeDelta = new Vector2(900, 650); // Slightly larger for better space usage
            rect.anchoredPosition = Vector2.zero;

            Image image = inventoryPanel.AddComponent<Image>();
            image.color = panelColor;

            // Add title
            CreateTitle(inventoryPanel, "Inventory", new Vector2(0, 310));
        }
        
        private void CreateHotbarPanel()
        {
            hotbarPanel = new GameObject("HotbarPanel");
            hotbarPanel.transform.SetParent(canvas.transform, false);
            
            RectTransform rect = hotbarPanel.AddComponent<RectTransform>();
            rect.anchorMin = new Vector2(0.5f, 0f);
            rect.anchorMax = new Vector2(0.5f, 0f);
            rect.sizeDelta = new Vector2(400, 70);
            rect.anchoredPosition = new Vector2(0, 50);
            
            Image image = hotbarPanel.AddComponent<Image>();
            image.color = hotbarColor;
        }
        
        private void CreateBackpackSlots()
        {
            if (playerInventory?.Backpack == null) return;
            
            int slotCount = playerInventory.Backpack.SlotCount;
            backpackSlots = new GameObject[slotCount];
            
            // Create container for backpack slots
            GameObject container = new GameObject("BackpackContainer");
            container.transform.SetParent(inventoryPanel.transform, false);

            RectTransform containerRect = container.AddComponent<RectTransform>();
            containerRect.anchorMin = new Vector2(0.4f, 0.1f); // Start closer to center
            containerRect.anchorMax = new Vector2(0.95f, 0.85f); // Use more space
            containerRect.offsetMin = Vector2.zero;
            containerRect.offsetMax = Vector2.zero;
            
            // Create slots in grid
            for (int i = 0; i < slotCount; i++)
            {
                backpackSlots[i] = CreateSlot(container, $"BackpackSlot_{i}", i, slotCount, true);
            }
            
            CreateTitle(inventoryPanel, "Backpack", new Vector2(150, 250));
        }
        
        private void CreateEquipmentSlots()
        {
            if (equipmentManager == null) return;

            var equipmentTypes = System.Enum.GetValues(typeof(EquipmentSlot));
            equipmentSlots = new GameObject[equipmentTypes.Length];

            // Create container for equipment slots
            GameObject container = new GameObject("EquipmentContainer");
            container.transform.SetParent(inventoryPanel.transform, false);

            RectTransform containerRect = container.AddComponent<RectTransform>();
            containerRect.anchorMin = new Vector2(0.05f, 0.1f);
            containerRect.anchorMax = new Vector2(0.35f, 0.85f); // Use more vertical space
            containerRect.offsetMin = Vector2.zero;
            containerRect.offsetMax = Vector2.zero;

            // Create equipment slots in a more compact grid
            int slotsPerColumn = 5; // 5 slots per column for better fit

            for (int i = 0; i < equipmentTypes.Length; i++)
            {
                EquipmentSlot slotType = (EquipmentSlot)equipmentTypes.GetValue(i);
                equipmentSlots[i] = CreateEquipmentSlot(container, slotType, i, slotsPerColumn);
            }

            CreateTitle(inventoryPanel, "Equipment", new Vector2(-250, 250));
        }
        
        private void CreateHotbarSlots()
        {
            if (playerInventory?.Hotbar == null) return;
            
            int slotCount = playerInventory.Hotbar.SlotCount;
            hotbarSlots = new GameObject[slotCount];
            
            for (int i = 0; i < slotCount; i++)
            {
                hotbarSlots[i] = CreateHotbarSlot(hotbarPanel, i);
            }
        }
        
        private GameObject CreateSlot(GameObject parent, string name, int index, int totalSlots, bool showGrid)
        {
            GameObject slot = new GameObject(name);
            slot.transform.SetParent(parent.transform, false);
            
            RectTransform rect = slot.AddComponent<RectTransform>();
            
            if (showGrid)
            {
                // Calculate grid position
                int row = index / slotsPerRow;
                int col = index % slotsPerRow;
                
                float x = col * (slotSize.x + slotSpacing) - (slotsPerRow - 1) * (slotSize.x + slotSpacing) * 0.5f;
                float y = -row * (slotSize.y + slotSpacing);
                
                rect.anchoredPosition = new Vector2(x, y);
            }
            
            rect.sizeDelta = slotSize;
            
            // Add background
            Image bg = slot.AddComponent<Image>();
            bg.color = slotColor;
            
            // Add item icon
            GameObject iconObj = new GameObject("Icon");
            iconObj.transform.SetParent(slot.transform, false);
            
            RectTransform iconRect = iconObj.AddComponent<RectTransform>();
            iconRect.anchorMin = Vector2.zero;
            iconRect.anchorMax = Vector2.one;
            iconRect.offsetMin = Vector2.one * 5;
            iconRect.offsetMax = Vector2.one * -5;
            
            Image icon = iconObj.AddComponent<Image>();
            icon.color = Color.clear; // Start transparent
            
            // Add quantity text
            GameObject textObj = new GameObject("Quantity");
            textObj.transform.SetParent(slot.transform, false);
            
            RectTransform textRect = textObj.AddComponent<RectTransform>();
            textRect.anchorMin = new Vector2(0.6f, 0f);
            textRect.anchorMax = new Vector2(1f, 0.4f);
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;
            
            TextMeshProUGUI text = textObj.AddComponent<TextMeshProUGUI>();
            text.text = "";
            text.fontSize = 12;
            text.color = Color.white;
            text.alignment = TextAlignmentOptions.BottomRight;
            
            return slot;
        }
        
        private GameObject CreateEquipmentSlot(GameObject parent, EquipmentSlot slotType, int index, int slotsPerColumn)
        {
            GameObject slot = CreateSlot(parent, $"Equipment_{slotType}", index, 10, false);

            RectTransform rect = slot.GetComponent<RectTransform>();

            // Calculate position in grid layout
            int column = index / slotsPerColumn;
            int row = index % slotsPerColumn;

            float x = column * (slotSize.x + slotSpacing + 60); // Reduced spacing for labels
            float y = -row * (slotSize.y + slotSpacing * 0.8f); // Tighter vertical spacing

            rect.anchoredPosition = new Vector2(x, y);

            // Add label with shorter text for better fit
            GameObject labelObj = new GameObject("Label");
            labelObj.transform.SetParent(slot.transform, false);

            RectTransform labelRect = labelObj.AddComponent<RectTransform>();
            labelRect.anchorMin = new Vector2(1.1f, 0f);
            labelRect.anchorMax = new Vector2(2.5f, 1f);
            labelRect.offsetMin = Vector2.zero;
            labelRect.offsetMax = Vector2.zero;

            TextMeshProUGUI label = labelObj.AddComponent<TextMeshProUGUI>();
            label.text = GetShortSlotName(slotType);
            label.fontSize = 9;
            label.color = Color.white;
            label.alignment = TextAlignmentOptions.MidlineLeft;

            return slot;
        }

        private string GetShortSlotName(EquipmentSlot slotType)
        {
            switch (slotType)
            {
                case EquipmentSlot.Head: return "Head";
                case EquipmentSlot.Chest: return "Chest";
                case EquipmentSlot.Legs: return "Legs";
                case EquipmentSlot.Feet: return "Feet";
                case EquipmentSlot.Hands: return "Hands";
                case EquipmentSlot.Back: return "Back";
                case EquipmentSlot.MainHand: return "Main";
                case EquipmentSlot.OffHand: return "Off";
                case EquipmentSlot.Ring: return "Ring";
                case EquipmentSlot.Necklace: return "Neck";
                default: return slotType.ToString();
            }
        }
        
        private GameObject CreateHotbarSlot(GameObject parent, int index)
        {
            GameObject slot = CreateSlot(parent, $"HotbarSlot_{index}", index, 8, false);
            
            RectTransform rect = slot.GetComponent<RectTransform>();
            float x = (index - 3.5f) * (slotSize.x + slotSpacing);
            rect.anchoredPosition = new Vector2(x, 0);
            
            // Add key number
            GameObject keyObj = new GameObject("KeyNumber");
            keyObj.transform.SetParent(slot.transform, false);
            
            RectTransform keyRect = keyObj.AddComponent<RectTransform>();
            keyRect.anchorMin = new Vector2(0f, 0.8f);
            keyRect.anchorMax = new Vector2(0.4f, 1f);
            keyRect.offsetMin = Vector2.zero;
            keyRect.offsetMax = Vector2.zero;
            
            TextMeshProUGUI keyText = keyObj.AddComponent<TextMeshProUGUI>();
            keyText.text = (index + 1).ToString();
            keyText.fontSize = 10;
            keyText.color = Color.yellow;
            keyText.alignment = TextAlignmentOptions.TopLeft;
            
            return slot;
        }
        
        private void CreateTitle(GameObject parent, string title, Vector2 position)
        {
            GameObject titleObj = new GameObject($"{title}Title");
            titleObj.transform.SetParent(parent.transform, false);
            
            RectTransform rect = titleObj.AddComponent<RectTransform>();
            rect.anchoredPosition = position;
            rect.sizeDelta = new Vector2(200, 30);
            
            TextMeshProUGUI text = titleObj.AddComponent<TextMeshProUGUI>();
            text.text = title;
            text.fontSize = 18;
            text.color = Color.white;
            text.alignment = TextAlignmentOptions.Center;
            text.fontStyle = FontStyles.Bold;
        }
        
        private void UpdateSlots()
        {
            if (playerInventory == null) return;
            
            // Update backpack slots
            UpdateBackpackSlots();
            
            // Update equipment slots
            UpdateEquipmentSlots();
            
            // Update hotbar slots
            UpdateHotbarSlots();
        }
        
        private void UpdateBackpackSlots()
        {
            if (backpackSlots == null || playerInventory?.Backpack == null) return;

            for (int i = 0; i < backpackSlots.Length; i++)
            {
                var slot = playerInventory.Backpack.GetSlot(i);
                UpdateSlotDisplay(backpackSlots[i], slot?.Item);
            }
        }
        
        private void UpdateEquipmentSlots()
        {
            if (equipmentSlots == null || equipmentManager == null) return;
            
            var equipmentTypes = System.Enum.GetValues(typeof(EquipmentSlot));
            for (int i = 0; i < equipmentSlots.Length && i < equipmentTypes.Length; i++)
            {
                EquipmentSlot slotType = (EquipmentSlot)equipmentTypes.GetValue(i);
                var item = equipmentManager.GetEquippedItem(slotType);
                UpdateSlotDisplay(equipmentSlots[i], item);
            }
        }
        
        private void UpdateHotbarSlots()
        {
            if (hotbarSlots == null || playerInventory.Hotbar == null) return;
            
            for (int i = 0; i < hotbarSlots.Length; i++)
            {
                var slot = playerInventory.Hotbar.GetSlot(i);
                UpdateSlotDisplay(hotbarSlots[i], slot?.Item);
                
                // Highlight selected slot
                Image bg = hotbarSlots[i].GetComponent<Image>();
                bg.color = (i == playerInventory.SelectedHotbarSlot) ? selectedSlotColor : slotColor;
            }
        }
        
        private void UpdateSlotDisplay(GameObject slotObj, InventoryItem item)
        {
            if (slotObj == null) return;
            
            Image icon = slotObj.transform.Find("Icon")?.GetComponent<Image>();
            TextMeshProUGUI quantityText = slotObj.transform.Find("Quantity")?.GetComponent<TextMeshProUGUI>();
            
            if (item?.ItemData != null)
            {
                // Show item
                if (icon != null)
                {
                    icon.sprite = item.ItemData.icon;
                    icon.color = item.ItemData.icon != null ? Color.white : Color.clear;
                }
                
                if (quantityText != null)
                {
                    quantityText.text = item.Quantity > 1 ? item.Quantity.ToString() : "";
                }
            }
            else
            {
                // Empty slot
                if (icon != null)
                {
                    icon.sprite = null;
                    icon.color = Color.clear;
                }
                
                if (quantityText != null)
                {
                    quantityText.text = "";
                }
            }
        }
        
        private void SetupInput()
        {
            // Create input action for inventory toggle
            toggleInventoryAction = new InputAction(binding: $"<Keyboard>/{toggleKey.ToString().ToLower()}");
            toggleInventoryAction.performed += OnToggleInventory;
            toggleInventoryAction.Enable();
        }

        private void OnToggleInventory(InputAction.CallbackContext context)
        {
            ToggleInventory();
        }

        private void HandleInput()
        {
            // Input is now handled by InputAction
        }
        
        public void ToggleInventory()
        {
            SetVisible(!isVisible);
        }
        
        public void SetVisible(bool visible)
        {
            isVisible = visible;

            if (inventoryPanel != null)
            {
                inventoryPanel.SetActive(visible);
            }

            // Always show hotbar
            if (hotbarPanel != null)
            {
                hotbarPanel.SetActive(true);
            }

            // Handle cursor
            Cursor.lockState = visible ? CursorLockMode.None : CursorLockMode.Locked;
            Cursor.visible = visible;

            Debug.Log($"Inventory visibility set to: {visible}");
        }

        private void SubscribeToInventoryEvents()
        {
            if (playerInventory != null)
            {
                // Subscribe to inventory change events
                playerInventory.OnInventoryChanged += OnInventoryChanged;
                playerInventory.OnInventoryToggled += OnInventoryToggled;
                playerInventory.OnHotbarSelectionChanged += OnHotbarSelectionChanged;
                playerInventory.OnItemPickedUp += OnItemPickedUp;

                Debug.Log("Subscribed to inventory events for UI updates");
            }
            else
            {
                Debug.LogWarning("Cannot subscribe to inventory events - playerInventory is null");
            }
        }

        private void OnInventoryChanged(PlayerInventory inventory)
        {
            Debug.Log("Inventory changed - updating UI");
            // Force immediate UI update
            UpdateSlots();
        }

        private void OnInventoryToggled(bool isOpen)
        {
            SetVisible(isOpen);
        }

        private void OnHotbarSelectionChanged(int slotIndex)
        {
            // Update hotbar selection highlighting
            UpdateHotbarSlots();
        }

        private void OnItemPickedUp(InventoryItem item)
        {
            Debug.Log($"Item picked up: {item.ItemData.itemName} - forcing UI update");
            // Force immediate UI update when item is picked up
            UpdateSlots();
        }

        private void EnsureSingleUISystem()
        {
            // Find all SimpleInventoryUI instances
            var allUIs = FindObjectsByType<SimpleInventoryUI>(FindObjectsSortMode.None);

            // If there are multiple, destroy the others and keep this one
            if (allUIs.Length > 1)
            {
                Debug.Log($"Found {allUIs.Length} SimpleInventoryUI instances, removing duplicates...");

                for (int i = 0; i < allUIs.Length; i++)
                {
                    if (allUIs[i] != this)
                    {
                        Debug.Log($"Destroying duplicate SimpleInventoryUI: {allUIs[i].name}");
                        Destroy(allUIs[i].gameObject);
                    }
                }
            }

            // Also check for other inventory UI systems
            var otherUIs = FindObjectsByType<InventoryUI>(FindObjectsSortMode.None);
            if (otherUIs.Length > 0)
            {
                Debug.Log($"Found {otherUIs.Length} other InventoryUI instances, disabling them...");

                foreach (var ui in otherUIs)
                {
                    ui.enabled = false;
                    Debug.Log($"Disabled InventoryUI: {ui.name}");
                }
            }
        }

        [ContextMenu("Create Test Items")]
        public void CreateTestItems()
        {
            if (playerInventory == null)
            {
                Debug.LogError("No PlayerInventory found!");
                return;
            }

            Debug.Log("Creating test items manually...");

            // Create test consumable
            ConsumableItemData bandage = ScriptableObject.CreateInstance<ConsumableItemData>();
            bandage.itemName = "Test Bandage";
            bandage.itemID = "test_bandage";
            bandage.description = "A test bandage";
            bandage.healthRestore = 25f;
            bandage.maxStackSize = 10;

            InventoryItem testItem = new InventoryItem(bandage, 3);
            bool success = playerInventory.PickupItem(testItem);

            Debug.Log($"Test item creation: {(success ? "SUCCESS" : "FAILED")}");

            if (success)
            {
                Debug.Log("Test item added to inventory!");
            }
        }

        private void OnDestroy()
        {
            // Unsubscribe from inventory events
            if (playerInventory != null)
            {
                playerInventory.OnInventoryChanged -= OnInventoryChanged;
                playerInventory.OnInventoryToggled -= OnInventoryToggled;
                playerInventory.OnHotbarSelectionChanged -= OnHotbarSelectionChanged;
                playerInventory.OnItemPickedUp -= OnItemPickedUp;
            }

            if (toggleInventoryAction != null)
            {
                toggleInventoryAction.Dispose();
            }
        }
    }
}
