using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;
using ZombieGame.Inventory;

namespace ZombieGame.UI
{
    /// <summary>
    /// UI component for equipment slots
    /// </summary>
    public class EquipmentSlotUI : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointer<PERSON><PERSON><PERSON><PERSON><PERSON>, IPointer<PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerExitH<PERSON>ler, IDropHandler
    {
        [Header("UI Elements")]
        [SerializeField] private Image itemIcon;
        [SerializeField] private Image backgroundImage;
        [SerializeField] private Image slotTypeIcon;
        [SerializeField] private TextMeshProUGUI slotLabel;
        
        [Header("Visual Settings")]
        [SerializeField] private Color normalColor = Color.white;
        [SerializeField] private Color hoverColor = Color.yellow;
        [SerializeField] private Color emptySlotColor = new Color(1f, 1f, 1f, 0.3f);
        [SerializeField] private Color invalidDropColor = Color.red;
        
        [Header("Slot Type Icons")]
        [SerializeField] private Sprite headIcon;
        [SerializeField] private Sprite chestIcon;
        [SerializeField] private Sprite legsIcon;
        [SerializeField] private Sprite feetIcon;
        [SerializeField] private Sprite handsIcon;
        [SerializeField] private Sprite backIcon;
        [SerializeField] private Sprite mainHandIcon;
        [SerializeField] private Sprite offHandIcon;
        [SerializeField] private Sprite ringIcon;
        [SerializeField] private Sprite necklaceIcon;
        
        // References
        private EquipmentManager equipmentManager;
        private EquipmentSlot equipmentSlot;
        
        // State
        private bool isHovered = false;
        private bool canAcceptDrop = false;
        
        public EquipmentSlot EquipmentSlot => equipmentSlot;
        public EquipmentManager EquipmentManager => equipmentManager;
        
        private void Awake()
        {
            SetupComponents();
        }
        
        private void SetupComponents()
        {
            // Setup default UI elements if not assigned
            if (itemIcon == null)
            {
                itemIcon = transform.Find("ItemIcon")?.GetComponent<Image>();
            }
            
            if (backgroundImage == null)
            {
                backgroundImage = GetComponent<Image>();
            }
            
            if (slotTypeIcon == null)
            {
                slotTypeIcon = transform.Find("SlotTypeIcon")?.GetComponent<Image>();
            }
            
            if (slotLabel == null)
            {
                slotLabel = GetComponentInChildren<TextMeshProUGUI>();
            }
        }
        
        public void Initialize(EquipmentManager equipmentManager, EquipmentSlot equipmentSlot)
        {
            this.equipmentManager = equipmentManager;
            this.equipmentSlot = equipmentSlot;
            
            // Subscribe to equipment changes
            if (equipmentManager != null)
            {
                equipmentManager.OnItemEquipped += OnItemEquipped;
                equipmentManager.OnItemUnequipped += OnItemUnequipped;
            }
            
            // Setup slot appearance
            SetupSlotAppearance();
            UpdateDisplay();
        }
        
        private void SetupSlotAppearance()
        {
            // Set slot type icon
            if (slotTypeIcon != null)
            {
                slotTypeIcon.sprite = GetSlotTypeIcon(equipmentSlot);
                slotTypeIcon.gameObject.SetActive(slotTypeIcon.sprite != null);
            }
            
            // Set slot label
            if (slotLabel != null)
            {
                slotLabel.text = GetSlotDisplayName(equipmentSlot);
            }
        }
        
        private Sprite GetSlotTypeIcon(EquipmentSlot slot)
        {
            switch (slot)
            {
                case EquipmentSlot.Head: return headIcon;
                case EquipmentSlot.Chest: return chestIcon;
                case EquipmentSlot.Legs: return legsIcon;
                case EquipmentSlot.Feet: return feetIcon;
                case EquipmentSlot.Hands: return handsIcon;
                case EquipmentSlot.Back: return backIcon;
                case EquipmentSlot.MainHand: return mainHandIcon;
                case EquipmentSlot.OffHand: return offHandIcon;
                case EquipmentSlot.Ring: return ringIcon;
                case EquipmentSlot.Necklace: return necklaceIcon;
                default: return null;
            }
        }
        
        private string GetSlotDisplayName(EquipmentSlot slot)
        {
            switch (slot)
            {
                case EquipmentSlot.Head: return "Head";
                case EquipmentSlot.Chest: return "Chest";
                case EquipmentSlot.Legs: return "Legs";
                case EquipmentSlot.Feet: return "Feet";
                case EquipmentSlot.Hands: return "Hands";
                case EquipmentSlot.Back: return "Back";
                case EquipmentSlot.MainHand: return "Main Hand";
                case EquipmentSlot.OffHand: return "Off Hand";
                case EquipmentSlot.Ring: return "Ring";
                case EquipmentSlot.Necklace: return "Necklace";
                default: return slot.ToString();
            }
        }
        
        public void UpdateDisplay()
        {
            if (equipmentManager == null) return;
            
            var equippedItem = equipmentManager.GetEquippedItem(equipmentSlot);
            
            // Update item icon
            if (itemIcon != null)
            {
                if (equippedItem?.ItemData?.icon != null)
                {
                    itemIcon.sprite = equippedItem.ItemData.icon;
                    itemIcon.color = Color.white;
                    itemIcon.gameObject.SetActive(true);
                    
                    // Hide slot type icon when item is equipped
                    if (slotTypeIcon != null)
                    {
                        slotTypeIcon.gameObject.SetActive(false);
                    }
                }
                else
                {
                    itemIcon.gameObject.SetActive(false);
                    
                    // Show slot type icon when empty
                    if (slotTypeIcon != null)
                    {
                        slotTypeIcon.gameObject.SetActive(true);
                    }
                }
            }
            
            UpdateVisualState();
        }
        
        private void UpdateVisualState()
        {
            if (backgroundImage == null) return;
            
            Color targetColor = normalColor;
            
            if (!canAcceptDrop && isHovered)
            {
                targetColor = invalidDropColor;
            }
            else if (isHovered)
            {
                targetColor = hoverColor;
            }
            else if (!equipmentManager.IsSlotEquipped(equipmentSlot))
            {
                targetColor = emptySlotColor;
            }
            
            backgroundImage.color = targetColor;
        }
        
        // Event handlers
        private void OnItemEquipped(EquipmentSlot slot, InventoryItem item)
        {
            if (slot == equipmentSlot)
            {
                UpdateDisplay();
            }
        }
        
        private void OnItemUnequipped(EquipmentSlot slot, InventoryItem item)
        {
            if (slot == equipmentSlot)
            {
                UpdateDisplay();
            }
        }
        
        // UI Event Handlers
        public void OnPointerClick(PointerEventData eventData)
        {
            if (eventData.button == PointerEventData.InputButton.Left)
            {
                HandleLeftClick();
            }
            else if (eventData.button == PointerEventData.InputButton.Right)
            {
                HandleRightClick();
            }
        }
        
        public void OnPointerEnter(PointerEventData eventData)
        {
            isHovered = true;
            
            // Check if we can accept the dragged item
            var draggedSlot = eventData.pointerDrag?.GetComponent<InventorySlotUI>();
            if (draggedSlot != null)
            {
                canAcceptDrop = CanAcceptItem(draggedSlot.InventorySlot?.Item);
            }
            
            UpdateVisualState();
            
            // Show tooltip
            var equippedItem = equipmentManager?.GetEquippedItem(equipmentSlot);
            if (equippedItem != null)
            {
                ShowTooltip(equippedItem);
            }
        }
        
        public void OnPointerExit(PointerEventData eventData)
        {
            isHovered = false;
            canAcceptDrop = false;
            UpdateVisualState();
            
            // Hide tooltip
            HideTooltip();
        }
        
        public void OnDrop(PointerEventData eventData)
        {
            var draggedSlot = eventData.pointerDrag?.GetComponent<InventorySlotUI>();
            if (draggedSlot?.InventorySlot?.Item != null)
            {
                HandleItemDrop(draggedSlot.InventorySlot.Item, draggedSlot);
            }
        }
        
        private void HandleLeftClick()
        {
            // Unequip item if equipped
            if (equipmentManager.IsSlotEquipped(equipmentSlot))
            {
                equipmentManager.UnequipItem(equipmentSlot);
            }
        }
        
        private void HandleRightClick()
        {
            // TODO: Implement context menu for equipment
            Debug.Log($"Right clicked equipment slot: {equipmentSlot}");
        }
        
        private void HandleItemDrop(InventoryItem item, InventorySlotUI sourceSlot)
        {
            if (!CanAcceptItem(item)) return;
            
            // Remove item from source slot
            var removedItem = sourceSlot.InventorySlot.RemoveItem();
            if (removedItem != null)
            {
                // Try to equip the item
                bool success = equipmentManager.EquipItem(removedItem);
                if (!success)
                {
                    // Failed to equip, put item back
                    sourceSlot.InventorySlot.AddItem(removedItem);
                }
            }
        }
        
        private bool CanAcceptItem(InventoryItem item)
        {
            if (item?.ItemData == null) return false;
            
            if (!(item.ItemData is EquipmentItemData equipmentData)) return false;
            
            return equipmentData.equipmentSlot == equipmentSlot;
        }
        
        private void ShowTooltip(InventoryItem item)
        {
            // TODO: Implement tooltip system
            Debug.Log($"Show equipment tooltip for {item.ItemData.itemName}");
        }
        
        private void HideTooltip()
        {
            // TODO: Implement tooltip hiding
        }
        
        private void OnDestroy()
        {
            if (equipmentManager != null)
            {
                equipmentManager.OnItemEquipped -= OnItemEquipped;
                equipmentManager.OnItemUnequipped -= OnItemUnequipped;
            }
        }
    }
}
