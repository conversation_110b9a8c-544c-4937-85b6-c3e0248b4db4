using System;
using System.Collections.Generic;
using UnityEngine;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Network-serializable inventory data structures for multiplayer support
    /// </summary>
    
    [Serializable]
    public struct NetworkInventoryItem
    {
        public string itemID;
        public int quantity;
        public float durability;
        public ItemCondition condition;
        
        public NetworkInventoryItem(InventoryItem item)
        {
            itemID = item?.ItemID ?? "";
            quantity = item?.Quantity ?? 0;
            durability = item?.Durability ?? 100f;
            condition = item?.Condition ?? ItemCondition.Perfect;
        }
        
        public InventoryItem ToInventoryItem()
        {
            if (string.IsNullOrEmpty(itemID)) return null;
            return new InventoryItem(itemID, quantity, durability, condition);
        }
    }
    
    [Serializable]
    public struct NetworkInventorySlot
    {
        public NetworkInventoryItem item;
        public bool isLocked;
        public SlotType slotType;
        
        public NetworkInventorySlot(InventorySlot slot)
        {
            item = slot?.Item != null ? new NetworkInventoryItem(slot.Item) : new NetworkInventoryItem();
            isLocked = slot?.IsLocked ?? false;
            slotType = slot?.SlotType ?? SlotType.General;
        }
    }
    
    [Serializable]
    public struct NetworkInventoryContainer
    {
        public string containerName;
        public NetworkInventorySlot[] slots;
        public float maxWeight;
        public ContainerType containerType;
        
        public NetworkInventoryContainer(InventoryContainer container)
        {
            containerName = container?.ContainerName ?? "";
            maxWeight = container?.MaxWeight ?? -1f;
            containerType = container?.ContainerType ?? ContainerType.General;
            
            if (container?.Slots != null)
            {
                slots = new NetworkInventorySlot[container.Slots.Length];
                for (int i = 0; i < container.Slots.Length; i++)
                {
                    slots[i] = new NetworkInventorySlot(container.Slots[i]);
                }
            }
            else
            {
                slots = new NetworkInventorySlot[0];
            }
        }
    }
    
    [Serializable]
    public struct NetworkPlayerInventory
    {
        public NetworkInventoryContainer backpack;
        public NetworkInventoryContainer hotbar;
        public NetworkInventoryContainer equipment;
        public int selectedHotbarSlot;
        public bool isInventoryOpen;
        
        public NetworkPlayerInventory(PlayerInventory playerInventory)
        {
            backpack = new NetworkInventoryContainer(playerInventory?.Backpack);
            hotbar = new NetworkInventoryContainer(playerInventory?.Hotbar);
            equipment = new NetworkInventoryContainer(playerInventory?.Equipment);
            selectedHotbarSlot = playerInventory?.SelectedHotbarSlot ?? 0;
            isInventoryOpen = playerInventory?.IsInventoryOpen ?? false;
        }
    }
    
    /// <summary>
    /// Network events for inventory synchronization
    /// </summary>
    [Serializable]
    public struct InventoryNetworkEvent
    {
        public InventoryEventType eventType;
        public int containerIndex;
        public int slotIndex;
        public NetworkInventoryItem item;
        public int quantity;
        public float timestamp;
        
        public InventoryNetworkEvent(InventoryEventType type, int container, int slot, InventoryItem item = null, int qty = 0)
        {
            eventType = type;
            containerIndex = container;
            slotIndex = slot;
            this.item = item != null ? new NetworkInventoryItem(item) : new NetworkInventoryItem();
            quantity = qty;
            timestamp = Time.time;
        }
    }
    
    public enum InventoryEventType
    {
        ItemAdded,
        ItemRemoved,
        ItemMoved,
        ItemUsed,
        SlotSwapped,
        HotbarSelectionChanged,
        InventoryToggled
    }
    
    /// <summary>
    /// Interface for network inventory synchronization
    /// </summary>
    public interface INetworkInventory
    {
        void OnInventoryEventReceived(InventoryNetworkEvent networkEvent);
        void SendInventoryEvent(InventoryNetworkEvent networkEvent);
        NetworkPlayerInventory GetNetworkInventoryState();
        void ApplyNetworkInventoryState(NetworkPlayerInventory networkState);
    }
    
    /// <summary>
    /// Network inventory manager for multiplayer synchronization
    /// </summary>
    public class NetworkInventoryManager : MonoBehaviour, INetworkInventory
    {
        [Header("Network Settings")]
        [SerializeField] private bool isServer = false;
        [SerializeField] private bool enableClientPrediction = true;
        [SerializeField] private float syncInterval = 0.1f;
        
        [Header("Debug")]
        [SerializeField] private bool logNetworkEvents = false;
        
        private PlayerInventory playerInventory;
        private Queue<InventoryNetworkEvent> pendingEvents = new Queue<InventoryNetworkEvent>();
        private float lastSyncTime = 0f;
        
        // Events for network integration
        public event Action<InventoryNetworkEvent> OnNetworkEventSent;
        public event Action<NetworkPlayerInventory> OnInventoryStateSent;
        
        private void Awake()
        {
            playerInventory = GetComponent<PlayerInventory>();
            if (playerInventory == null)
            {
                Debug.LogError("NetworkInventoryManager: No PlayerInventory component found!");
            }
        }
        
        private void Start()
        {
            if (playerInventory != null)
            {
                // Subscribe to inventory events
                playerInventory.OnInventoryChanged += OnLocalInventoryChanged;
                playerInventory.OnHotbarSelectionChanged += OnLocalHotbarSelectionChanged;
                playerInventory.OnInventoryToggled += OnLocalInventoryToggled;
            }
        }
        
        private void Update()
        {
            // Process pending network events
            ProcessPendingEvents();
            
            // Periodic sync for server
            if (isServer && Time.time - lastSyncTime > syncInterval)
            {
                SyncInventoryState();
                lastSyncTime = Time.time;
            }
        }
        
        private void OnLocalInventoryChanged(PlayerInventory inventory)
        {
            if (enableClientPrediction || isServer)
            {
                // Create and send network event
                var networkEvent = new InventoryNetworkEvent(
                    InventoryEventType.ItemAdded, 0, 0, null, 0
                );
                
                SendInventoryEvent(networkEvent);
            }
        }
        
        private void OnLocalHotbarSelectionChanged(int slotIndex)
        {
            var networkEvent = new InventoryNetworkEvent(
                InventoryEventType.HotbarSelectionChanged, 1, slotIndex, null, 0
            );
            
            SendInventoryEvent(networkEvent);
        }
        
        private void OnLocalInventoryToggled(bool isOpen)
        {
            var networkEvent = new InventoryNetworkEvent(
                InventoryEventType.InventoryToggled, 0, isOpen ? 1 : 0, null, 0
            );
            
            SendInventoryEvent(networkEvent);
        }
        
        public void OnInventoryEventReceived(InventoryNetworkEvent networkEvent)
        {
            if (logNetworkEvents)
            {
                Debug.Log($"Received network event: {networkEvent.eventType}");
            }
            
            // Queue the event for processing
            pendingEvents.Enqueue(networkEvent);
        }
        
        public void SendInventoryEvent(InventoryNetworkEvent networkEvent)
        {
            if (logNetworkEvents)
            {
                Debug.Log($"Sending network event: {networkEvent.eventType}");
            }
            
            OnNetworkEventSent?.Invoke(networkEvent);
        }
        
        public NetworkPlayerInventory GetNetworkInventoryState()
        {
            return new NetworkPlayerInventory(playerInventory);
        }
        
        public void ApplyNetworkInventoryState(NetworkPlayerInventory networkState)
        {
            if (playerInventory == null) return;
            
            // TODO: Apply the network state to the local inventory
            // This would involve reconstructing the inventory containers from network data
            
            if (logNetworkEvents)
            {
                Debug.Log("Applied network inventory state");
            }
        }
        
        private void ProcessPendingEvents()
        {
            while (pendingEvents.Count > 0)
            {
                var networkEvent = pendingEvents.Dequeue();
                ApplyNetworkEvent(networkEvent);
            }
        }
        
        private void ApplyNetworkEvent(InventoryNetworkEvent networkEvent)
        {
            if (playerInventory == null) return;
            
            switch (networkEvent.eventType)
            {
                case InventoryEventType.HotbarSelectionChanged:
                    playerInventory.SelectHotbarSlot(networkEvent.slotIndex);
                    break;
                    
                case InventoryEventType.InventoryToggled:
                    // Only apply if different from current state
                    bool shouldBeOpen = networkEvent.slotIndex == 1;
                    if (playerInventory.IsInventoryOpen != shouldBeOpen)
                    {
                        playerInventory.ToggleInventory();
                    }
                    break;
                    
                // TODO: Implement other event types
                default:
                    if (logNetworkEvents)
                    {
                        Debug.Log($"Unhandled network event: {networkEvent.eventType}");
                    }
                    break;
            }
        }
        
        private void SyncInventoryState()
        {
            var networkState = GetNetworkInventoryState();
            OnInventoryStateSent?.Invoke(networkState);
        }
        
        // Public methods for network integration
        public void SetServerMode(bool isServerMode)
        {
            isServer = isServerMode;
        }
        
        public void SetClientPrediction(bool enabled)
        {
            enableClientPrediction = enabled;
        }
        
        private void OnDestroy()
        {
            if (playerInventory != null)
            {
                playerInventory.OnInventoryChanged -= OnLocalInventoryChanged;
                playerInventory.OnHotbarSelectionChanged -= OnLocalHotbarSelectionChanged;
                playerInventory.OnInventoryToggled -= OnLocalInventoryToggled;
            }
        }
    }
}
