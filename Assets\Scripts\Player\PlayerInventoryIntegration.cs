using UnityEngine;
using ZombieGame.Inventory;

namespace ZombieGame.Player
{
    /// <summary>
    /// Integrates the inventory system with the existing player controller
    /// </summary>
    public class PlayerInventoryIntegration : MonoBehaviour
    {
        [Header("Inventory Integration")]
        [SerializeField] private bool autoSetupInventory = true;
        [SerializeField] private bool createTestItems = false;
        
        [Header("References")]
        [SerializeField] private PlayerMovement playerMovement;
        [SerializeField] private CameraController cameraController;
        
        // Inventory components
        private PlayerInventory playerInventory;
        private InventoryInputController inputController;
        private InteractionSystem interactionSystem;
        
        private void Awake()
        {
            // Get existing player components
            if (playerMovement == null)
                playerMovement = GetComponent<PlayerMovement>();
            
            if (cameraController == null)
                cameraController = GetComponent<CameraController>();
            
            if (autoSetupInventory)
            {
                SetupInventorySystem();
            }
        }
        
        private void Start()
        {
            if (createTestItems)
            {
                CreateTestItems();
            }
        }
        
        private void SetupInventorySystem()
        {
            Debug.Log("Setting up inventory system on player...");
            
            // Add PlayerInventory if not present
            playerInventory = GetComponent<PlayerInventory>();
            if (playerInventory == null)
            {
                playerInventory = gameObject.AddComponent<PlayerInventory>();
                Debug.Log("Added PlayerInventory component");
            }
            
            // Add InventoryInputController if not present
            inputController = GetComponent<InventoryInputController>();
            if (inputController == null)
            {
                inputController = gameObject.AddComponent<InventoryInputController>();
                Debug.Log("Added InventoryInputController component");
            }
            
            // Add InteractionSystem if not present
            interactionSystem = GetComponent<InteractionSystem>();
            if (interactionSystem == null)
            {
                interactionSystem = gameObject.AddComponent<InteractionSystem>();
                Debug.Log("Added InteractionSystem component");
            }
            
            // Subscribe to inventory events
            if (playerInventory != null)
            {
                playerInventory.OnInventoryToggled += OnInventoryToggled;
                playerInventory.OnItemPickedUp += OnItemPickedUp;
                playerInventory.OnItemUsed += OnItemUsed;
            }
            
            Debug.Log("Inventory system setup complete!");
        }
        
        private void OnInventoryToggled(bool isOpen)
        {
            // Disable player movement when inventory is open
            if (playerMovement != null)
            {
                // You might want to add a method to PlayerMovement to disable input
                Debug.Log($"Inventory {(isOpen ? "opened" : "closed")} - consider disabling movement input");
            }
            
            // Show/hide cursor when inventory is open
            Cursor.lockState = isOpen ? CursorLockMode.None : CursorLockMode.Locked;
            Cursor.visible = isOpen;
        }
        
        private void OnItemPickedUp(InventoryItem item)
        {
            Debug.Log($"Picked up: {item.ItemData.itemName} x{item.Quantity}");
            
            // You could add visual/audio feedback here
            // For example, show a pickup notification UI
        }
        
        private void OnItemUsed(InventoryItem item)
        {
            Debug.Log($"Used: {item.ItemData.itemName}");
            
            // Handle item usage effects
            if (item.ItemData is ConsumableItemData consumable)
            {
                ApplyConsumableEffects(consumable);
            }
        }
        
        private void ApplyConsumableEffects(ConsumableItemData consumable)
        {
            // TODO: Apply effects to player stats
            // For now, just log the effects
            if (consumable.healthRestore > 0)
            {
                Debug.Log($"Restored {consumable.healthRestore} health");
                // playerHealth.Heal(consumable.healthRestore);
            }
            
            if (consumable.hungerRestore > 0)
            {
                Debug.Log($"Restored {consumable.hungerRestore} hunger");
                // playerHunger.Feed(consumable.hungerRestore);
            }
            
            if (consumable.thirstRestore > 0)
            {
                Debug.Log($"Restored {consumable.thirstRestore} thirst");
                // playerThirst.Drink(consumable.thirstRestore);
            }
        }
        
        private void CreateTestItems()
        {
            if (playerInventory == null) return;
            
            Debug.Log("Creating test items...");
            
            // Create some test items directly (since we don't have ItemDatabase set up yet)
            CreateTestConsumable("bandage", "Bandage", 25f, 0f, 0f);
            CreateTestConsumable("water_bottle", "Water Bottle", 0f, 0f, 50f);
            CreateTestConsumable("canned_food", "Canned Food", 0f, 30f, 0f);
            CreateTestMaterial("metal_scrap", "Metal Scrap", MaterialCategory.Scrap);
            CreateTestWeapon("knife", "Combat Knife", WeaponType.Melee, 35f);
        }
        
        private void CreateTestConsumable(string id, string name, float health, float hunger, float thirst)
        {
            // Create a temporary consumable item data
            ConsumableItemData itemData = ScriptableObject.CreateInstance<ConsumableItemData>();
            itemData.itemID = id;
            itemData.itemName = name;
            itemData.description = $"A {name.ToLower()} for survival.";
            itemData.healthRestore = health;
            itemData.hungerRestore = hunger;
            itemData.thirstRestore = thirst;
            itemData.maxStackSize = 10;
            
            InventoryItem item = new InventoryItem(itemData, Random.Range(1, 4));
            playerInventory.PickupItem(item);
            
            Debug.Log($"Created test item: {name}");
        }
        
        private void CreateTestMaterial(string id, string name, MaterialCategory category)
        {
            MaterialItemData itemData = ScriptableObject.CreateInstance<MaterialItemData>();
            itemData.itemID = id;
            itemData.itemName = name;
            itemData.description = $"Useful {name.ToLower()} for crafting.";
            itemData.category = category;
            itemData.maxStackSize = 50;
            
            InventoryItem item = new InventoryItem(itemData, Random.Range(5, 15));
            playerInventory.PickupItem(item);
            
            Debug.Log($"Created test material: {name}");
        }
        
        private void CreateTestWeapon(string id, string name, WeaponType weaponType, float damage)
        {
            WeaponItemData itemData = ScriptableObject.CreateInstance<WeaponItemData>();
            itemData.itemID = id;
            itemData.itemName = name;
            itemData.description = $"A {name.ToLower()} for combat.";
            itemData.weaponType = weaponType;
            itemData.damage = damage;
            itemData.attackSpeed = 1.5f;
            itemData.range = weaponType == WeaponType.Melee ? 2f : 10f;
            
            InventoryItem item = new InventoryItem(itemData, 1);
            playerInventory.PickupItem(item);
            
            Debug.Log($"Created test weapon: {name}");
        }
        
        /// <summary>
        /// Creates a test world item near the player
        /// </summary>
        [ContextMenu("Spawn Test World Item")]
        public void SpawnTestWorldItem()
        {
            Vector3 spawnPosition = transform.position + transform.forward * 3f + Vector3.up;
            
            // Create a test item
            CreateTestConsumable("test_bandage", "Test Bandage", 25f, 0f, 0f);
            var testItem = new InventoryItem(ScriptableObject.CreateInstance<ConsumableItemData>(), 1);
            
            // Spawn it in the world
            WorldItem.SpawnWorldItem(testItem, spawnPosition);
            Debug.Log($"Spawned test world item at {spawnPosition}");
        }
        
        // Public accessors for other scripts
        public PlayerInventory PlayerInventory => playerInventory;
        public InventoryInputController InputController => inputController;
        public InteractionSystem InteractionSystem => interactionSystem;
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (playerInventory != null)
            {
                playerInventory.OnInventoryToggled -= OnInventoryToggled;
                playerInventory.OnItemPickedUp -= OnItemPickedUp;
                playerInventory.OnItemUsed -= OnItemUsed;
            }
        }
    }
}
