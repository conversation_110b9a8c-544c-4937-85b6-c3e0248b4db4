using UnityEngine;
using ZombieGame.Inventory;

namespace ZombieGame.Testing
{
    /// <summary>
    /// Test setup script for quickly testing the inventory system
    /// </summary>
    public class InventoryTestSetup : MonoBehaviour
    {
        [Header("Test Setup")]
        [SerializeField] private bool setupOnStart = true;
        [SerializeField] private bool spawnTestItems = true;
        [SerializeField] private int numberOfTestItems = 5;
        [SerializeField] private float spawnRadius = 10f;
        
        [Header("Test Items")]
        [SerializeField] private TestItemDefinition[] testItems = {
            new TestItemDefinition("Bandage", ItemType.Consumable, 25f, 0f, 0f),
            new TestItemDefinition("Water Bottle", ItemType.Consumable, 0f, 0f, 50f),
            new TestItemDefinition("Canned Food", ItemType.Consumable, 0f, 30f, 0f),
            new TestItemDefinition("Metal Scrap", ItemType.Material, 0f, 0f, 0f),
            new TestItemDefinition("Combat Knife", ItemType.Weapon, 0f, 0f, 0f)
        };
        
        private void Start()
        {
            if (setupOnStart)
            {
                SetupTestEnvironment();
            }
        }
        
        [ContextMenu("Setup Test Environment")]
        public void SetupTestEnvironment()
        {
            Debug.Log("Setting up inventory test environment...");
            
            // Find or create player
            GameObject player = FindPlayer();
            if (player == null)
            {
                Debug.LogWarning("No player found in scene. Please add a player with PlayerInventoryIntegration component.");
                return;
            }
            
            // Ensure player has inventory system
            var inventoryIntegration = player.GetComponent<ZombieGame.Player.PlayerInventoryIntegration>();
            if (inventoryIntegration == null)
            {
                inventoryIntegration = player.AddComponent<ZombieGame.Player.PlayerInventoryIntegration>();
                Debug.Log("Added PlayerInventoryIntegration to player");
            }
            
            // Create test items in world
            if (spawnTestItems)
            {
                SpawnTestWorldItems(player.transform.position);
            }
            
            // Add some items to player inventory
            AddTestItemsToPlayer(player);
            
            Debug.Log("Test environment setup complete!");
            Debug.Log("Controls: Tab=Inventory, 1-8=Hotbar, E=Pickup, Q=Drop, Mouse Wheel=Scroll Hotbar");
        }
        
        private GameObject FindPlayer()
        {
            // Try to find player by tag first
            GameObject player = GameObject.FindWithTag("Player");
            if (player != null) return player;
            
            // Try to find by component
            var playerMovement = FindFirstObjectByType<PlayerMovement>();
            if (playerMovement != null) return playerMovement.gameObject;
            
            // Try to find by name
            player = GameObject.Find("Player");
            if (player != null) return player;
            
            return null;
        }
        
        private void SpawnTestWorldItems(Vector3 playerPosition)
        {
            Debug.Log($"Spawning {numberOfTestItems} test world items...");
            
            for (int i = 0; i < numberOfTestItems; i++)
            {
                // Pick a random test item
                var testItemDef = testItems[Random.Range(0, testItems.Length)];
                
                // Create the item data
                var itemData = CreateTestItemData(testItemDef);
                var inventoryItem = new InventoryItem(itemData, Random.Range(1, 4));
                
                // Calculate spawn position
                Vector2 randomCircle = Random.insideUnitCircle * spawnRadius;
                Vector3 spawnPosition = playerPosition + new Vector3(randomCircle.x, 2f, randomCircle.y);
                
                // Spawn the world item
                var worldItem = WorldItem.SpawnWorldItem(inventoryItem, spawnPosition);
                
                // Add some random velocity for scatter effect
                if (worldItem != null)
                {
                    var rb = worldItem.GetComponent<Rigidbody>();
                    if (rb != null)
                    {
                        Vector3 randomVelocity = new Vector3(
                            Random.Range(-2f, 2f),
                            Random.Range(0f, 3f),
                            Random.Range(-2f, 2f)
                        );
                        rb.linearVelocity = randomVelocity;
                    }
                }
                
                Debug.Log($"Spawned {testItemDef.name} at {spawnPosition}");
            }
        }
        
        private void AddTestItemsToPlayer(GameObject player)
        {
            var playerInventory = player.GetComponent<PlayerInventory>();
            if (playerInventory == null)
            {
                Debug.LogWarning("Player doesn't have PlayerInventory component");
                return;
            }
            
            Debug.Log("Adding test items to player inventory...");
            
            // Add one of each test item to player
            foreach (var testItemDef in testItems)
            {
                var itemData = CreateTestItemData(testItemDef);
                var inventoryItem = new InventoryItem(itemData, 1);
                
                bool success = playerInventory.PickupItem(inventoryItem);
                if (success)
                {
                    Debug.Log($"Added {testItemDef.name} to player inventory");
                }
                else
                {
                    Debug.LogWarning($"Failed to add {testItemDef.name} to player inventory");
                }
            }
        }
        
        private ItemData CreateTestItemData(TestItemDefinition testDef)
        {
            switch (testDef.itemType)
            {
                case ItemType.Consumable:
                    return CreateTestConsumable(testDef);
                case ItemType.Material:
                    return CreateTestMaterial(testDef);
                case ItemType.Weapon:
                    return CreateTestWeapon(testDef);
                default:
                    return CreateTestGenericItem(testDef);
            }
        }
        
        private ConsumableItemData CreateTestConsumable(TestItemDefinition testDef)
        {
            var itemData = ScriptableObject.CreateInstance<ConsumableItemData>();
            itemData.itemName = testDef.name;
            itemData.itemID = testDef.name.ToLower().Replace(" ", "_");
            itemData.description = $"A test {testDef.name.ToLower()} for survival.";
            itemData.healthRestore = testDef.healthRestore;
            itemData.hungerRestore = testDef.hungerRestore;
            itemData.thirstRestore = testDef.thirstRestore;
            itemData.maxStackSize = 10;
            itemData.weight = 0.5f;
            return itemData;
        }
        
        private MaterialItemData CreateTestMaterial(TestItemDefinition testDef)
        {
            var itemData = ScriptableObject.CreateInstance<MaterialItemData>();
            itemData.itemName = testDef.name;
            itemData.itemID = testDef.name.ToLower().Replace(" ", "_");
            itemData.description = $"Useful {testDef.name.ToLower()} for crafting.";
            itemData.category = MaterialCategory.Scrap;
            itemData.maxStackSize = 50;
            itemData.weight = 0.2f;
            return itemData;
        }
        
        private WeaponItemData CreateTestWeapon(TestItemDefinition testDef)
        {
            var itemData = ScriptableObject.CreateInstance<WeaponItemData>();
            itemData.itemName = testDef.name;
            itemData.itemID = testDef.name.ToLower().Replace(" ", "_");
            itemData.description = $"A {testDef.name.ToLower()} for combat.";
            itemData.weaponType = WeaponType.Melee;
            itemData.damage = 35f;
            itemData.attackSpeed = 1.5f;
            itemData.range = 2f;
            itemData.maxStackSize = 1;
            itemData.weight = 2f;
            return itemData;
        }
        
        private ItemData CreateTestGenericItem(TestItemDefinition testDef)
        {
            var itemData = ScriptableObject.CreateInstance<ItemData>();
            itemData.itemName = testDef.name;
            itemData.itemID = testDef.name.ToLower().Replace(" ", "_");
            itemData.description = $"A test {testDef.name.ToLower()}.";
            itemData.itemType = testDef.itemType;
            itemData.maxStackSize = 10;
            itemData.weight = 1f;
            return itemData;
        }
        
        [ContextMenu("Clear All World Items")]
        public void ClearAllWorldItems()
        {
            var worldItems = FindObjectsByType<WorldItem>(FindObjectsSortMode.None);
            foreach (var worldItem in worldItems)
            {
                DestroyImmediate(worldItem.gameObject);
            }
            Debug.Log($"Cleared {worldItems.Length} world items");
        }
        
        [ContextMenu("Spawn Single Test Item")]
        public void SpawnSingleTestItem()
        {
            var player = FindPlayer();
            if (player != null)
            {
                var testItemDef = testItems[Random.Range(0, testItems.Length)];
                var itemData = CreateTestItemData(testItemDef);
                var inventoryItem = new InventoryItem(itemData, 1);
                
                Vector3 spawnPosition = player.transform.position + player.transform.forward * 3f + Vector3.up;
                WorldItem.SpawnWorldItem(inventoryItem, spawnPosition);
                
                Debug.Log($"Spawned {testItemDef.name} in front of player");
            }
        }
    }
    
    [System.Serializable]
    public class TestItemDefinition
    {
        public string name;
        public ItemType itemType;
        public float healthRestore;
        public float hungerRestore;
        public float thirstRestore;
        
        public TestItemDefinition(string name, ItemType type, float health, float hunger, float thirst)
        {
            this.name = name;
            this.itemType = type;
            this.healthRestore = health;
            this.hungerRestore = hunger;
            this.thirstRestore = thirst;
        }
    }
}
