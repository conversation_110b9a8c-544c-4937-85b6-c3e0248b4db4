using System.Collections.Generic;
using UnityEngine;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Singleton database that manages all item data in the game
    /// </summary>
    [CreateAssetMenu(fileName = "ItemDatabase", menuName = "Inventory/Item Database")]
    public class ItemDatabase : ScriptableObject
    {
        [SerializeField] private List<ItemData> allItems = new List<ItemData>();
        
        private static ItemDatabase _instance;
        private static Dictionary<string, ItemData> _itemLookup;
        
        public static ItemDatabase Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = Resources.Load<ItemDatabase>("ItemDatabase");
                    if (_instance == null)
                    {
                        Debug.LogError("ItemDatabase not found in Resources folder! Please create one.");
                    }
                    else
                    {
                        _instance.BuildLookupTable();
                    }
                }
                return _instance;
            }
        }
        
        private void BuildLookupTable()
        {
            _itemLookup = new Dictionary<string, ItemData>();
            
            foreach (var item in allItems)
            {
                if (item != null && !string.IsNullOrEmpty(item.itemID))
                {
                    if (_itemLookup.ContainsKey(item.itemID))
                    {
                        Debug.LogWarning($"Duplicate item ID found: {item.itemID}. Item: {item.name}");
                    }
                    else
                    {
                        _itemLookup[item.itemID] = item;
                    }
                }
            }
            
            Debug.Log($"ItemDatabase loaded with {_itemLookup.Count} items.");
        }
        
        /// <summary>
        /// Gets item data by ID
        /// </summary>
        public static ItemData GetItemData(string itemID)
        {
            if (Instance == null) return null;
            
            _itemLookup.TryGetValue(itemID, out ItemData itemData);
            if (itemData == null)
            {
                Debug.LogWarning($"Item with ID '{itemID}' not found in database.");
            }
            
            return itemData;
        }
        
        /// <summary>
        /// Gets all items of a specific type
        /// </summary>
        public static List<ItemData> GetItemsByType(ItemType itemType)
        {
            if (Instance == null) return new List<ItemData>();
            
            List<ItemData> result = new List<ItemData>();
            foreach (var item in Instance.allItems)
            {
                if (item != null && item.itemType == itemType)
                {
                    result.Add(item);
                }
            }
            return result;
        }
        
        /// <summary>
        /// Gets all items of a specific rarity
        /// </summary>
        public static List<ItemData> GetItemsByRarity(ItemRarity rarity)
        {
            if (Instance == null) return new List<ItemData>();
            
            List<ItemData> result = new List<ItemData>();
            foreach (var item in Instance.allItems)
            {
                if (item != null && item.rarity == rarity)
                {
                    result.Add(item);
                }
            }
            return result;
        }
        
        /// <summary>
        /// Creates a new inventory item instance
        /// </summary>
        public static InventoryItem CreateItem(string itemID, int quantity = 1)
        {
            ItemData itemData = GetItemData(itemID);
            if (itemData == null) return null;
            
            return new InventoryItem(itemData, quantity);
        }
        
        /// <summary>
        /// Validates all items in the database
        /// </summary>
        [ContextMenu("Validate Database")]
        public void ValidateDatabase()
        {
            HashSet<string> usedIDs = new HashSet<string>();
            List<string> issues = new List<string>();
            
            for (int i = 0; i < allItems.Count; i++)
            {
                var item = allItems[i];
                if (item == null)
                {
                    issues.Add($"Null item at index {i}");
                    continue;
                }
                
                if (string.IsNullOrEmpty(item.itemID))
                {
                    issues.Add($"Item '{item.name}' has empty ID");
                }
                else if (usedIDs.Contains(item.itemID))
                {
                    issues.Add($"Duplicate ID '{item.itemID}' found in item '{item.name}'");
                }
                else
                {
                    usedIDs.Add(item.itemID);
                }
                
                if (item.icon == null)
                {
                    issues.Add($"Item '{item.name}' missing icon");
                }
                
                if (item.maxStackSize <= 0)
                {
                    issues.Add($"Item '{item.name}' has invalid max stack size");
                }
            }
            
            if (issues.Count > 0)
            {
                Debug.LogWarning($"ItemDatabase validation found {issues.Count} issues:\n" + string.Join("\n", issues));
            }
            else
            {
                Debug.Log("ItemDatabase validation passed!");
            }
        }
        
        public List<ItemData> AllItems => allItems;
        public int ItemCount => allItems.Count;
    }
}
