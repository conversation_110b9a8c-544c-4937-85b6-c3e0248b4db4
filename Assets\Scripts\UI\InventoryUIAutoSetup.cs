using UnityEngine;

namespace ZombieGame.UI
{
    /// <summary>
    /// Auto-setup script to ensure inventory UI is initialized when the game starts
    /// </summary>
    public class InventoryUIAutoSetup : MonoBehaviour
    {
        [Header("Auto Setup")]
        [SerializeField] private bool setupOnStart = true;
        [SerializeField] private float setupDelay = 0.5f;
        
        private void Start()
        {
            if (setupOnStart)
            {
                Invoke(nameof(SetupInventoryUI), setupDelay);
            }
        }
        
        private void SetupInventoryUI()
        {
            Debug.Log("Auto-setting up inventory UI...");
            
            // Find or create SimpleInventoryUI
            var simpleInventoryUI = FindFirstObjectByType<SimpleInventoryUI>();
            if (simpleInventoryUI != null)
            {
                simpleInventoryUI.SetupUI();
                Debug.Log("SimpleInventoryUI setup completed");
            }
            else
            {
                Debug.LogWarning("No SimpleInventoryUI found in scene");
            }
            
            // Also set up InventoryUISetup if it exists
            var inventoryUISetup = FindFirstObjectByType<InventoryUISetup>();
            if (inventoryUISetup != null)
            {
                inventoryUISetup.SetupInventoryUI();
                Debug.Log("InventoryUISetup completed");
            }
        }
    }
}
