using System;
using UnityEngine;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Represents a single slot in an inventory container
    /// </summary>
    [Serializable]
    public class InventorySlot
    {
        [SerializeField] private InventoryItem item;
        [SerializeField] private bool isLocked;
        [SerializeField] private SlotType slotType;
        [SerializeField] private string[] allowedItemTypes; // For restricted slots
        
        public InventoryItem Item => item;
        public bool IsEmpty => item == null || item.IsEmpty;
        public bool IsLocked => isLocked;
        public SlotType SlotType => slotType;
        
        // Events
        public event Action<InventorySlot> OnSlotChanged;
        
        public InventorySlot(SlotType slotType = SlotType.General)
        {
            this.slotType = slotType;
            this.isLocked = false;
            this.allowedItemTypes = new string[0];
        }
        
        public InventorySlot(SlotType slotType, string[] allowedItemTypes)
        {
            this.slotType = slotType;
            this.allowedItemTypes = allowedItemTypes ?? new string[0];
            this.isLocked = false;
        }
        
        /// <summary>
        /// Attempts to add an item to this slot
        /// </summary>
        /// <param name="newItem">Item to add</param>
        /// <returns>Remaining item that couldn't be added (null if all was added)</returns>
        public InventoryItem AddItem(InventoryItem newItem)
        {
            if (newItem == null || isLocked) return newItem;
            
            // Check if item type is allowed in this slot
            if (!CanAcceptItem(newItem))
            {
                return newItem;
            }
            
            // If slot is empty, place the item
            if (IsEmpty)
            {
                item = newItem;
                OnSlotChanged?.Invoke(this);
                return null;
            }
            
            // If items can stack, try to stack them
            if (item.CanStackWith(newItem))
            {
                int overflow = item.AddQuantity(newItem.Quantity);
                if (overflow > 0)
                {
                    // Return the overflow as a new item
                    var overflowItem = new InventoryItem(newItem.ItemData, overflow);
                    OnSlotChanged?.Invoke(this);
                    return overflowItem;
                }
                else
                {
                    OnSlotChanged?.Invoke(this);
                    return null; // All items were added
                }
            }
            
            // Items can't stack, return the new item
            return newItem;
        }
        
        /// <summary>
        /// Removes a specific quantity from this slot
        /// </summary>
        /// <param name="quantity">Amount to remove</param>
        /// <returns>The removed item</returns>
        public InventoryItem RemoveItem(int quantity = -1)
        {
            if (IsEmpty || isLocked) return null;
            
            // If quantity is -1 or greater than current quantity, remove all
            if (quantity == -1 || quantity >= item.Quantity)
            {
                var removedItem = item;
                item = null;
                OnSlotChanged?.Invoke(this);
                return removedItem;
            }
            
            // Split the stack
            var splitItem = item.Split(quantity);
            OnSlotChanged?.Invoke(this);
            return splitItem;
        }
        
        /// <summary>
        /// Swaps items between this slot and another
        /// </summary>
        public bool SwapWith(InventorySlot otherSlot)
        {
            if (otherSlot == null || isLocked || otherSlot.isLocked) return false;
            
            // Check if items can be placed in each other's slots
            bool thisCanAcceptOther = otherSlot.IsEmpty || CanAcceptItem(otherSlot.item);
            bool otherCanAcceptThis = IsEmpty || otherSlot.CanAcceptItem(item);
            
            if (!thisCanAcceptOther || !otherCanAcceptThis) return false;
            
            // Perform the swap
            var tempItem = item;
            item = otherSlot.item;
            otherSlot.item = tempItem;
            
            OnSlotChanged?.Invoke(this);
            otherSlot.OnSlotChanged?.Invoke(otherSlot);
            
            return true;
        }
        
        /// <summary>
        /// Checks if this slot can accept a specific item
        /// </summary>
        public bool CanAcceptItem(InventoryItem itemToCheck)
        {
            if (itemToCheck == null || itemToCheck.ItemData == null) return false;
            
            // Check slot type restrictions
            if (allowedItemTypes.Length > 0)
            {
                bool isAllowed = false;
                foreach (string allowedType in allowedItemTypes)
                {
                    if (itemToCheck.ItemData.itemType.ToString() == allowedType ||
                        itemToCheck.ItemData.itemID == allowedType)
                    {
                        isAllowed = true;
                        break;
                    }
                }
                if (!isAllowed) return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// Locks or unlocks this slot
        /// </summary>
        public void SetLocked(bool locked)
        {
            isLocked = locked;
            OnSlotChanged?.Invoke(this);
        }
        
        /// <summary>
        /// Clears the slot completely
        /// </summary>
        public void Clear()
        {
            if (!isLocked)
            {
                item = null;
                OnSlotChanged?.Invoke(this);
            }
        }
        
        /// <summary>
        /// Gets the total weight of items in this slot
        /// </summary>
        public float GetWeight()
        {
            if (IsEmpty) return 0f;
            return item.ItemData.weight * item.Quantity;
        }
    }
    
    public enum SlotType
    {
        General,        // Can hold any item
        Weapon,         // Only weapons
        Equipment,      // Only equipment/armor
        Consumable,     // Only consumables
        Material,       // Only materials
        Hotbar,         // Hotbar slots (usually general but with quick access)
        Restricted      // Custom restrictions via allowedItemTypes
    }
}
