{"name": "com.unity.ide.vscode", "displayName": "Visual Studio Code Editor", "description": "Code editor integration for supporting Visual Studio Code as code editor for unity. Adds support for generating csproj files for intellisense purposes, auto discovery of installations, etc.", "version": "1.2.1", "unity": "2019.2", "unityRelease": "0a12", "dependencies": {}, "relatedPackages": {"com.unity.ide.vscode.tests": "1.2.1"}, "upmCi": {"footprint": "4dde6af44bb4725574d8975254aa3161e72090cc"}, "repository": {"type": "git", "url": "https://github.cds.internal.unity3d.com/unity/com.unity.ide.vscode.git", "revision": "4395bd811a72b875607adb1001398d0325947baa"}, "_fingerprint": "27b0f2094353d294f320e6ac2f8618dd4a274aad"}