using System;
using System.Collections.Generic;
using UnityEngine;
using ZombieGame.Player;

namespace ZombieGame.Inventory
{
    /// <summary>
    /// Manages equipped items and their effects on the player
    /// </summary>
    public class EquipmentManager : MonoBehaviour
    {
        [Header("Equipment Slots")]
        [SerializeField] private Transform[] equipmentSlots = new Transform[10]; // One for each EquipmentSlot enum
        
        [Header("Audio")]
        [SerializeField] private AudioSource audioSource;
        
        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = false;
        
        // Equipment storage
        private Dictionary<EquipmentSlot, InventoryItem> equippedItems = new Dictionary<EquipmentSlot, InventoryItem>();
        private Dictionary<EquipmentSlot, GameObject> equippedModels = new Dictionary<EquipmentSlot, GameObject>();
        
        // Components
        private PlayerStats playerStats;
        private PlayerInventory playerInventory;
        private Animator playerAnimator;
        
        // Events
        public event Action<EquipmentSlot, InventoryItem> OnItemEquipped;
        public event Action<EquipmentSlot, InventoryItem> OnItemUnequipped;
        public event Action OnEquipmentChanged;
        
        // Properties
        public Dictionary<EquipmentSlot, InventoryItem> EquippedItems => new Dictionary<EquipmentSlot, InventoryItem>(equippedItems);
        
        private void Awake()
        {
            SetupComponents();
            InitializeEquipmentSlots();
        }
        
        private void SetupComponents()
        {
            playerStats = GetComponent<PlayerStats>();
            if (playerStats == null)
            {
                playerStats = gameObject.AddComponent<PlayerStats>();
                Debug.Log("Added PlayerStats component to player");
            }
            
            playerInventory = GetComponent<PlayerInventory>();
            if (playerInventory == null)
            {
                Debug.LogWarning("EquipmentManager: No PlayerInventory found!");
            }
            
            playerAnimator = GetComponent<Animator>();
            
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
                audioSource.spatialBlend = 0f; // 2D sound
            }
        }
        
        private void InitializeEquipmentSlots()
        {
            // Initialize all equipment slots as empty
            foreach (EquipmentSlot slot in Enum.GetValues(typeof(EquipmentSlot)))
            {
                equippedItems[slot] = null;
                equippedModels[slot] = null;
            }
            
            // If equipment slots transforms aren't assigned, try to find them
            if (equipmentSlots[0] == null)
            {
                SetupDefaultEquipmentSlots();
            }
        }
        
        private void SetupDefaultEquipmentSlots()
        {
            // Create default equipment slot transforms if they don't exist
            for (int i = 0; i < equipmentSlots.Length; i++)
            {
                if (equipmentSlots[i] == null)
                {
                    EquipmentSlot slotType = (EquipmentSlot)i;
                    GameObject slotObj = new GameObject($"EquipmentSlot_{slotType}");
                    slotObj.transform.SetParent(transform);
                    slotObj.transform.localPosition = GetDefaultSlotPosition(slotType);
                    equipmentSlots[i] = slotObj.transform;
                }
            }
        }
        
        private Vector3 GetDefaultSlotPosition(EquipmentSlot slot)
        {
            // Default positions for equipment slots relative to player
            switch (slot)
            {
                case EquipmentSlot.Head: return new Vector3(0, 1.8f, 0);
                case EquipmentSlot.Chest: return new Vector3(0, 1.2f, 0);
                case EquipmentSlot.Legs: return new Vector3(0, 0.6f, 0);
                case EquipmentSlot.Feet: return new Vector3(0, 0.1f, 0);
                case EquipmentSlot.Hands: return new Vector3(0, 1.0f, 0.3f);
                case EquipmentSlot.Back: return new Vector3(0, 1.2f, -0.3f);
                case EquipmentSlot.MainHand: return new Vector3(0.3f, 1.0f, 0.2f);
                case EquipmentSlot.OffHand: return new Vector3(-0.3f, 1.0f, 0.2f);
                case EquipmentSlot.Ring: return new Vector3(0.2f, 1.0f, 0.1f);
                case EquipmentSlot.Necklace: return new Vector3(0, 1.4f, 0.1f);
                default: return Vector3.zero;
            }
        }
        
        /// <summary>
        /// Attempts to equip an item
        /// </summary>
        public bool EquipItem(InventoryItem item)
        {
            if (item?.ItemData == null) return false;
            
            if (!(item.ItemData is EquipmentItemData equipmentData))
            {
                Debug.LogWarning($"Cannot equip {item.ItemData.itemName}: Not an equipment item");
                return false;
            }
            
            EquipmentSlot targetSlot = equipmentData.equipmentSlot;
            
            // Check if slot is already occupied
            if (equippedItems[targetSlot] != null)
            {
                // Try to unequip current item first
                if (!UnequipItem(targetSlot))
                {
                    Debug.LogWarning($"Cannot equip {item.ItemData.itemName}: Failed to unequip current item");
                    return false;
                }
            }
            
            // Equip the new item
            equippedItems[targetSlot] = item;
            
            // Apply stat modifiers
            ApplyEquipmentStats(equipmentData, true);
            
            // Create visual representation
            CreateEquipmentModel(targetSlot, equipmentData);
            
            // Play equip sound
            PlayEquipSound(equipmentData.equipSound);
            
            // Fire events
            OnItemEquipped?.Invoke(targetSlot, item);
            OnEquipmentChanged?.Invoke();
            
            if (showDebugInfo)
            {
                Debug.Log($"Equipped {item.ItemData.itemName} in {targetSlot} slot");
            }
            
            return true;
        }
        
        /// <summary>
        /// Attempts to unequip an item from a specific slot
        /// </summary>
        public bool UnequipItem(EquipmentSlot slot)
        {
            if (!equippedItems.ContainsKey(slot) || equippedItems[slot] == null)
            {
                return false; // Nothing equipped in this slot
            }
            
            InventoryItem item = equippedItems[slot];
            EquipmentItemData equipmentData = item.ItemData as EquipmentItemData;
            
            // Try to add item back to inventory
            if (playerInventory != null)
            {
                var remainingItem = playerInventory.Backpack.AddItem(item);
                if (remainingItem != null)
                {
                    Debug.LogWarning($"Cannot unequip {item.ItemData.itemName}: Inventory full");
                    return false;
                }
            }
            
            // Remove from equipped items
            equippedItems[slot] = null;
            
            // Remove stat modifiers
            if (equipmentData != null)
            {
                ApplyEquipmentStats(equipmentData, false);
            }
            
            // Remove visual representation
            RemoveEquipmentModel(slot);
            
            // Play unequip sound
            if (equipmentData != null)
            {
                PlayEquipSound(equipmentData.unequipSound);
            }
            
            // Fire events
            OnItemUnequipped?.Invoke(slot, item);
            OnEquipmentChanged?.Invoke();
            
            if (showDebugInfo)
            {
                Debug.Log($"Unequipped {item.ItemData.itemName} from {slot} slot");
            }
            
            return true;
        }
        
        /// <summary>
        /// Gets the item equipped in a specific slot
        /// </summary>
        public InventoryItem GetEquippedItem(EquipmentSlot slot)
        {
            equippedItems.TryGetValue(slot, out InventoryItem item);
            return item;
        }
        
        /// <summary>
        /// Checks if a specific slot has an item equipped
        /// </summary>
        public bool IsSlotEquipped(EquipmentSlot slot)
        {
            return equippedItems.ContainsKey(slot) && equippedItems[slot] != null;
        }
        
        /// <summary>
        /// Gets all equipped items
        /// </summary>
        public List<InventoryItem> GetAllEquippedItems()
        {
            var items = new List<InventoryItem>();
            foreach (var kvp in equippedItems)
            {
                if (kvp.Value != null)
                {
                    items.Add(kvp.Value);
                }
            }
            return items;
        }
        
        private void ApplyEquipmentStats(EquipmentItemData equipmentData, bool apply)
        {
            if (playerStats == null || equipmentData.statModifiers == null) return;
            
            foreach (var modifier in equipmentData.statModifiers)
            {
                if (apply)
                {
                    playerStats.AddModifier(modifier);
                }
                else
                {
                    playerStats.RemoveModifier(modifier);
                }
            }
        }
        
        private void CreateEquipmentModel(EquipmentSlot slot, EquipmentItemData equipmentData)
        {
            if (equipmentData.equipmentPrefab == null) return;
            
            Transform slotTransform = equipmentSlots[(int)slot];
            if (slotTransform == null) return;
            
            // Remove existing model
            RemoveEquipmentModel(slot);
            
            // Instantiate new model
            GameObject model = Instantiate(equipmentData.equipmentPrefab, slotTransform);
            model.transform.localPosition = Vector3.zero;
            model.transform.localRotation = Quaternion.identity;
            
            equippedModels[slot] = model;
            
            // Apply animator override if available
            if (equipmentData.animatorOverride != null && playerAnimator != null)
            {
                // TODO: Implement animator override system
            }
        }
        
        private void RemoveEquipmentModel(EquipmentSlot slot)
        {
            if (equippedModels.ContainsKey(slot) && equippedModels[slot] != null)
            {
                DestroyImmediate(equippedModels[slot]);
                equippedModels[slot] = null;
            }
        }
        
        private void PlayEquipSound(AudioClip clip)
        {
            if (audioSource != null && clip != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        
        /// <summary>
        /// Gets the total armor value from all equipped items
        /// </summary>
        public float GetTotalArmor()
        {
            float totalArmor = 0f;
            foreach (var kvp in equippedItems)
            {
                if (kvp.Value?.ItemData is EquipmentItemData equipmentData)
                {
                    totalArmor += equipmentData.armorValue;
                }
            }
            return totalArmor;
        }
        
        /// <summary>
        /// Gets the total damage reduction from all equipped items
        /// </summary>
        public float GetTotalDamageReduction()
        {
            float totalReduction = 0f;
            foreach (var kvp in equippedItems)
            {
                if (kvp.Value?.ItemData is EquipmentItemData equipmentData)
                {
                    totalReduction += equipmentData.damageReduction;
                }
            }
            return Mathf.Clamp01(totalReduction); // Cap at 100%
        }
        
        /// <summary>
        /// Checks if the player has a specific special property from equipment
        /// </summary>
        public bool HasSpecialProperty(SpecialProperty property)
        {
            foreach (var kvp in equippedItems)
            {
                if (kvp.Value?.ItemData is EquipmentItemData equipmentData)
                {
                    if (equipmentData.HasSpecialProperty(property))
                    {
                        return true;
                    }
                }
            }
            return false;
        }
    }
}
